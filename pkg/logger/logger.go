package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Config 日志配置
type Config struct {
	Level     string `mapstructure:"level"`
	Format    string `mapstructure:"format"`
	Output    string `mapstructure:"output"`
	FilePath  string `mapstructure:"file_path"`
	LoggerID  string `mapstructure:"logger_id"`   // 用于生成日志文件名的ID
	IsMainLog bool   `mapstructure:"is_main_log"` // 是否为主进程日志
}

var (
	// Logger 全局日志实例
	Logger *zap.Logger
	// Sugar 语法糖日志实例
	Sugar *zap.SugaredLogger
	// processID 进程ID
	processID string
)

// Init 初始化日志
func Init(cfg *Config) error {
	// 获取进程ID
	processID = strconv.Itoa(os.Getpid())

	// 设置日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 设置日志编码器
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 设置日志输出
	var writeSyncer zapcore.WriteSyncer
	if cfg.Output == "file" && cfg.FilePath != "" {
		// 生成日志文件路径
		logFilePath := generateLogFilePath(cfg)
		fmt.Println("日志 文件路径", logFilePath)

		// 确保日志目录存在
		dir := filepath.Dir(logFilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}

		file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}
		writeSyncer = zapcore.AddSync(file)
	} else {
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	// 创建日志核心
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 创建日志实例，添加进程ID字段
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1)).With(zap.String("pid", processID))
	Sugar = Logger.Sugar()

	return nil
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// Debugf 调试日志（格式化）
func Debugf(format string, args ...interface{}) {
	Sugar.Debugf(format, args...)
}

// Infof 信息日志（格式化）
func Infof(format string, args ...interface{}) {
	Sugar.Infof(format, args...)
}

// Warnf 警告日志（格式化）
func Warnf(format string, args ...interface{}) {
	Sugar.Warnf(format, args...)
}

// Errorf 错误日志（格式化）
func Errorf(format string, args ...interface{}) {
	Sugar.Errorf(format, args...)
}

// Fatalf 致命错误日志（格式化）
func Fatalf(format string, args ...interface{}) {
	Sugar.Fatalf(format, args...)
}

// RecoverWithStack 在recover()后打印堆栈信息
func RecoverWithStack(err interface{}) {
	Logger.Error("程序异常",
		zap.Any("error", err),
		zap.Stack("stack"),
	)
}

// RecoverWithStackf 在recover()后打印堆栈信息（带格式化信息）
func RecoverWithStackf(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	Logger.Error(msg,
		zap.Stack("stack"),
	)
}

// Sync 同步日志
func Sync() {
	Logger.Sync()
}

// generateLogFilePath 生成日志文件路径
// 如果配置了ID，则按照 {base_dir}/{date}/{id}.log 格式生成
// 如果没有配置ID，则使用原来的文件路径
func generateLogFilePath(cfg *Config) string {
	if cfg.LoggerID == "" {
		// 没有配置ID，使用原来的文件路径
		return cfg.FilePath
	}
	// 获取当前日期
	now := time.Now()
	// 获取基础目录
	logDir := filepath.Dir(cfg.FilePath)

	logFileName := cfg.LoggerID
	if !cfg.IsMainLog {
		dateStr := now.Format("2006-01-02")
		// 生成新的文件路径: {base_dir}/{date}/{id}.log
		logDir = filepath.Join(logDir, dateStr)
		timeStr := now.Format("150405")
		logFileName = fmt.Sprintf("%s_%s", timeStr, logFileName)
	} else {
		dateTimeStr := now.Format("20060102")
		logFileName = fmt.Sprintf("%s_%s", logFileName, dateTimeStr)
	}

	return filepath.Join(logDir, logFileName+".log")
}
