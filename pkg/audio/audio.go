package audio

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"math"
	"sort"

	"github.com/hajimehoshi/go-mp3"
	"github.com/mewkiz/flac"
	"github.com/youpy/go-wav"
)

// Audio constants
const (
	MP3Format  = "mp3"
	WAVFormat  = "wav"
	AACFormat  = "aac"
	FLACFormat = "flac"
	PCMFormat  = "pcm"
)

// AudioInfo represents information about an audio file
type AudioInfo struct {
	Format       string
	SampleRate   int
	Channels     int
	BitDepth     int
	Duration     float64 // in seconds
	FileSize     int     // in bytes
	TotalSamples int64
}

// AudioPCMFormat represents PCM audio format parameters
type AudioPCMFormat struct {
	SampleRate int
	BitDepth   int
	Channels   int
}

// AudioProcessor handles audio processing operations
type AudioProcessor struct {
	data []byte
	info AudioInfo
}

// NewAudioProcessor creates a new AudioProcessor from byte slice
func NewAudioProcessor(data []byte) (*AudioProcessor, error) {
	if len(data) == 0 {
		return nil, errors.New("音频数据为空")
	}

	ap := &AudioProcessor{
		data: data,
	}

	// Detect format and fill info
	if err := ap.detectFormat(); err != nil {
		return nil, err
	}

	return ap, nil
}

func ProcessAudioToPCM(audioBytes []byte, dstFormat AudioPCMFormat) ([]byte, error) {
	if audioBytes == nil {
		return nil, fmt.Errorf("媒体数据为空,跳过转换PCM")
	}

	processor, err := NewAudioProcessor(audioBytes)
	if err != nil {
		return nil, fmt.Errorf("创建音频处理器失败: %w", err)
	}

	pcmData, audioPCMFormat, err := processor.DecodeToPCM()
	if err != nil {
		return nil, fmt.Errorf("解码PCM失败: %w", err)
	}

	resampledPCM, err := ResamplePCM(pcmData, audioPCMFormat, dstFormat)
	if err != nil {
		return nil, fmt.Errorf("重采样PCM失败: %w", err)
	}

	return resampledPCM, nil
}

// GetInfo returns audio information
func (ap *AudioProcessor) GetInfo() AudioInfo {
	return ap.info
}

// detectFormat analyzes the data to determine audio format and extract metadata
func (ap *AudioProcessor) detectFormat() error {
	// Check for WAV signature (RIFF)
	if len(ap.data) > 12 && string(ap.data[0:4]) == "RIFF" && string(ap.data[8:12]) == "WAVE" {
		return ap.parseWavInfo()
	}

	// Check for MP3 header (ID3 or sync word)
	if len(ap.data) > 3 && string(ap.data[0:3]) == "ID3" || ap.isMP3SyncWord(ap.data) {
		return ap.parseMP3Info()
	}

	// Check for AAC ADTS marker
	if len(ap.data) > 2 && ap.data[0] == 0xFF && (ap.data[1]&0xF0) == 0xF0 {
		return ap.parseAACInfo()
	}

	// Check for FLAC signature "fLaC"
	if len(ap.data) > 4 && string(ap.data[0:4]) == "fLaC" {
		return ap.parseFLACInfo()
	}

	return fmt.Errorf("未知或不支持的音频格式")
}

// isMP3SyncWord checks if the byte slice starts with an MP3 sync word
func (ap *AudioProcessor) isMP3SyncWord(data []byte) bool {
	// Search for sync word (11 bits set)
	for i := 0; i < len(data)-1; i++ {
		if data[i] == 0xFF && (data[i+1]&0xE0) == 0xE0 {
			return true
		}
		// Only check first 4K to avoid false positives
		if i > 4096 {
			break
		}
	}
	return false
}

// parseWavInfo extracts WAV metadata
func (ap *AudioProcessor) parseWavInfo() error {
	reader := bytes.NewReader(ap.data)
	wavReader := wav.NewReader(reader)

	format, err := wavReader.Format()
	if err != nil {
		return fmt.Errorf("获取WAV格式错误: %v", err)
	}

	ap.info.Format = WAVFormat
	ap.info.SampleRate = int(format.SampleRate)
	ap.info.Channels = int(format.NumChannels)
	ap.info.BitDepth = int(format.BitsPerSample)
	ap.info.FileSize = len(ap.data)

	// Calculate duration
	bytesPerSample := int(format.BitsPerSample) / 8
	dataSize := len(ap.data) - 44 // Typical WAV header size
	totalSamples := dataSize / bytesPerSample / int(format.NumChannels)
	ap.info.TotalSamples = int64(totalSamples)
	ap.info.Duration = float64(totalSamples) / float64(format.SampleRate)

	return nil
}

// parseMP3Info extracts MP3 metadata
func (ap *AudioProcessor) parseMP3Info() error {
	reader := bytes.NewReader(ap.data)
	decoder, err := mp3.NewDecoder(reader)
	if err != nil {
		return fmt.Errorf("解析MP3错误: %v", err)
	}

	ap.info.Format = MP3Format
	ap.info.SampleRate = decoder.SampleRate()
	ap.info.Channels = 2  // MP3 typically has 2 channels
	ap.info.BitDepth = 16 // MP3 typically decodes to 16-bit
	ap.info.FileSize = len(ap.data)

	// Calculate duration
	samples := decoder.Length() / 4 // 4 bytes per sample (16-bit stereo)
	ap.info.TotalSamples = samples
	ap.info.Duration = float64(samples) / float64(decoder.SampleRate())

	return nil
}

// parseFLACInfo extracts FLAC metadata
func (ap *AudioProcessor) parseFLACInfo() error {
	reader := bytes.NewReader(ap.data)
	stream, err := flac.New(reader)
	if err != nil {
		return fmt.Errorf("解析FLAC错误: %v", err)
	}

	ap.info.Format = FLACFormat
	ap.info.SampleRate = int(stream.Info.SampleRate)
	ap.info.Channels = int(stream.Info.NChannels)
	ap.info.BitDepth = int(stream.Info.BitsPerSample)
	ap.info.FileSize = len(ap.data)
	ap.info.TotalSamples = int64(stream.Info.NSamples)
	ap.info.Duration = float64(stream.Info.NSamples) / float64(stream.Info.SampleRate)

	return nil
}

// parseAACInfo extracts AAC metadata
func (ap *AudioProcessor) parseAACInfo() error {
	ap.info.Format = AACFormat
	ap.info.FileSize = len(ap.data)

	// Extract sample rate and channels from AAC header
	if len(ap.data) > 2 {
		// AAC ADTS header parsing
		// See: https://wiki.multimedia.cx/index.php/ADTS

		// Sample rate index is in the 3rd byte
		sampleRateIndex := (ap.data[2] & 0x3C) >> 2

		// Map sample rate index to actual sample rate
		sampleRates := []int{96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350}
		if int(sampleRateIndex) < len(sampleRates) {
			ap.info.SampleRate = sampleRates[sampleRateIndex]
		} else {
			ap.info.SampleRate = 44100 // Default
		}

		// Channel configuration is stored across 3rd and 4th bytes
		channelConfig := ((ap.data[2] & 0x01) << 2) | ((ap.data[3] & 0xC0) >> 6)
		if channelConfig > 0 && channelConfig <= 7 {
			ap.info.Channels = int(channelConfig)
		} else {
			ap.info.Channels = 2 // Default to stereo
		}
	}

	ap.info.BitDepth = 16 // AAC typically decodes to 16-bit

	// Duration estimation is not accurate without parsing the entire file
	// This would require a full AAC parser
	ap.info.Duration = 0

	return nil
}

// DecodeToPCM converts audio data to raw PCM data
func (ap *AudioProcessor) DecodeToPCM() (pcmData []byte, audioPCMFormat AudioPCMFormat, err error) {
	switch ap.info.Format {
	case WAVFormat:
		return ap.wavToPCM()
	case MP3Format:
		return ap.mp3ToPCM()
	case AACFormat:
		return ap.aacToPCM()
	case FLACFormat:
		return ap.flacToPCM()
	default:
		return nil, AudioPCMFormat{}, fmt.Errorf("不支持的PCM转换格式: %s", ap.info.Format)
	}
}

// wavToPCM extracts PCM data from WAV
func (ap *AudioProcessor) wavToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	wavReader := wav.NewReader(reader)

	format, err := wavReader.Format()
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("获取WAV格式错误: %v", err)
	}

	// Skip the header and get the PCM data
	pcmData, err := io.ReadAll(wavReader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("读取WAV数据错误: %v", err)
	}

	return pcmData, AudioPCMFormat{
		SampleRate: int(format.SampleRate),
		Channels:   int(format.NumChannels),
		BitDepth:   int(format.BitsPerSample),
	}, nil
}

// mp3ToPCM decodes MP3 to PCM
func (ap *AudioProcessor) mp3ToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	decoder, err := mp3.NewDecoder(reader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("解析MP3错误: %v", err)
	}

	var pcmBuffer bytes.Buffer
	buf := make([]byte, 8192)
	for {
		n, err := decoder.Read(buf)
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, AudioPCMFormat{}, fmt.Errorf("解码MP3错误: %v", err)
		}
		pcmBuffer.Write(buf[:n])
	}

	return pcmBuffer.Bytes(), AudioPCMFormat{
		SampleRate: decoder.SampleRate(),
		Channels:   2,
		BitDepth:   16,
	}, nil // MP3 decodes to stereo
}

// flacToPCM decodes FLAC to PCM
func (ap *AudioProcessor) flacToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	stream, err := flac.New(reader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("解析FLAC错误: %v", err)
	}

	var pcmBuffer bytes.Buffer

	for {
		frame, err := stream.ParseNext()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, AudioPCMFormat{}, fmt.Errorf("解码FLAC帧错误: %v", err)
		}

		// For each channel
		for i := 0; i < int(stream.Info.NChannels); i++ {
			// For each sample in this frame
			for j := 0; j < len(frame.Subframes[i].Samples); j++ {
				// Convert sample to bytes and write to buffer
				sample := int16(frame.Subframes[i].Samples[j])
				binary.Write(&pcmBuffer, binary.LittleEndian, sample)
			}
		}
	}

	return pcmBuffer.Bytes(), AudioPCMFormat{
		SampleRate: int(stream.Info.SampleRate),
		Channels:   int(stream.Info.NChannels),
		BitDepth:   int(stream.Info.BitsPerSample),
	}, nil
}

// aacToPCM decodes AAC to PCM
func (ap *AudioProcessor) aacToPCM() ([]byte, AudioPCMFormat, error) {
	// For AAC decoding, we'd typically use a C library like FAAD2 with CGO
	// For pure Go, there are limited options
	// This is a placeholder that would need a proper implementation with a CGO wrapper
	return nil, AudioPCMFormat{}, fmt.Errorf("纯Go环境未完全实现AAC到PCM的转换")
}

// ConvertFormat converts audio from one format to another
func (ap *AudioProcessor) ConvertFormat(targetFormat string) ([]byte, error) {
	// First decode to PCM as an intermediate format
	pcmData, audioPCMFormat, err := ap.DecodeToPCM()
	if err != nil {
		return nil, err
	}

	// Then encode PCM to target format
	return EncodePCMTo(pcmData, audioPCMFormat, targetFormat)
}

// EncodePCMTo converts PCM data to a specific format
func EncodePCMTo(pcmData []byte, audioPCMFormat AudioPCMFormat, format string) ([]byte, error) {
	switch format {
	case WAVFormat:
		return PCMToWAV(pcmData, audioPCMFormat)
	case MP3Format:
		return nil, fmt.Errorf("纯Go环境未实现MP3编码 - 需要CGO和LAME")
	case AACFormat:
		return nil, fmt.Errorf("纯Go环境未实现AAC编码 - 需要CGO和FAAC")
	case FLACFormat:
		return nil, fmt.Errorf("未实现FLAC编码")
	default:
		return nil, fmt.Errorf("不支持的目标格式: %s", format)
	}
}

// PCMToWAV converts PCM data to WAV
func PCMToWAV(pcmData []byte, audioPCMFormat AudioPCMFormat) ([]byte, error) {
	var wavBuffer bytes.Buffer

	// Create WAV writer
	wavWriter := wav.NewWriter(&wavBuffer,
		uint32(len(pcmData)/2/audioPCMFormat.Channels),
		uint16(audioPCMFormat.Channels),
		uint32(audioPCMFormat.SampleRate),
		uint16(16),
		// uint16(audioPCMFormat.BitDepth),
	)

	// Convert PCM data to samples
	numSamples := len(pcmData) / 2 // 16-bit samples
	samples := make([]wav.Sample, numSamples)

	for i := 0; i < numSamples; i++ {
		var values [2]int
		// First channel
		values[0] = int(int16(binary.LittleEndian.Uint16(pcmData[i*2*audioPCMFormat.Channels : i*2*audioPCMFormat.Channels+2])))
		// Second channel (if stereo)
		if audioPCMFormat.Channels > 1 {
			values[1] = int(int16(binary.LittleEndian.Uint16(pcmData[i*2*audioPCMFormat.Channels+2 : i*2*audioPCMFormat.Channels+4])))
		} else {
			// If mono, duplicate the value to both channels
			values[1] = values[0]
		}
		samples[i].Values = values
	}

	if err := wavWriter.WriteSamples(samples); err != nil {
		return nil, fmt.Errorf("写入WAV采样数据错误: %v", err)
	}

	return wavBuffer.Bytes(), nil
}

// ResamplePCM resamples PCM data to a new sample rate, bit depth, and channel count
// pcmData: input PCM data (16-bit, little-endian)
// srcFormat: source PCM format
// dstFormat: destination PCM format
// Returns resampled PCM data
func ResamplePCM(pcmData []byte, srcFormat, dstFormat AudioPCMFormat) ([]byte, error) {
	if len(pcmData) == 0 {
		return nil, errors.New("PCM数据为空")
	}

	if srcFormat.BitDepth == dstFormat.BitDepth &&
		srcFormat.Channels == dstFormat.Channels &&
		srcFormat.SampleRate == dstFormat.SampleRate {
		return pcmData, nil
	}

	// Validate source format
	bytesPerSample := srcFormat.BitDepth / 8
	if bytesPerSample < 1 || bytesPerSample > 4 {
		return nil, fmt.Errorf("不支持的源比特深度: %d", srcFormat.BitDepth)
	}

	if srcFormat.Channels < 1 || srcFormat.Channels > 8 {
		return nil, fmt.Errorf("不支持的源声道数: %d", srcFormat.Channels)
	}

	if srcFormat.SampleRate < 1 {
		return nil, fmt.Errorf("无效的源采样率: %d", srcFormat.SampleRate)
	}

	// Validate destination format
	dstBytesPerSample := dstFormat.BitDepth / 8
	if dstBytesPerSample < 1 || dstBytesPerSample > 4 {
		return nil, fmt.Errorf("不支持的目标比特深度: %d", dstFormat.BitDepth)
	}

	if dstFormat.Channels < 1 || dstFormat.Channels > 8 {
		return nil, fmt.Errorf("不支持的目标声道数: %d", dstFormat.Channels)
	}

	if dstFormat.SampleRate < 1 {
		return nil, fmt.Errorf("无效的目标采样率: %d", dstFormat.SampleRate)
	}

	// Convert bit depth and extract samples
	samples, err := extractSamples(pcmData, srcFormat)
	if err != nil {
		return nil, err
	}

	// Process channel conversion
	samples = convertChannels(samples, srcFormat.Channels, dstFormat.Channels)

	// Process sample rate conversion
	samples = convertSampleRate(samples, srcFormat.SampleRate, dstFormat.SampleRate, dstFormat.Channels)

	// Pack samples back to PCM
	return packSamples(samples, dstFormat)
}

// extractSamples reads PCM data and extracts sample values as float64
func extractSamples(pcmData []byte, srcFormat AudioPCMFormat) ([]float64, error) {
	bytesPerFrame := srcFormat.BitDepth / 8 * srcFormat.Channels
	numFrames := len(pcmData) / bytesPerFrame

	// Extract samples from all channels into a single array
	samples := make([]float64, numFrames*srcFormat.Channels)

	for i := 0; i < numFrames; i++ {
		for ch := 0; ch < srcFormat.Channels; ch++ {
			sampleIndex := i*srcFormat.Channels + ch
			byteIndex := i*bytesPerFrame + ch*(srcFormat.BitDepth/8)

			// Extract sample based on bit depth
			var rawSample int64

			switch srcFormat.BitDepth {
			case 8:
				// 8-bit audio is typically unsigned
				rawSample = int64(pcmData[byteIndex]) - 128
				samples[sampleIndex] = float64(rawSample) / 128.0 // Normalize to [-1.0, 1.0]

			case 16:
				if byteIndex+1 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于16位采样过短")
				}
				// 16-bit audio is typically signed, little-endian
				rawSample = int64(int16(binary.LittleEndian.Uint16(pcmData[byteIndex : byteIndex+2])))
				samples[sampleIndex] = float64(rawSample) / 32768.0 // Normalize to [-1.0, 1.0]

			case 24:
				if byteIndex+2 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于24位采样过短")
				}
				// 24-bit audio is typically signed, little-endian
				rawSample = int64(pcmData[byteIndex]) | int64(pcmData[byteIndex+1])<<8 | int64(pcmData[byteIndex+2])<<16
				// Sign extension for negative values
				if (rawSample & 0x800000) != 0 {
					rawSample |= int64(-1) << 24
				}
				samples[sampleIndex] = float64(rawSample) / 8388608.0 // Normalize to [-1.0, 1.0]

			case 32:
				if byteIndex+3 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于32位采样过短")
				}
				// 32-bit audio is typically signed, little-endian
				rawSample = int64(binary.LittleEndian.Uint32(pcmData[byteIndex : byteIndex+4]))
				if (rawSample & 0x80000000) != 0 {
					rawSample = int64(int32(rawSample))
				}
				samples[sampleIndex] = float64(rawSample) / 2147483648.0 // Normalize to [-1.0, 1.0]

			default:
				return nil, fmt.Errorf("不支持的比特深度: %d", srcFormat.BitDepth)
			}
		}
	}

	return samples, nil
}

// convertChannels changes the number of channels in the audio
func convertChannels(samples []float64, srcChannels, dstChannels int) []float64 {
	// If no change in channels, return the original samples
	if srcChannels == dstChannels {
		return samples
	}

	numFrames := len(samples) / srcChannels
	result := make([]float64, numFrames*dstChannels)

	// Convert channels
	for i := 0; i < numFrames; i++ {
		srcOffset := i * srcChannels
		dstOffset := i * dstChannels

		if srcChannels == 1 && dstChannels > 1 {
			// Mono to multi-channel (duplicate mono to all channels)
			monoSample := samples[srcOffset]
			for ch := 0; ch < dstChannels; ch++ {
				result[dstOffset+ch] = monoSample
			}
		} else if srcChannels > 1 && dstChannels == 1 {
			// Multi-channel to mono (average all channels)
			var sum float64
			for ch := 0; ch < srcChannels; ch++ {
				sum += samples[srcOffset+ch]
			}
			result[dstOffset] = sum / float64(srcChannels)
		} else {
			// General case: mix or drop channels
			for ch := 0; ch < dstChannels; ch++ {
				if ch < srcChannels {
					// Copy existing channel
					result[dstOffset+ch] = samples[srcOffset+ch]
				} else {
					// For additional channels, duplicate the last channel
					result[dstOffset+ch] = samples[srcOffset+srcChannels-1]
				}
			}
		}
	}

	return result
}

// convertSampleRate changes the sample rate of the audio using linear interpolation
func convertSampleRate(samples []float64, srcRate, dstRate, channels int) []float64 {
	// If no change in sample rate, return the original samples
	if srcRate == dstRate {
		return samples
	}

	// Calculate new length
	srcFrames := len(samples) / channels
	dstFrames := int(float64(srcFrames) * float64(dstRate) / float64(srcRate))

	result := make([]float64, dstFrames*channels)

	// Resample using linear interpolation
	for i := 0; i < dstFrames; i++ {
		// Calculate the exact position in the source
		srcPos := float64(i) * float64(srcRate) / float64(dstRate)
		srcIndex := int(srcPos)
		frac := srcPos - float64(srcIndex) // Fractional part for interpolation

		// Linear interpolation between adjacent samples for each channel
		for ch := 0; ch < channels; ch++ {
			if srcIndex < srcFrames-1 {
				// Normal case - interpolate between two adjacent samples
				sample1 := samples[srcIndex*channels+ch]
				sample2 := samples[(srcIndex+1)*channels+ch]
				result[i*channels+ch] = sample1 + frac*(sample2-sample1)
			} else if srcIndex < srcFrames {
				// Last frame, no next sample to interpolate with
				result[i*channels+ch] = samples[srcIndex*channels+ch]
			} else {
				// Beyond source data (shouldn't happen with our calculation)
				result[i*channels+ch] = 0
			}
		}
	}

	return result
}

// packSamples converts normalized float64 samples back to PCM bytes
func packSamples(samples []float64, dstFormat AudioPCMFormat) ([]byte, error) {
	numFrames := len(samples) / dstFormat.Channels
	bytesPerFrame := dstFormat.Channels * (dstFormat.BitDepth / 8)
	result := make([]byte, numFrames*bytesPerFrame)

	for i := 0; i < numFrames; i++ {
		for ch := 0; ch < dstFormat.Channels; ch++ {
			sampleIndex := i*dstFormat.Channels + ch
			byteIndex := i*bytesPerFrame + ch*(dstFormat.BitDepth/8)

			// Clamp the sample value to [-1.0, 1.0]
			sample := samples[sampleIndex]
			if sample < -1.0 {
				sample = -1.0
			} else if sample > 1.0 {
				sample = 1.0
			}

			// Convert to appropriate bit depth
			switch dstFormat.BitDepth {
			case 8:
				// 8-bit PCM is typically unsigned [0, 255]
				value := byte(sample*127.0 + 128.0)
				result[byteIndex] = value

			case 16:
				// 16-bit PCM is typically signed [-32768, 32767]
				value := int16(sample * 32767.0)
				binary.LittleEndian.PutUint16(result[byteIndex:byteIndex+2], uint16(value))

			case 24:
				// 24-bit PCM is typically signed [-8388608, 8388607]
				value := int32(sample * 8388607.0)
				result[byteIndex] = byte(value)
				result[byteIndex+1] = byte(value >> 8)
				result[byteIndex+2] = byte(value >> 16)

			case 32:
				// 32-bit PCM is typically signed [-2147483648, 2147483647]
				value := int32(sample * 2147483647.0)
				binary.LittleEndian.PutUint32(result[byteIndex:byteIndex+4], uint32(value))

			default:
				return nil, fmt.Errorf("不支持的输出比特深度: %d", dstFormat.BitDepth)
			}
		}
	}

	return result, nil
}

// MixAudioFramesWithWeights 根据权重混音多个音频帧
// audioFrames: 音频帧数组
// weights: 权重数组，长度必须与audioFrames相同，范围建议[0.0, 1.0]
// audioPCMFormat: PCM格式参数
// 返回混音后的PCM数据
func MixAudioFramesWithWeights(audioFrames [][]byte, weights []float64, audioPCMFormat AudioPCMFormat) ([]byte, error) {
	// 参数验证
	if len(audioFrames) == 0 {
		return nil, errors.New("没有音频帧需要混音")
	}

	if len(weights) != len(audioFrames) {
		return nil, fmt.Errorf("权重数组长度 %d 与音频帧数量 %d 不匹配", len(weights), len(audioFrames))
	}

	// 验证权重范围
	for i, weight := range weights {
		if weight < 0.0 {
			return nil, fmt.Errorf("权重 %d 的值 %f 不能为负数", i, weight)
		}
		if weight > 2.0 { // 允许适度的增强
			return nil, fmt.Errorf("权重 %d 的值 %f 过大，建议不超过2.0", i, weight)
		}
	}

	// 如果只有一个音频帧，直接应用权重并返回
	if len(audioFrames) == 1 {
		return applyWeightToAudioFrame(audioFrames[0], weights[0], audioPCMFormat)
	}

	// 预处理音频帧：根据权重调整每个音频帧的音量
	processedFrames := make([][]byte, len(audioFrames))
	for i, frame := range audioFrames {
		if weights[i] == 0.0 {
			// 权重为0，跳过此音频帧
			continue
		}

		processedFrame, err := applyWeightToAudioFrame(frame, weights[i], audioPCMFormat)
		if err != nil {
			return nil, fmt.Errorf("处理音频帧 %d 权重失败: %w", i, err)
		}
		processedFrames[i] = processedFrame
	}

	// 过滤掉权重为0的音频帧
	filteredFrames := make([][]byte, 0, len(processedFrames))
	for i, frame := range processedFrames {
		if weights[i] > 0.0 && len(frame) > 0 {
			filteredFrames = append(filteredFrames, frame)
		}
	}

	// 如果所有权重都为0，返回静音
	if len(filteredFrames) == 0 {
		// 创建与第一个音频帧同样长度的静音数据
		if len(audioFrames[0]) > 0 {
			silentFrame := make([]byte, len(audioFrames[0]))
			return silentFrame, nil
		}
		return nil, errors.New("所有音频帧权重都为0")
	}

	// 调用现有的混音函数
	return MixAudioFrames(filteredFrames, audioPCMFormat)
}

// applyWeightToAudioFrame 对单个音频帧应用权重（音量调整）
func applyWeightToAudioFrame(audioFrame []byte, weight float64, audioPCMFormat AudioPCMFormat) ([]byte, error) {
	if len(audioFrame) == 0 {
		return audioFrame, nil
	}

	if weight == 1.0 {
		// 权重为1.0，不需要处理，直接返回副本
		result := make([]byte, len(audioFrame))
		copy(result, audioFrame)
		return result, nil
	}

	// 提取样本
	samples, err := extractSamples(audioFrame, audioPCMFormat)
	if err != nil {
		return nil, fmt.Errorf("提取音频样本失败: %w", err)
	}

	// 应用权重（音量调整）
	for i := range samples {
		samples[i] *= weight

		// 软限幅处理，防止削波
		if samples[i] > 1.0 {
			samples[i] = 0.7 + 0.3*math.Tanh((samples[i]-1.0)*3.0)
		} else if samples[i] < -1.0 {
			samples[i] = -0.7 - 0.3*math.Tanh((-samples[i]-1.0)*3.0)
		}
	}

	// 重新打包为PCM数据
	result, err := packSamples(samples, audioPCMFormat)
	if err != nil {
		return nil, fmt.Errorf("打包音频样本失败: %w", err)
	}

	return result, nil
}

func MixAudioFrames(audioFrames [][]byte, audioPCMFormat AudioPCMFormat) ([]byte, error) {
	// 参数验证
	if len(audioFrames) == 0 {
		return nil, errors.New("没有音频帧需要混音")
	}

	// 如果只有一个音频帧，直接返回
	if len(audioFrames) == 1 {
		return audioFrames[0], nil
	}

	// 检查音频帧是否为空
	if len(audioFrames[0]) == 0 {
		return nil, errors.New("音频帧长度为0")
	}

	// 获取最长音频帧的长度，作为最终输出长度
	maxFrameLength := 0
	for _, frame := range audioFrames {
		if len(frame) > maxFrameLength {
			maxFrameLength = len(frame)
		}
	}

	// 验证PCM格式参数
	if audioPCMFormat.Channels < 1 || audioPCMFormat.Channels > 8 {
		return nil, fmt.Errorf("不支持的声道数: %d", audioPCMFormat.Channels)
	}
	if audioPCMFormat.BitDepth != 8 && audioPCMFormat.BitDepth != 16 &&
		audioPCMFormat.BitDepth != 24 && audioPCMFormat.BitDepth != 32 {
		return nil, fmt.Errorf("不支持的位深度: %d", audioPCMFormat.BitDepth)
	}

	// 计算帧参数
	bytesPerSample := audioPCMFormat.BitDepth / 8
	bytesPerFrame := bytesPerSample * audioPCMFormat.Channels

	// 验证最长音频帧的长度是否与格式匹配
	if maxFrameLength%bytesPerFrame != 0 {
		return nil, fmt.Errorf("音频帧长度 %d 与格式不匹配 (每帧 %d 字节)", maxFrameLength, bytesPerFrame)
	}

	// 提取所有音频帧的样本数据，允许长度不同
	allSamples := make([][]float64, len(audioFrames))
	frameLengths := make([]int, len(audioFrames))

	for i, frame := range audioFrames {
		// 验证每个音频帧的长度是否与格式匹配
		if len(frame)%bytesPerFrame != 0 {
			return nil, fmt.Errorf("音频帧 %d 长度 %d 与格式不匹配 (每帧 %d 字节)", i, len(frame), bytesPerFrame)
		}

		samples, err := extractSamples(frame, audioPCMFormat)
		if err != nil {
			return nil, fmt.Errorf("提取音频帧 %d 样本失败: %w", i, err)
		}
		allSamples[i] = samples
		frameLengths[i] = len(samples)
	}

	// 执行分段混音，动态处理不同长度的音频
	mixedSamples := performAdaptiveMixing(allSamples, frameLengths, audioPCMFormat.Channels)

	// 将混音结果打包为PCM数据
	result, err := packSamples(mixedSamples, audioPCMFormat)
	if err != nil {
		return nil, fmt.Errorf("打包混音结果失败: %w", err)
	}

	return result, nil
}

// performAdaptiveMixing 执行分段混音算法，处理不同长度的音频
func performAdaptiveMixing(allSamples [][]float64, frameLengths []int, channels int) []float64 {
	if len(allSamples) == 0 {
		return nil
	}

	// 找到最长的音频长度
	maxLength := 0
	for _, length := range frameLengths {
		if length > maxLength {
			maxLength = length
		}
	}

	if maxLength == 0 {
		return nil
	}

	result := make([]float64, maxLength)

	// 创建音频结束点的排序列表，用于动态调整混音源
	type audioEndPoint struct {
		index  int // 音频索引
		endPos int // 结束位置
	}

	endPoints := make([]audioEndPoint, 0, len(allSamples))
	for i, length := range frameLengths {
		if length > 0 {
			endPoints = append(endPoints, audioEndPoint{index: i, endPos: length})
		}
	}

	// 使用快速排序优化 - O(N log N) 而不是 O(N²)
	if len(endPoints) > 1 {
		sort.Slice(endPoints, func(i, j int) bool {
			return endPoints[i].endPos < endPoints[j].endPos
		})
	}

	// 分段混音
	currentPos := 0
	activeAudios := make([]int, len(allSamples)) // 当前活跃的音频索引
	for i := range activeAudios {
		activeAudios[i] = i
	}

	endPointIndex := 0

	for currentPos < maxLength {
		// 确定当前段的结束位置
		segmentEnd := maxLength
		if endPointIndex < len(endPoints) {
			segmentEnd = endPoints[endPointIndex].endPos
		}

		// 计算当前活跃音频的数量
		activeCount := len(activeAudios)
		if activeCount == 0 {
			break
		}

		// 计算当前段的权重 - 预计算避免重复计算
		weight := 1.0 / math.Sqrt(float64(activeCount))

		// 混音当前段
		for pos := currentPos; pos < segmentEnd && pos < maxLength; pos++ {
			var mixedValue float64

			// 累加所有活跃音频的样本
			for _, audioIndex := range activeAudios {
				if pos < len(allSamples[audioIndex]) {
					mixedValue += allSamples[audioIndex][pos] * weight
				}
			}

			// 软限幅处理
			if mixedValue > 1.0 {
				mixedValue = 0.7 + 0.3*math.Tanh((mixedValue-1.0)*3.0)
			} else if mixedValue < -1.0 {
				mixedValue = -0.7 - 0.3*math.Tanh((-mixedValue-1.0)*3.0)
			}

			result[pos] = mixedValue
		}

		// 移除在当前位置结束的音频 - 优化内存分配
		for endPointIndex < len(endPoints) && endPoints[endPointIndex].endPos <= segmentEnd {
			endedAudioIndex := endPoints[endPointIndex].index

			// 从活跃音频列表中移除 - 使用原地操作减少内存分配
			newActiveAudios := activeAudios[:0] // 重用切片
			for _, audioIndex := range activeAudios {
				if audioIndex != endedAudioIndex {
					newActiveAudios = append(newActiveAudios, audioIndex)
				}
			}
			activeAudios = newActiveAudios
			endPointIndex++
		}

		currentPos = segmentEnd
	}

	// 动态范围优化
	optimizeDynamicRange(result, channels)

	return result
}

// optimizeDynamicRange 优化混音结果的动态范围
func optimizeDynamicRange(samples []float64, channels int) {
	if len(samples) == 0 {
		return
	}

	// 计算RMS音量（分声道计算）
	numFrames := len(samples) / channels
	channelRMS := make([]float64, channels)

	for ch := 0; ch < channels; ch++ {
		var sumSquares float64
		for i := 0; i < numFrames; i++ {
			sample := samples[i*channels+ch]
			sumSquares += sample * sample
		}
		channelRMS[ch] = math.Sqrt(sumSquares / float64(numFrames))
	}

	// 计算平均RMS
	var avgRMS float64
	for _, rms := range channelRMS {
		avgRMS += rms
	}
	avgRMS /= float64(channels)

	// 如果平均音量过低（小于0.3），进行适度提升
	// 但要确保不会导致削波
	if avgRMS > 0 && avgRMS < 0.3 {
		// 计算峰值，确保增益后不会超出范围
		var peak float64
		for _, sample := range samples {
			if math.Abs(sample) > peak {
				peak = math.Abs(sample)
			}
		}

		// 计算安全的增益值
		targetRMS := 0.5
		desiredGain := targetRMS / avgRMS
		maxSafeGain := 0.95 / peak // 留5%的余量

		actualGain := math.Min(desiredGain, maxSafeGain)
		actualGain = math.Min(actualGain, 2.0) // 限制最大增益为2倍

		// 只有在增益大于1.1时才应用（避免微小的调整）
		if actualGain > 1.1 {
			for i := range samples {
				samples[i] *= actualGain
			}
		}
	}
}
