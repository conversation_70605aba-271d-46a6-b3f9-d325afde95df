// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: llm_parent.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ParentChat_Chat_FullMethodName = "/doll.parent.ParentChat/Chat"
)

// ParentChatClient is the client API for ParentChat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParentChatClient interface {
	Chat(ctx context.Context, in *ParentRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ParentResponse], error)
}

type parentChatClient struct {
	cc grpc.ClientConnInterface
}

func NewParentChatClient(cc grpc.ClientConnInterface) ParentChatClient {
	return &parentChatClient{cc}
}

func (c *parentChatClient) Chat(ctx context.Context, in *ParentRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ParentResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ParentChat_ServiceDesc.Streams[0], ParentChat_Chat_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ParentRequest, ParentResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ParentChat_ChatClient = grpc.ServerStreamingClient[ParentResponse]

// ParentChatServer is the server API for ParentChat service.
// All implementations must embed UnimplementedParentChatServer
// for forward compatibility.
type ParentChatServer interface {
	Chat(*ParentRequest, grpc.ServerStreamingServer[ParentResponse]) error
	mustEmbedUnimplementedParentChatServer()
}

// UnimplementedParentChatServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedParentChatServer struct{}

func (UnimplementedParentChatServer) Chat(*ParentRequest, grpc.ServerStreamingServer[ParentResponse]) error {
	return status.Errorf(codes.Unimplemented, "method Chat not implemented")
}
func (UnimplementedParentChatServer) mustEmbedUnimplementedParentChatServer() {}
func (UnimplementedParentChatServer) testEmbeddedByValue()                    {}

// UnsafeParentChatServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParentChatServer will
// result in compilation errors.
type UnsafeParentChatServer interface {
	mustEmbedUnimplementedParentChatServer()
}

func RegisterParentChatServer(s grpc.ServiceRegistrar, srv ParentChatServer) {
	// If the following call pancis, it indicates UnimplementedParentChatServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ParentChat_ServiceDesc, srv)
}

func _ParentChat_Chat_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ParentRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ParentChatServer).Chat(m, &grpc.GenericServerStream[ParentRequest, ParentResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ParentChat_ChatServer = grpc.ServerStreamingServer[ParentResponse]

// ParentChat_ServiceDesc is the grpc.ServiceDesc for ParentChat service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ParentChat_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "doll.parent.ParentChat",
	HandlerType: (*ParentChatServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Chat",
			Handler:       _ParentChat_Chat_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "llm_parent.proto",
}
