// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tts.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TTSRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Text            string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	VoiceType       string                 `protobuf:"bytes,2,opt,name=voice_type,json=voiceType,proto3" json:"voice_type,omitempty"`
	MsgIndex        int32                  `protobuf:"varint,3,opt,name=msg_index,json=msgIndex,proto3" json:"msg_index,omitempty"`
	MsgSegmentIndex int32                  `protobuf:"varint,4,opt,name=msg_segment_index,json=msgSegmentIndex,proto3" json:"msg_segment_index,omitempty"`
	IsCustomTts     bool                   `protobuf:"varint,5,opt,name=is_custom_tts,json=isCustomTts,proto3" json:"is_custom_tts,omitempty"`
	RequestId       int32                  `protobuf:"varint,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TTSRequest) Reset() {
	*x = TTSRequest{}
	mi := &file_tts_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TTSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TTSRequest) ProtoMessage() {}

func (x *TTSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TTSRequest.ProtoReflect.Descriptor instead.
func (*TTSRequest) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{0}
}

func (x *TTSRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TTSRequest) GetVoiceType() string {
	if x != nil {
		return x.VoiceType
	}
	return ""
}

func (x *TTSRequest) GetMsgIndex() int32 {
	if x != nil {
		return x.MsgIndex
	}
	return 0
}

func (x *TTSRequest) GetMsgSegmentIndex() int32 {
	if x != nil {
		return x.MsgSegmentIndex
	}
	return 0
}

func (x *TTSRequest) GetIsCustomTts() bool {
	if x != nil {
		return x.IsCustomTts
	}
	return false
}

func (x *TTSRequest) GetRequestId() int32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type TTSResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Audio           []byte                 `protobuf:"bytes,1,opt,name=audio,proto3" json:"audio,omitempty"` // 16k sample rate
	MsgIndex        int32                  `protobuf:"varint,2,opt,name=msg_index,json=msgIndex,proto3" json:"msg_index,omitempty"`
	MsgSegmentIndex int32                  `protobuf:"varint,3,opt,name=msg_segment_index,json=msgSegmentIndex,proto3" json:"msg_segment_index,omitempty"`
	RequestId       int32                  `protobuf:"varint,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	IsFinal         bool                   `protobuf:"varint,5,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	ErrorCode       int32                  `protobuf:"varint,10,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TTSResponse) Reset() {
	*x = TTSResponse{}
	mi := &file_tts_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TTSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TTSResponse) ProtoMessage() {}

func (x *TTSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TTSResponse.ProtoReflect.Descriptor instead.
func (*TTSResponse) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{1}
}

func (x *TTSResponse) GetAudio() []byte {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *TTSResponse) GetMsgIndex() int32 {
	if x != nil {
		return x.MsgIndex
	}
	return 0
}

func (x *TTSResponse) GetMsgSegmentIndex() int32 {
	if x != nil {
		return x.MsgSegmentIndex
	}
	return 0
}

func (x *TTSResponse) GetRequestId() int32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *TTSResponse) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *TTSResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

var File_tts_proto protoreflect.FileDescriptor

const file_tts_proto_rawDesc = "" +
	"\n" +
	"\ttts.proto\x12\bdoll.tts\"\xcb\x01\n" +
	"\n" +
	"TTSRequest\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x1d\n" +
	"\n" +
	"voice_type\x18\x02 \x01(\tR\tvoiceType\x12\x1b\n" +
	"\tmsg_index\x18\x03 \x01(\x05R\bmsgIndex\x12*\n" +
	"\x11msg_segment_index\x18\x04 \x01(\x05R\x0fmsgSegmentIndex\x12\"\n" +
	"\ris_custom_tts\x18\x05 \x01(\bR\visCustomTts\x12\x1d\n" +
	"\n" +
	"request_id\x18\x06 \x01(\x05R\trequestId\"\xc5\x01\n" +
	"\vTTSResponse\x12\x14\n" +
	"\x05audio\x18\x01 \x01(\fR\x05audio\x12\x1b\n" +
	"\tmsg_index\x18\x02 \x01(\x05R\bmsgIndex\x12*\n" +
	"\x11msg_segment_index\x18\x03 \x01(\x05R\x0fmsgSegmentIndex\x12\x1d\n" +
	"\n" +
	"request_id\x18\x04 \x01(\x05R\trequestId\x12\x19\n" +
	"\bis_final\x18\x05 \x01(\bR\aisFinal\x12\x1d\n" +
	"\n" +
	"error_code\x18\n" +
	" \x01(\x05R\terrorCode2=\n" +
	"\x03TTS\x126\n" +
	"\x03TTS\x12\x14.doll.tts.TTSRequest\x1a\x15.doll.tts.TTSResponse(\x010\x01B\x12Z\x10aigc.proto;protob\x06proto3"

var (
	file_tts_proto_rawDescOnce sync.Once
	file_tts_proto_rawDescData []byte
)

func file_tts_proto_rawDescGZIP() []byte {
	file_tts_proto_rawDescOnce.Do(func() {
		file_tts_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tts_proto_rawDesc), len(file_tts_proto_rawDesc)))
	})
	return file_tts_proto_rawDescData
}

var file_tts_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tts_proto_goTypes = []any{
	(*TTSRequest)(nil),  // 0: doll.tts.TTSRequest
	(*TTSResponse)(nil), // 1: doll.tts.TTSResponse
}
var file_tts_proto_depIdxs = []int32{
	0, // 0: doll.tts.TTS.TTS:input_type -> doll.tts.TTSRequest
	1, // 1: doll.tts.TTS.TTS:output_type -> doll.tts.TTSResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tts_proto_init() }
func file_tts_proto_init() {
	if File_tts_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tts_proto_rawDesc), len(file_tts_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tts_proto_goTypes,
		DependencyIndexes: file_tts_proto_depIdxs,
		MessageInfos:      file_tts_proto_msgTypes,
	}.Build()
	File_tts_proto = out.File
	file_tts_proto_goTypes = nil
	file_tts_proto_depIdxs = nil
}
