// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: llm_parent.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ParentRespType int32

const (
	ParentRespType_NORMAL ParentRespType = 0
	ParentRespType_RESUME ParentRespType = 1
	ParentRespType_RESET  ParentRespType = 2
)

// Enum value maps for ParentRespType.
var (
	ParentRespType_name = map[int32]string{
		0: "NORMAL",
		1: "RESUME",
		2: "RESET",
	}
	ParentRespType_value = map[string]int32{
		"NORMAL": 0,
		"RESUME": 1,
		"RESET":  2,
	}
)

func (x ParentRespType) Enum() *ParentRespType {
	p := new(ParentRespType)
	*p = x
	return p
}

func (x ParentRespType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParentRespType) Descriptor() protoreflect.EnumDescriptor {
	return file_llm_parent_proto_enumTypes[0].Descriptor()
}

func (ParentRespType) Type() protoreflect.EnumType {
	return &file_llm_parent_proto_enumTypes[0]
}

func (x ParentRespType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParentRespType.Descriptor instead.
func (ParentRespType) EnumDescriptor() ([]byte, []int) {
	return file_llm_parent_proto_rawDescGZIP(), []int{0}
}

type ParentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsFinal       bool                   `protobuf:"varint,1,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"` // asr 识别是否一句话结束
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParentRequest) Reset() {
	*x = ParentRequest{}
	mi := &file_llm_parent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentRequest) ProtoMessage() {}

func (x *ParentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_llm_parent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentRequest.ProtoReflect.Descriptor instead.
func (*ParentRequest) Descriptor() ([]byte, []int) {
	return file_llm_parent_proto_rawDescGZIP(), []int{0}
}

func (x *ParentRequest) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *ParentRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ParentResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Type            ParentRespType         `protobuf:"varint,1,opt,name=type,proto3,enum=doll.parent.ParentRespType" json:"type,omitempty"`
	Content         string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	MsgIndex        int32                  `protobuf:"varint,3,opt,name=msg_index,json=msgIndex,proto3" json:"msg_index,omitempty"`
	MsgSegmentIndex int32                  `protobuf:"varint,4,opt,name=msg_segment_index,json=msgSegmentIndex,proto3" json:"msg_segment_index,omitempty"`
	IsFinal         bool                   `protobuf:"varint,5,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	ToolCall        map[string]string      `protobuf:"bytes,6,rep,name=tool_call,json=toolCall,proto3" json:"tool_call,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // fn -> fn_msg
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ParentResponse) Reset() {
	*x = ParentResponse{}
	mi := &file_llm_parent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentResponse) ProtoMessage() {}

func (x *ParentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_llm_parent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentResponse.ProtoReflect.Descriptor instead.
func (*ParentResponse) Descriptor() ([]byte, []int) {
	return file_llm_parent_proto_rawDescGZIP(), []int{1}
}

func (x *ParentResponse) GetType() ParentRespType {
	if x != nil {
		return x.Type
	}
	return ParentRespType_NORMAL
}

func (x *ParentResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ParentResponse) GetMsgIndex() int32 {
	if x != nil {
		return x.MsgIndex
	}
	return 0
}

func (x *ParentResponse) GetMsgSegmentIndex() int32 {
	if x != nil {
		return x.MsgSegmentIndex
	}
	return 0
}

func (x *ParentResponse) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *ParentResponse) GetToolCall() map[string]string {
	if x != nil {
		return x.ToolCall
	}
	return nil
}

var File_llm_parent_proto protoreflect.FileDescriptor

const file_llm_parent_proto_rawDesc = "" +
	"\n" +
	"\x10llm_parent.proto\x12\vdoll.parent\"D\n" +
	"\rParentRequest\x12\x19\n" +
	"\bis_final\x18\x01 \x01(\bR\aisFinal\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"\xc4\x02\n" +
	"\x0eParentResponse\x12/\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1b.doll.parent.ParentRespTypeR\x04type\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1b\n" +
	"\tmsg_index\x18\x03 \x01(\x05R\bmsgIndex\x12*\n" +
	"\x11msg_segment_index\x18\x04 \x01(\x05R\x0fmsgSegmentIndex\x12\x19\n" +
	"\bis_final\x18\x05 \x01(\bR\aisFinal\x12F\n" +
	"\ttool_call\x18\x06 \x03(\v2).doll.parent.ParentResponse.ToolCallEntryR\btoolCall\x1a;\n" +
	"\rToolCallEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*3\n" +
	"\x0eParentRespType\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x00\x12\n" +
	"\n" +
	"\x06RESUME\x10\x01\x12\t\n" +
	"\x05RESET\x10\x022O\n" +
	"\n" +
	"ParentChat\x12A\n" +
	"\x04Chat\x12\x1a.doll.parent.ParentRequest\x1a\x1b.doll.parent.ParentResponse0\x01B\x12Z\x10aigc.proto;protob\x06proto3"

var (
	file_llm_parent_proto_rawDescOnce sync.Once
	file_llm_parent_proto_rawDescData []byte
)

func file_llm_parent_proto_rawDescGZIP() []byte {
	file_llm_parent_proto_rawDescOnce.Do(func() {
		file_llm_parent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_llm_parent_proto_rawDesc), len(file_llm_parent_proto_rawDesc)))
	})
	return file_llm_parent_proto_rawDescData
}

var file_llm_parent_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_llm_parent_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_llm_parent_proto_goTypes = []any{
	(ParentRespType)(0),    // 0: doll.parent.ParentRespType
	(*ParentRequest)(nil),  // 1: doll.parent.ParentRequest
	(*ParentResponse)(nil), // 2: doll.parent.ParentResponse
	nil,                    // 3: doll.parent.ParentResponse.ToolCallEntry
}
var file_llm_parent_proto_depIdxs = []int32{
	0, // 0: doll.parent.ParentResponse.type:type_name -> doll.parent.ParentRespType
	3, // 1: doll.parent.ParentResponse.tool_call:type_name -> doll.parent.ParentResponse.ToolCallEntry
	1, // 2: doll.parent.ParentChat.Chat:input_type -> doll.parent.ParentRequest
	2, // 3: doll.parent.ParentChat.Chat:output_type -> doll.parent.ParentResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_llm_parent_proto_init() }
func file_llm_parent_proto_init() {
	if File_llm_parent_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_llm_parent_proto_rawDesc), len(file_llm_parent_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_llm_parent_proto_goTypes,
		DependencyIndexes: file_llm_parent_proto_depIdxs,
		EnumInfos:         file_llm_parent_proto_enumTypes,
		MessageInfos:      file_llm_parent_proto_msgTypes,
	}.Build()
	File_llm_parent_proto = out.File
	file_llm_parent_proto_goTypes = nil
	file_llm_parent_proto_depIdxs = nil
}
