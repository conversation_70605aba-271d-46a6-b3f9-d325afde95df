// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: llm.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LLMChat_Chat_FullMethodName = "/doll.llm.LLMChat/Chat"
)

// LLMChatClient is the client API for LLMChat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LLMChatClient interface {
	Chat(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[LLMRequest, LLMResponse], error)
}

type lLMChatClient struct {
	cc grpc.ClientConnInterface
}

func NewLLMChatClient(cc grpc.ClientConnInterface) LLMChatClient {
	return &lLMChatClient{cc}
}

func (c *lLMChatClient) Chat(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[LLMRequest, LLMResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &LLMChat_ServiceDesc.Streams[0], LLMChat_Chat_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[LLMRequest, LLMResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type LLMChat_ChatClient = grpc.BidiStreamingClient[LLMRequest, LLMResponse]

// LLMChatServer is the server API for LLMChat service.
// All implementations must embed UnimplementedLLMChatServer
// for forward compatibility.
type LLMChatServer interface {
	Chat(grpc.BidiStreamingServer[LLMRequest, LLMResponse]) error
	mustEmbedUnimplementedLLMChatServer()
}

// UnimplementedLLMChatServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLLMChatServer struct{}

func (UnimplementedLLMChatServer) Chat(grpc.BidiStreamingServer[LLMRequest, LLMResponse]) error {
	return status.Errorf(codes.Unimplemented, "method Chat not implemented")
}
func (UnimplementedLLMChatServer) mustEmbedUnimplementedLLMChatServer() {}
func (UnimplementedLLMChatServer) testEmbeddedByValue()                 {}

// UnsafeLLMChatServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LLMChatServer will
// result in compilation errors.
type UnsafeLLMChatServer interface {
	mustEmbedUnimplementedLLMChatServer()
}

func RegisterLLMChatServer(s grpc.ServiceRegistrar, srv LLMChatServer) {
	// If the following call pancis, it indicates UnimplementedLLMChatServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LLMChat_ServiceDesc, srv)
}

func _LLMChat_Chat_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(LLMChatServer).Chat(&grpc.GenericServerStream[LLMRequest, LLMResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type LLMChat_ChatServer = grpc.BidiStreamingServer[LLMRequest, LLMResponse]

// LLMChat_ServiceDesc is the grpc.ServiceDesc for LLMChat service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LLMChat_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "doll.llm.LLMChat",
	HandlerType: (*LLMChatServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Chat",
			Handler:       _LLMChat_Chat_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "llm.proto",
}
