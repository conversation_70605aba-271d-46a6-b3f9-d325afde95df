// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: asr.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ASR_ASR_FullMethodName = "/doll.asr.ASR/ASR"
)

// ASRClient is the client API for ASR service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ASRClient interface {
	ASR(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[ASRRequest, ASRResponse], error)
}

type aSRClient struct {
	cc grpc.ClientConnInterface
}

func NewASRClient(cc grpc.ClientConnInterface) ASRClient {
	return &aSRClient{cc}
}

func (c *aSRClient) ASR(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[ASRRequest, ASRResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ASR_ServiceDesc.Streams[0], ASR_ASR_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ASRRequest, ASRResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ASR_ASRClient = grpc.BidiStreamingClient[ASRRequest, ASRResponse]

// ASRServer is the server API for ASR service.
// All implementations must embed UnimplementedASRServer
// for forward compatibility.
type ASRServer interface {
	ASR(grpc.BidiStreamingServer[ASRRequest, ASRResponse]) error
	mustEmbedUnimplementedASRServer()
}

// UnimplementedASRServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedASRServer struct{}

func (UnimplementedASRServer) ASR(grpc.BidiStreamingServer[ASRRequest, ASRResponse]) error {
	return status.Errorf(codes.Unimplemented, "method ASR not implemented")
}
func (UnimplementedASRServer) mustEmbedUnimplementedASRServer() {}
func (UnimplementedASRServer) testEmbeddedByValue()             {}

// UnsafeASRServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ASRServer will
// result in compilation errors.
type UnsafeASRServer interface {
	mustEmbedUnimplementedASRServer()
}

func RegisterASRServer(s grpc.ServiceRegistrar, srv ASRServer) {
	// If the following call pancis, it indicates UnimplementedASRServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ASR_ServiceDesc, srv)
}

func _ASR_ASR_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ASRServer).ASR(&grpc.GenericServerStream[ASRRequest, ASRResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ASR_ASRServer = grpc.BidiStreamingServer[ASRRequest, ASRResponse]

// ASR_ServiceDesc is the grpc.ServiceDesc for ASR service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ASR_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "doll.asr.ASR",
	HandlerType: (*ASRServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ASR",
			Handler:       _ASR_ASR_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "asr.proto",
}
