// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: tts.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TTS_TTS_FullMethodName = "/doll.tts.TTS/TTS"
)

// TTSClient is the client API for TTS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TTSClient interface {
	TTS(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[TTSRequest, TTSResponse], error)
}

type tTSClient struct {
	cc grpc.ClientConnInterface
}

func NewTTSClient(cc grpc.ClientConnInterface) TTSClient {
	return &tTSClient{cc}
}

func (c *tTSClient) TTS(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[TTSRequest, TTSResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &TTS_ServiceDesc.Streams[0], TTS_TTS_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[TTSRequest, TTSResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type TTS_TTSClient = grpc.BidiStreamingClient[TTSRequest, TTSResponse]

// TTSServer is the server API for TTS service.
// All implementations must embed UnimplementedTTSServer
// for forward compatibility.
type TTSServer interface {
	TTS(grpc.BidiStreamingServer[TTSRequest, TTSResponse]) error
	mustEmbedUnimplementedTTSServer()
}

// UnimplementedTTSServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTTSServer struct{}

func (UnimplementedTTSServer) TTS(grpc.BidiStreamingServer[TTSRequest, TTSResponse]) error {
	return status.Errorf(codes.Unimplemented, "method TTS not implemented")
}
func (UnimplementedTTSServer) mustEmbedUnimplementedTTSServer() {}
func (UnimplementedTTSServer) testEmbeddedByValue()             {}

// UnsafeTTSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TTSServer will
// result in compilation errors.
type UnsafeTTSServer interface {
	mustEmbedUnimplementedTTSServer()
}

func RegisterTTSServer(s grpc.ServiceRegistrar, srv TTSServer) {
	// If the following call pancis, it indicates UnimplementedTTSServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TTS_ServiceDesc, srv)
}

func _TTS_TTS_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TTSServer).TTS(&grpc.GenericServerStream[TTSRequest, TTSResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type TTS_TTSServer = grpc.BidiStreamingServer[TTSRequest, TTSResponse]

// TTS_ServiceDesc is the grpc.ServiceDesc for TTS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TTS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "doll.tts.TTS",
	HandlerType: (*TTSServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TTS",
			Handler:       _TTS_TTS_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "tts.proto",
}
