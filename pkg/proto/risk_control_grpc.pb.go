// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: risk_control.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RiskControl_RiskControl_FullMethodName = "/doll.risk_control.RiskControl/RiskControl"
)

// RiskControlClient is the client API for RiskControl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RiskControlClient interface {
	RiskControl(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[RiskControlRequest, RiskControlResponse], error)
}

type riskControlClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskControlClient(cc grpc.ClientConnInterface) RiskControlClient {
	return &riskControlClient{cc}
}

func (c *riskControlClient) RiskControl(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[RiskControlRequest, RiskControlResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &RiskControl_ServiceDesc.Streams[0], RiskControl_RiskControl_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[RiskControlRequest, RiskControlResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type RiskControl_RiskControlClient = grpc.BidiStreamingClient[RiskControlRequest, RiskControlResponse]

// RiskControlServer is the server API for RiskControl service.
// All implementations must embed UnimplementedRiskControlServer
// for forward compatibility.
type RiskControlServer interface {
	RiskControl(grpc.BidiStreamingServer[RiskControlRequest, RiskControlResponse]) error
	mustEmbedUnimplementedRiskControlServer()
}

// UnimplementedRiskControlServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRiskControlServer struct{}

func (UnimplementedRiskControlServer) RiskControl(grpc.BidiStreamingServer[RiskControlRequest, RiskControlResponse]) error {
	return status.Errorf(codes.Unimplemented, "method RiskControl not implemented")
}
func (UnimplementedRiskControlServer) mustEmbedUnimplementedRiskControlServer() {}
func (UnimplementedRiskControlServer) testEmbeddedByValue()                     {}

// UnsafeRiskControlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RiskControlServer will
// result in compilation errors.
type UnsafeRiskControlServer interface {
	mustEmbedUnimplementedRiskControlServer()
}

func RegisterRiskControlServer(s grpc.ServiceRegistrar, srv RiskControlServer) {
	// If the following call pancis, it indicates UnimplementedRiskControlServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RiskControl_ServiceDesc, srv)
}

func _RiskControl_RiskControl_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RiskControlServer).RiskControl(&grpc.GenericServerStream[RiskControlRequest, RiskControlResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type RiskControl_RiskControlServer = grpc.BidiStreamingServer[RiskControlRequest, RiskControlResponse]

// RiskControl_ServiceDesc is the grpc.ServiceDesc for RiskControl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RiskControl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "doll.risk_control.RiskControl",
	HandlerType: (*RiskControlServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RiskControl",
			Handler:       _RiskControl_RiskControl_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "risk_control.proto",
}
