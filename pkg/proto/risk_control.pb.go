// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: risk_control.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RiskTextType int32

const (
	RiskTextType_RISK_PROMPT   RiskTextType = 0
	RiskTextType_RISK_RESPONSE RiskTextType = 1
)

// Enum value maps for RiskTextType.
var (
	RiskTextType_name = map[int32]string{
		0: "RISK_PROMPT",
		1: "RISK_RESPONSE",
	}
	RiskTextType_value = map[string]int32{
		"RISK_PROMPT":   0,
		"RISK_RESPONSE": 1,
	}
)

func (x RiskTextType) Enum() *RiskTextType {
	p := new(RiskTextType)
	*p = x
	return p
}

func (x RiskTextType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskTextType) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_control_proto_enumTypes[0].Descriptor()
}

func (RiskTextType) Type() protoreflect.EnumType {
	return &file_risk_control_proto_enumTypes[0]
}

func (x RiskTextType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskTextType.Descriptor instead.
func (RiskTextType) EnumDescriptor() ([]byte, []int) {
	return file_risk_control_proto_rawDescGZIP(), []int{0}
}

type RiskControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RiskTextType  RiskTextType           `protobuf:"varint,3,opt,name=risk_text_type,json=riskTextType,proto3,enum=doll.risk_control.RiskTextType" json:"risk_text_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RiskControlRequest) Reset() {
	*x = RiskControlRequest{}
	mi := &file_risk_control_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskControlRequest) ProtoMessage() {}

func (x *RiskControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_control_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskControlRequest.ProtoReflect.Descriptor instead.
func (*RiskControlRequest) Descriptor() ([]byte, []int) {
	return file_risk_control_proto_rawDescGZIP(), []int{0}
}

func (x *RiskControlRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *RiskControlRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RiskControlRequest) GetRiskTextType() RiskTextType {
	if x != nil {
		return x.RiskTextType
	}
	return RiskTextType_RISK_PROMPT
}

type RiskControlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	IsRisk        bool                   `protobuf:"varint,2,opt,name=is_risk,json=isRisk,proto3" json:"is_risk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RiskControlResponse) Reset() {
	*x = RiskControlResponse{}
	mi := &file_risk_control_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskControlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskControlResponse) ProtoMessage() {}

func (x *RiskControlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_control_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskControlResponse.ProtoReflect.Descriptor instead.
func (*RiskControlResponse) Descriptor() ([]byte, []int) {
	return file_risk_control_proto_rawDescGZIP(), []int{1}
}

func (x *RiskControlResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RiskControlResponse) GetIsRisk() bool {
	if x != nil {
		return x.IsRisk
	}
	return false
}

var File_risk_control_proto protoreflect.FileDescriptor

const file_risk_control_proto_rawDesc = "" +
	"\n" +
	"\x12risk_control.proto\x12\x11doll.risk_control\"\x88\x01\n" +
	"\x12RiskControlRequest\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12E\n" +
	"\x0erisk_text_type\x18\x03 \x01(\x0e2\x1f.doll.risk_control.RiskTextTypeR\friskTextType\"M\n" +
	"\x13RiskControlResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\ais_risk\x18\x02 \x01(\bR\x06isRisk*2\n" +
	"\fRiskTextType\x12\x0f\n" +
	"\vRISK_PROMPT\x10\x00\x12\x11\n" +
	"\rRISK_RESPONSE\x10\x012o\n" +
	"\vRiskControl\x12`\n" +
	"\vRiskControl\x12%.doll.risk_control.RiskControlRequest\x1a&.doll.risk_control.RiskControlResponse(\x010\x01B\x12Z\x10aigc.proto;protob\x06proto3"

var (
	file_risk_control_proto_rawDescOnce sync.Once
	file_risk_control_proto_rawDescData []byte
)

func file_risk_control_proto_rawDescGZIP() []byte {
	file_risk_control_proto_rawDescOnce.Do(func() {
		file_risk_control_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_risk_control_proto_rawDesc), len(file_risk_control_proto_rawDesc)))
	})
	return file_risk_control_proto_rawDescData
}

var file_risk_control_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_risk_control_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_risk_control_proto_goTypes = []any{
	(RiskTextType)(0),           // 0: doll.risk_control.RiskTextType
	(*RiskControlRequest)(nil),  // 1: doll.risk_control.RiskControlRequest
	(*RiskControlResponse)(nil), // 2: doll.risk_control.RiskControlResponse
}
var file_risk_control_proto_depIdxs = []int32{
	0, // 0: doll.risk_control.RiskControlRequest.risk_text_type:type_name -> doll.risk_control.RiskTextType
	1, // 1: doll.risk_control.RiskControl.RiskControl:input_type -> doll.risk_control.RiskControlRequest
	2, // 2: doll.risk_control.RiskControl.RiskControl:output_type -> doll.risk_control.RiskControlResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_risk_control_proto_init() }
func file_risk_control_proto_init() {
	if File_risk_control_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_risk_control_proto_rawDesc), len(file_risk_control_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_risk_control_proto_goTypes,
		DependencyIndexes: file_risk_control_proto_depIdxs,
		EnumInfos:         file_risk_control_proto_enumTypes,
		MessageInfos:      file_risk_control_proto_msgTypes,
	}.Build()
	File_risk_control_proto = out.File
	file_risk_control_proto_goTypes = nil
	file_risk_control_proto_depIdxs = nil
}
