// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: llm.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LLMReqType int32

const (
	LLMReqType_USER LLMReqType = 0
	LLMReqType_AIGC LLMReqType = 1
)

// Enum value maps for LLMReqType.
var (
	LLMReqType_name = map[int32]string{
		0: "USER",
		1: "AIGC",
	}
	LLMReqType_value = map[string]int32{
		"USER": 0,
		"AIGC": 1,
	}
)

func (x LLMReqType) Enum() *LLMReqType {
	p := new(LLMReqType)
	*p = x
	return p
}

func (x LLMReqType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LLMReqType) Descriptor() protoreflect.EnumDescriptor {
	return file_llm_proto_enumTypes[0].Descriptor()
}

func (LLMReqType) Type() protoreflect.EnumType {
	return &file_llm_proto_enumTypes[0]
}

func (x LLMReqType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LLMReqType.Descriptor instead.
func (LLMReqType) EnumDescriptor() ([]byte, []int) {
	return file_llm_proto_rawDescGZIP(), []int{0}
}

type LLMRespType int32

const (
	LLMRespType_NORMAL LLMRespType = 0
	LLMRespType_RESUME LLMRespType = 1
	LLMRespType_RESET  LLMRespType = 2
)

// Enum value maps for LLMRespType.
var (
	LLMRespType_name = map[int32]string{
		0: "NORMAL",
		1: "RESUME",
		2: "RESET",
	}
	LLMRespType_value = map[string]int32{
		"NORMAL": 0,
		"RESUME": 1,
		"RESET":  2,
	}
)

func (x LLMRespType) Enum() *LLMRespType {
	p := new(LLMRespType)
	*p = x
	return p
}

func (x LLMRespType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LLMRespType) Descriptor() protoreflect.EnumDescriptor {
	return file_llm_proto_enumTypes[1].Descriptor()
}

func (LLMRespType) Type() protoreflect.EnumType {
	return &file_llm_proto_enumTypes[1]
}

func (x LLMRespType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LLMRespType.Descriptor instead.
func (LLMRespType) EnumDescriptor() ([]byte, []int) {
	return file_llm_proto_rawDescGZIP(), []int{1}
}

type LLMRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsFinal       bool                   `protobuf:"varint,1,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"` // asr 识别是否一句话结束
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	ToolCall      string                 `protobuf:"bytes,3,opt,name=tool_call,json=toolCall,proto3" json:"tool_call,omitempty"` // json: {"fn1": "fn_msg", "fn2": "fn_msg"}
	Type          LLMReqType             `protobuf:"varint,4,opt,name=type,proto3,enum=doll.llm.LLMReqType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LLMRequest) Reset() {
	*x = LLMRequest{}
	mi := &file_llm_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMRequest) ProtoMessage() {}

func (x *LLMRequest) ProtoReflect() protoreflect.Message {
	mi := &file_llm_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMRequest.ProtoReflect.Descriptor instead.
func (*LLMRequest) Descriptor() ([]byte, []int) {
	return file_llm_proto_rawDescGZIP(), []int{0}
}

func (x *LLMRequest) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *LLMRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *LLMRequest) GetToolCall() string {
	if x != nil {
		return x.ToolCall
	}
	return ""
}

func (x *LLMRequest) GetType() LLMReqType {
	if x != nil {
		return x.Type
	}
	return LLMReqType_USER
}

type LLMResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Type            LLMRespType            `protobuf:"varint,1,opt,name=type,proto3,enum=doll.llm.LLMRespType" json:"type,omitempty"`
	Content         string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	MsgIndex        int32                  `protobuf:"varint,3,opt,name=msg_index,json=msgIndex,proto3" json:"msg_index,omitempty"`
	MsgSegmentIndex int32                  `protobuf:"varint,4,opt,name=msg_segment_index,json=msgSegmentIndex,proto3" json:"msg_segment_index,omitempty"`
	IsFinal         bool                   `protobuf:"varint,5,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	ToolCall        string                 `protobuf:"bytes,6,opt,name=tool_call,json=toolCall,proto3" json:"tool_call,omitempty"` // json: {"fn1": "fn_msg", "fn2": "fn_msg"}
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *LLMResponse) Reset() {
	*x = LLMResponse{}
	mi := &file_llm_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMResponse) ProtoMessage() {}

func (x *LLMResponse) ProtoReflect() protoreflect.Message {
	mi := &file_llm_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMResponse.ProtoReflect.Descriptor instead.
func (*LLMResponse) Descriptor() ([]byte, []int) {
	return file_llm_proto_rawDescGZIP(), []int{1}
}

func (x *LLMResponse) GetType() LLMRespType {
	if x != nil {
		return x.Type
	}
	return LLMRespType_NORMAL
}

func (x *LLMResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *LLMResponse) GetMsgIndex() int32 {
	if x != nil {
		return x.MsgIndex
	}
	return 0
}

func (x *LLMResponse) GetMsgSegmentIndex() int32 {
	if x != nil {
		return x.MsgSegmentIndex
	}
	return 0
}

func (x *LLMResponse) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *LLMResponse) GetToolCall() string {
	if x != nil {
		return x.ToolCall
	}
	return ""
}

var File_llm_proto protoreflect.FileDescriptor

const file_llm_proto_rawDesc = "" +
	"\n" +
	"\tllm.proto\x12\bdoll.llm\"\x88\x01\n" +
	"\n" +
	"LLMRequest\x12\x19\n" +
	"\bis_final\x18\x01 \x01(\bR\aisFinal\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1b\n" +
	"\ttool_call\x18\x03 \x01(\tR\btoolCall\x12(\n" +
	"\x04type\x18\x04 \x01(\x0e2\x14.doll.llm.LLMReqTypeR\x04type\"\xd3\x01\n" +
	"\vLLMResponse\x12)\n" +
	"\x04type\x18\x01 \x01(\x0e2\x15.doll.llm.LLMRespTypeR\x04type\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1b\n" +
	"\tmsg_index\x18\x03 \x01(\x05R\bmsgIndex\x12*\n" +
	"\x11msg_segment_index\x18\x04 \x01(\x05R\x0fmsgSegmentIndex\x12\x19\n" +
	"\bis_final\x18\x05 \x01(\bR\aisFinal\x12\x1b\n" +
	"\ttool_call\x18\x06 \x01(\tR\btoolCall* \n" +
	"\n" +
	"LLMReqType\x12\b\n" +
	"\x04USER\x10\x00\x12\b\n" +
	"\x04AIGC\x10\x01*0\n" +
	"\vLLMRespType\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x00\x12\n" +
	"\n" +
	"\x06RESUME\x10\x01\x12\t\n" +
	"\x05RESET\x10\x022B\n" +
	"\aLLMChat\x127\n" +
	"\x04Chat\x12\x14.doll.llm.LLMRequest\x1a\x15.doll.llm.LLMResponse(\x010\x01B\x12Z\x10aigc.proto;protob\x06proto3"

var (
	file_llm_proto_rawDescOnce sync.Once
	file_llm_proto_rawDescData []byte
)

func file_llm_proto_rawDescGZIP() []byte {
	file_llm_proto_rawDescOnce.Do(func() {
		file_llm_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_llm_proto_rawDesc), len(file_llm_proto_rawDesc)))
	})
	return file_llm_proto_rawDescData
}

var file_llm_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_llm_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_llm_proto_goTypes = []any{
	(LLMReqType)(0),     // 0: doll.llm.LLMReqType
	(LLMRespType)(0),    // 1: doll.llm.LLMRespType
	(*LLMRequest)(nil),  // 2: doll.llm.LLMRequest
	(*LLMResponse)(nil), // 3: doll.llm.LLMResponse
}
var file_llm_proto_depIdxs = []int32{
	0, // 0: doll.llm.LLMRequest.type:type_name -> doll.llm.LLMReqType
	1, // 1: doll.llm.LLMResponse.type:type_name -> doll.llm.LLMRespType
	2, // 2: doll.llm.LLMChat.Chat:input_type -> doll.llm.LLMRequest
	3, // 3: doll.llm.LLMChat.Chat:output_type -> doll.llm.LLMResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_llm_proto_init() }
func file_llm_proto_init() {
	if File_llm_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_llm_proto_rawDesc), len(file_llm_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_llm_proto_goTypes,
		DependencyIndexes: file_llm_proto_depIdxs,
		EnumInfos:         file_llm_proto_enumTypes,
		MessageInfos:      file_llm_proto_msgTypes,
	}.Build()
	File_llm_proto = out.File
	file_llm_proto_goTypes = nil
	file_llm_proto_depIdxs = nil
}
