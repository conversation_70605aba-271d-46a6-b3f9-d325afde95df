// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: asr.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ASRErrorCode int32

const (
	ASRErrorCode_ASR_ERROR_CODE_OK                  ASRErrorCode = 0
	ASRErrorCode_ASR_ERROR_CODE_RECEIVE_AUDIO_ERROR ASRErrorCode = 101
)

// Enum value maps for ASRErrorCode.
var (
	ASRErrorCode_name = map[int32]string{
		0:   "ASR_ERROR_CODE_OK",
		101: "ASR_ERROR_CODE_RECEIVE_AUDIO_ERROR",
	}
	ASRErrorCode_value = map[string]int32{
		"ASR_ERROR_CODE_OK":                  0,
		"ASR_ERROR_CODE_RECEIVE_AUDIO_ERROR": 101,
	}
)

func (x ASRErrorCode) Enum() *ASRErrorCode {
	p := new(ASRErrorCode)
	*p = x
	return p
}

func (x ASRErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ASRErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_asr_proto_enumTypes[0].Descriptor()
}

func (ASRErrorCode) Type() protoreflect.EnumType {
	return &file_asr_proto_enumTypes[0]
}

func (x ASRErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ASRErrorCode.Descriptor instead.
func (ASRErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_asr_proto_rawDescGZIP(), []int{0}
}

type ASRRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Audio         []byte                 `protobuf:"bytes,1,opt,name=audio,proto3" json:"audio,omitempty"` // 16k sample rate
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ASRRequest) Reset() {
	*x = ASRRequest{}
	mi := &file_asr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ASRRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ASRRequest) ProtoMessage() {}

func (x *ASRRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ASRRequest.ProtoReflect.Descriptor instead.
func (*ASRRequest) Descriptor() ([]byte, []int) {
	return file_asr_proto_rawDescGZIP(), []int{0}
}

func (x *ASRRequest) GetAudio() []byte {
	if x != nil {
		return x.Audio
	}
	return nil
}

type ASRResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	IsFinal       bool                   `protobuf:"varint,2,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	ErrorCode     ASRErrorCode           `protobuf:"varint,100,opt,name=error_code,json=errorCode,proto3,enum=doll.asr.ASRErrorCode" json:"error_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ASRResponse) Reset() {
	*x = ASRResponse{}
	mi := &file_asr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ASRResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ASRResponse) ProtoMessage() {}

func (x *ASRResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ASRResponse.ProtoReflect.Descriptor instead.
func (*ASRResponse) Descriptor() ([]byte, []int) {
	return file_asr_proto_rawDescGZIP(), []int{1}
}

func (x *ASRResponse) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ASRResponse) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *ASRResponse) GetErrorCode() ASRErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return ASRErrorCode_ASR_ERROR_CODE_OK
}

var File_asr_proto protoreflect.FileDescriptor

const file_asr_proto_rawDesc = "" +
	"\n" +
	"\tasr.proto\x12\bdoll.asr\"\"\n" +
	"\n" +
	"ASRRequest\x12\x14\n" +
	"\x05audio\x18\x01 \x01(\fR\x05audio\"s\n" +
	"\vASRResponse\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x19\n" +
	"\bis_final\x18\x02 \x01(\bR\aisFinal\x125\n" +
	"\n" +
	"error_code\x18d \x01(\x0e2\x16.doll.asr.ASRErrorCodeR\terrorCode*M\n" +
	"\fASRErrorCode\x12\x15\n" +
	"\x11ASR_ERROR_CODE_OK\x10\x00\x12&\n" +
	"\"ASR_ERROR_CODE_RECEIVE_AUDIO_ERROR\x10e2=\n" +
	"\x03ASR\x126\n" +
	"\x03ASR\x12\x14.doll.asr.ASRRequest\x1a\x15.doll.asr.ASRResponse(\x010\x01B\x12Z\x10aigc.proto;protob\x06proto3"

var (
	file_asr_proto_rawDescOnce sync.Once
	file_asr_proto_rawDescData []byte
)

func file_asr_proto_rawDescGZIP() []byte {
	file_asr_proto_rawDescOnce.Do(func() {
		file_asr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_asr_proto_rawDesc), len(file_asr_proto_rawDesc)))
	})
	return file_asr_proto_rawDescData
}

var file_asr_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_asr_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_asr_proto_goTypes = []any{
	(ASRErrorCode)(0),   // 0: doll.asr.ASRErrorCode
	(*ASRRequest)(nil),  // 1: doll.asr.ASRRequest
	(*ASRResponse)(nil), // 2: doll.asr.ASRResponse
}
var file_asr_proto_depIdxs = []int32{
	0, // 0: doll.asr.ASRResponse.error_code:type_name -> doll.asr.ASRErrorCode
	1, // 1: doll.asr.ASR.ASR:input_type -> doll.asr.ASRRequest
	2, // 2: doll.asr.ASR.ASR:output_type -> doll.asr.ASRResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_asr_proto_init() }
func file_asr_proto_init() {
	if File_asr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_asr_proto_rawDesc), len(file_asr_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_asr_proto_goTypes,
		DependencyIndexes: file_asr_proto_depIdxs,
		EnumInfos:         file_asr_proto_enumTypes,
		MessageInfos:      file_asr_proto_msgTypes,
	}.Build()
	File_asr_proto = out.File
	file_asr_proto_goTypes = nil
	file_asr_proto_depIdxs = nil
}
