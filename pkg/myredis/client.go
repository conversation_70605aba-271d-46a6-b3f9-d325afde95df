package myredis

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

var (
	client *redis.Client
	once   sync.Once
)

// Init 初始化Redis客户端
func Init(cfg *config.RedisConfig) error {
	var err error
	once.Do(func() {
		addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)

		client = redis.NewClient(&redis.Options{
			Addr:         addr,
			Password:     cfg.Password,
			DB:           cfg.Database,
			PoolSize:     cfg.PoolSize,
			MinIdleConns: cfg.MinIdleConns,
			MaxRetries:   cfg.MaxRetries,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
		})

		// 测试连接
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if pingErr := client.Ping(ctx).Err(); pingErr != nil {
			err = fmt.Errorf("Redis连接测试失败: %w", pingErr)
			return
		}

		logger.Info("Redis客户端初始化成功",
			zap.String("addr", addr),
			zap.Int("database", cfg.Database),
			zap.Int("pool_size", cfg.PoolSize),
		)
	})

	return err
}

// GetClient 获取Redis客户端实例
func GetClient() *redis.Client {
	if client == nil {
		logger.Error("Redis客户端未初始化")
	}
	return client
}

// Close 关闭Redis客户端
func Close() error {
	if client != nil {
		logger.Info("正在关闭Redis客户端")
		return client.Close()
	}
	return nil
}

// Health 检查Redis连接健康状态
func Health(ctx context.Context) error {
	if client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return client.Ping(ctx).Err()
}
