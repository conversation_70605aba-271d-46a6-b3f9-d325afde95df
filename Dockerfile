# AIGC Server Dockerfile
# 基于Ubuntu 22.04作为基础镜像，确保C/C++库兼容性和GLIBC版本支持
FROM ubuntu:22.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    libc6 \
    libssl3 \
    libstdc++6 \
    libatomic1 \
    libgcc-s1 \
    net-tools \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 创建用户和组（显式指定UID/GID与K8s securityContext保持一致）
RUN groupadd -g 1000 aigc && \
    useradd -g 1000 -l -m -s /bin/bash -u 1000 aigc

# 复制动态库文件
COPY lib/ /app/lib/

# 复制可执行文件
COPY aigc-server-main /app/aigc-server-main
COPY aigc-server-doll /app/aigc-server-doll

# 注意：不再复制配置文件，完全依赖ConfigMap
# COPY configs/ /app/configs/  # ← 删除这一行

# 设置执行权限
RUN chmod +x /app/aigc-server-main /app/aigc-server-doll

# 创建必要的目录（但不包含配置文件）
RUN mkdir -p /app/logs /app/configs && \
    chown -R aigc:aigc /app

# 设置动态库路径
ENV LD_LIBRARY_PATH=/app/lib:$LD_LIBRARY_PATH

# 添加构建参数支持调试模式
ARG DEBUG_MODE=false

# 将构建参数转换为环境变量
ENV DEBUG_MODE=$DEBUG_MODE

# 切换到非root用户
USER aigc

# 暴露端口
EXPOSE 8970 8971

# 根据调试模式设置不同的健康检查和启动命令
# 生产模式：检查主进程是否在监听指定端口
# 调试模式：跳过健康检查（因为应用不会自动启动）
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD if [ "$DEBUG_MODE" = "true" ]; then exit 0; else netstat -an | grep -q ":8970.*LISTEN" && netstat -an | grep -q ":8971.*LISTEN" || exit 1; fi

# 启动命令
# 生产模式：直接启动应用
# 调试模式：显示提示信息并等待手动启动
CMD if [ "$DEBUG_MODE" = "true" ]; then \
        echo "🐛 调试模式已启动！"; \
        echo "💡 请使用以下命令手动启动应用:"; \
        echo "   cd /app && ./aigc-server-main --env prod"; \
        echo ""; \
        echo "📝 可用的调试命令:"; \
        echo "   ls -la /app/          # 查看应用文件"; \
        echo "   cat /app/configs/prod.yaml  # 查看配置"; \
        echo "   ./aigc-server-main --help   # 查看帮助"; \
        echo ""; \
        echo "⏳ 容器将保持运行状态等待您的操作..."; \
        while true; do sleep 3600; done; \
    else \
        exec /app/aigc-server-main --env prod; \
    fi 