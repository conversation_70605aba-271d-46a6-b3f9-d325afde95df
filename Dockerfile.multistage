# AIGC Server 多阶段构建 Dockerfile
# 第一阶段：编译环境
FROM ubuntu:22.04 AS builder

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装编译工具和依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    make \
    git \
    golang-1.21 \
    ca-certificates \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 设置Go环境
ENV PATH="/usr/lib/go-1.21/bin:${PATH}"
ENV GOPATH="/go"
ENV GOROOT="/usr/lib/go-1.21"

# 创建工作目录
WORKDIR /build

# 复制源代码
COPY . .

# 设置Go模块
RUN go mod download

# 设置编译环境变量
ENV CGO_ENABLED=1
ENV GOOS=linux
ENV GOARCH=amd64
ENV HEADER_FILE_PATH="/build/sdk/VolcEngineRTC_Linux_3.60.1.29857238_x86_64_Release/include"
ENV CGO_CXXFLAGS="-std=c++14 -I${HEADER_FILE_PATH}"
ENV CGO_LDFLAGS="-L/build/lib -latomic -lbytertc_ffmpeg_audio_extension -lbytenn -lbytertc_vp8codec_extension -lbytertc_nico_extension -lRTCFFmpeg -lbytertc_fdk-aac_extension -lVolcEngineRTC -lVolcEngineRTCWrapper"

# 编译应用程序
RUN echo "编译主进程..." && \
    go build -o aigc-server-main -ldflags "-X main.Version=docker-build -X main.BuildTime=$(date -u '+%Y-%m-%dT%H:%M:%S') -X main.GitCommit=docker" ./cmd/main/ && \
    echo "编译子进程..." && \
    go build -o aigc-server-doll -ldflags "-X main.Version=docker-build -X main.BuildTime=$(date -u '+%Y-%m-%dT%H:%M:%S') -X main.GitCommit=docker" ./cmd/doll/

# 第二阶段：运行环境
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    libc6 \
    libgcc-s1 \
    libstdc++6 \
    libatomic1 \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r aigc && useradd -r -g aigc -d /app -s /bin/bash aigc

# 从构建阶段复制文件
COPY --from=builder /build/aigc-server-main /app/aigc-server-main
COPY --from=builder /build/aigc-server-doll /app/aigc-server-doll
COPY --from=builder /build/lib/ /app/lib/
COPY --from=builder /build/configs/ /app/configs/

# 设置执行权限
RUN chmod +x /app/aigc-server-main /app/aigc-server-doll

# 创建必要的目录
RUN mkdir -p /app/logs && \
    chown -R aigc:aigc /app

# 设置动态库路径
ENV LD_LIBRARY_PATH=/app/lib:$LD_LIBRARY_PATH

# 切换到非root用户
USER aigc

# 暴露端口
EXPOSE 8970 8971

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD netstat -an | grep -q ":8970.*LISTEN" && netstat -an | grep -q ":8971.*LISTEN" || exit 1

# 启动命令
CMD ["/app/aigc-server-main", "--env", "prod"] 