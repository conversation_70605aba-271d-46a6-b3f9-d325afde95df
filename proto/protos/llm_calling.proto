syntax = "proto3";

package doll.llm.calling;

option go_package = "aigc.proto;proto";

import "llm.proto";

enum CallingRole {
    CALLER = 0;
    ACCEPTER = 1;
}
enum LLMCallingStatus {
    WAITING = 0;
    READY = 1;
    OVER = 2;
}

message LLMCallingBindRoleRequest {
    CallingRole role = 1;
}

message LLMCallingBindRoleResponse {
    LLMCallingStatus status = 1;
    string roles = 2; // [{uid: role}, {uid: role}]
}

message LLMCallingRequest {
    LLMRequest llm_request = 1;
}

message LLMCallingResponse {
    LLMResponse llm_response = 1;
}