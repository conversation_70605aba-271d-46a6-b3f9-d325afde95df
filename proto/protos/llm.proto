syntax = "proto3";

package doll.llm;

option go_package = "aigc.proto;proto";

enum LLMReqType {
  USER = 0;
  AIGC = 1;
}

enum LLMRespType {
  NORMAL = 0;
  RESUME = 1;
  RESET = 2;
}

message LLMRequest {
  bool is_final = 1; // asr 识别是否一句话结束
  string content = 2;
  string tool_call = 3; // json: {"fn1": "fn_msg", "fn2": "fn_msg"}
  LLMReqType type = 4;
}

message LLMResponse {
  LLMRespType type = 1;
  string content = 2;
  int32  msg_index = 3;
  int32  msg_segment_index = 4;
  bool   is_final = 5;
  string tool_call = 6; // json: {"fn1": "fn_msg", "fn2": "fn_msg"}
}

service LLMChat {
  rpc Chat(stream LLMRequest) returns (stream LLMResponse);
}