syntax = "proto3";

package doll.tts;

option go_package = "aigc.proto;proto";

message TTSRequest {
  string text = 1;
  string voice_type = 2;
  int32 msg_index = 3;
  int32 msg_segment_index = 4;
  bool is_custom_tts = 5;
  int32 request_id = 6;
}   

message TTSResponse {
  bytes audio = 1; // 16k sample rate
  int32 msg_index = 2;
  int32 msg_segment_index = 3;
  int32 request_id = 4;
  bool is_final = 5;
  int32 error_code = 10;
}

service TTS {
  rpc TTS(stream TTSRequest) returns (stream TTSResponse);
}
