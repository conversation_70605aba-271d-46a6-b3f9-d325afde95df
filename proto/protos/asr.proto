syntax = "proto3";

package doll.asr;

option go_package = "aigc.proto;proto";

enum ASRErrorCode {
  ASR_ERROR_CODE_OK = 0;
  ASR_ERROR_CODE_RECEIVE_AUDIO_ERROR = 101;
}

message ASRRequest {
  bytes audio = 1; // 16k sample rate
}

message ASRResponse {
  string text = 1;
  bool is_final = 2;
  ASRErrorCode error_code = 100;
}

service ASR {
  rpc ASR(stream ASRRequest) returns (stream ASRResponse);
}
