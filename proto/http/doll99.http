
// Doll99 API
// This file contains HTTP requests for the Doll99 API.
// The API is used to manage dolls and their information.

@BaseUrl=http://115.190.73.213:8912
# @BaseUrl=https://ap.fifthday.bid
@Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJkMGplMHEzbjRjdGJqajFrcjNtZyJ9.oGCmRK1PWWa8surJ2vcsOijjIl0vqumpZq89yoB0umY

@DollID=zl

###

// 检查更新
POST {{BaseUrl}}/app/ver/check-update
Content-Type: application/json

{
  "ver": "1.0.0",
  "platform": "android", // android, ios
}

{
  "code": 0,
  "message": "success",
  "data": {
    "flag": "noupdate", // noupdate: 不需要更新, needupdate: 需要更新, forceupdate: 强制更新
    "verNew": "1.0.0",
    "updateType": "download", // download: 下载安装更新 market: 跳转应用市场更新
    "url": "https://www.baidu.com", // updateType 为 download 时，下载地址, updateType 为 market 时，跳转地址
    "changelog": "更新内容",
  }
}

###

// 请求验证码
POST {{BaseUrl}}/app/login/get-verifycode
Content-Type: application/json

{
  "phone": "18600000000"
}

###

// 验证验证码
POST {{BaseUrl}}/app/login/verify-verifycode
Content-Type: application/json

{
  "phone": "18600000000",
  "verifyCode": "6470"
}

###

// 刷新 token
POST {{BaseUrl}}/app/user/refresh-token
Content-Type: application/json
Authorization: Bearer {{Token}}

{
}

### 
// 绑定小玩偶
POST {{BaseUrl}}/app/doll/bind
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success"
}

###
// 解绑小玩偶
POST {{BaseUrl}}/app/doll/unbind
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success"
}

### 
// 获取小玩偶列表
// go 
# type DollState struct {
# 	Battery    int    `json:"battery"`
# 	Volume     int    `json:"volume"`
# 	IsCharging bool   `json:"isCharging"`
# 	State      string `json:"state"`
# 	Unreads    int    `json:"unreads"`
# 	IsSounding bool   `json:"isSounding"`
# }

# type DollInfo struct {
# 	DollId         string    `json:"dollId"`
# 	DollType       string    `json:"dollType"`
# 	DollName       string    `json:"dollName"`
# 	DollDesc       string    `json:"dollDesc"`
# 	UsedRoleId     string    `json:"usedRoleId"`
# 	OriginalRoleId string    `json:"originalRoleId"`
# 	DollState      DollState `json:"dollState"`
# }
POST {{BaseUrl}}/app/doll/infos
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

{
  "code": 0,
  "message": "success",
  "dollInfos": [
    {
      "dollId" : "zl",
      "dollType" : "doll",
      "dollName" : "小酸奶",
      "dollDesc" : "小酸奶是一个可爱的小玩偶",
      "usedRoleId" : "r_1",
      "originalRoleId" : "r_1",
      "dollState" : {
        "dollId" : "zl",
        "battery": 50,
        "volume": 50,
        "isCharging": true,
        "state": "idle",
        "unreads": 0,
        "isSounding": false,
        "version": "1.0.0"
      }
    },
  ]
}

###

// 存储聊天内容
POST {{BaseUrl}}/doll/msg/save
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "query": "我今天不开心",
  "answer": "我陪你一起玩吧"
}

###

// 获取聊天内容
POST {{BaseUrl}}/app/msg/get-doll
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "limit": 1,
  "timestamp": 0,
  "op": "before"
}

###

// 获取聊天总结
POST {{BaseUrl}}/app/msg/get-summary
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 获取角色列表
POST {{BaseUrl}}/app/role/get-role
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success",
  "data": {
    "roles": [
      {
        "roleId": "d154m8jn4ctf3uv0bnpg",
        "roleStory": "我是一个超级可爱、能说会道的小玩偶，名字叫小酸奶，由人工智能生产力科技创造的智能玩偶，性格超级活泼开朗哟。",
        "roleInfo": "",
        "timbreId": "BV415_streaming",
        "roleName": "小酸奶",
        "timbreName": "小酸奶"
      }
    ]
  }
}

###

// 切换角色
POST {{BaseUrl}}/app/role/change
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "newRoleId": "r_2"
}

###

// 删除角色
POST {{BaseUrl}}/app/role/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "roleId": "{{DollID}}_d0gvbi3n4ct31vgt7mig"
}

###

// 获取硬件状态（电量、音量等）
POST {{BaseUrl}}/app/doll/states
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollIds": ["zl", "doll66"]
}

{
  "code": 0,
  "message": "success",
  "data": {
    "dollStates" : [{
      "dollId" : "zl",
      "battery": 50,
      "volume": 50,
      "isCharging": true,
      "state": "idle",
      "unreads": 0,
      "isSounding": false,
      "version": "1.0.0"
    }]
  }
}

### 

// 一键生成角色内容
POST {{BaseUrl}}/app/role/gen
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661",
  "roleName": "小酸奶"
}

{
  "roleName": "小酸奶",
  "timbreId": "original_1",
  "timbreName": "原声",
  "roleStory": "我是一个智慧的故事讲述者",
  "roleInfo": "" // jsonString
}

###

// 创建/修改自定义角色
POST {{BaseUrl}}/app/role/upsert
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661",
  "roleId": "",
  "roleName": "小酸奶",
  "roleStory": "我是一个智慧的故事讲述者",
  "timbreId": "original_1",
  "roleInfo": "" // jsonString
}

###

// 获取音色列表
POST {{BaseUrl}}/app/timbre/list
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 删除音色
POST {{BaseUrl}}/app/timbre/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "timbreId": "t_001"
}

###

// 录制音色（voice 为 base64 编码音频，最长 20 秒）
POST {{BaseUrl}}/app/timbre/record
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "voice": "BASE64_ENCODED_AUDIO"
}

###

// 家长助手聊天
GET {{BaseUrl}}/app/msg/chat-parent-sse?dollId={{DollID}}&queryType=text&query=孩子今天在干嘛？
Content-Type: application/json
Authorization: Bearer {{Token}}

###

// 获取家长助手消息记录
POST {{BaseUrl}}/app/msg/get-parent
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "offset": 0,
  "limit": 20
}

###

// 获取订阅链接
POST {{BaseUrl}}/app/setting/subscribe
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 获取知识库导入链接
POST {{BaseUrl}}/app/setting/repository
Content-Type: application/json
Authorization: Bearer {{Token}}

{}


###

// 退出登录
POST {{BaseUrl}}/app/setting/quit-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 注销账号
POST {{BaseUrl}}/app/setting/delete-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

### 
// 排行榜
POST {{BaseUrl}}/app/rank/get-rank
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "type": ["total", "idiom", "english", "voice", "puzzle", "wisdom"]
}

{
  "code": 0,
  "message": "success",
    "data": {
      "rank": [
      {
        "type": "total",
        "friends": [
          {
            "rank": 1,
            "nickname": "张三",
            "score": 100,
            "isSelf": true
          },
          {
            "rank": 1,
            "nickname": "张三",
            "score": 100
          }
        ],
        "area": [
          {
            "rank": 1,
            "nickname": "张三",
            "score": 100,
            "isSelf": true
          },
          {
            "rank": 1,
            "nickname": "张三",
            "score": 100
          }
        ]
      }]
    }
}

###

// 家长助手提示词
POST {{BaseUrl}}/app/msg/get-parent-prompts
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "prompts":[
#       {
#         "id": 1,
#         "prompt": "你好，我是家长助手，有什么可以帮你的吗？"
#       }
#     ] 
#   }
# }

###

// 修改小朋友信息
POST {{BaseUrl}}/app/child/update-info
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "name": "小明",
  "birthday": "2020-01-01"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "name": "小明",
#     "birthday": "2020-01-01"
#   }
# }

###

// 获取小朋友信息
POST {{BaseUrl}}/app/child/get-info
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "name": "小明",
#     "birthday": "2020-01-01"
#   }
# }

###

// 游戏详细内容列表
POST {{BaseUrl}}/app/game/get-game-list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success",
  "data": {
    "gameList": [
      {
        "id": 1,
        "name": "成语接龙",
        "group": "idiom",
        "progress":10
      },
      {
        "id": 2,
        "name": "半词接龙",
        "group": "idiom",
        "progress":10
      },
      {
        "id": 3,
        "name": "英语跟读",
        "group": "english",
        "progress":10
      },
      {
        "id": 4,
        "name": "猜谜",
        "group": "puzzle",
        "progress":10
      },
      {
        "id": 5,
        "name": " 听声识别",
        "group": "voice",
        "progress":10
      },
      {
        "id": 6,
        "name": "智慧大脑",
        "group": "wisdom",
        "progress":10
      }
    ]
  }
}

###

// 获取游戏详细内容
POST {{BaseUrl}}/app/game/get-stage-list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "gameId": 1
}

{
  "code": 0,
  "message": "success",
  "data": {
    "curStageId": 1,
    "stages": [
      {
        "id": 1,
        "name": "等级1",
        "award": 2,
        "step": {
          "totalCount": 10,
          "finCount": 1,
          "lastStepState": 0, // 0: 未解锁 1: 已解锁 2: 成功 3: 失败
        },
      },
      {
        "id": 2,
        "name": "等级2",
        "award": 2,
        "step": {
          "totalCount": 10,
          "finCount": 1,
          "lastStepState": 0, // 0: 未解锁 1: 已解锁 2: 成功 3: 失败
        },
      },
    ]
  }
}

###

// 获取游戏stage详情
POST {{BaseUrl}}/app/game/get-stage-detail
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "gameId": 1,
  "stageId": 1
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "infoTemplate": "你好，我是家长助手，有什么可以帮你的吗？",
#     "chatTemplate": "你好，我是家长助手，有什么可以帮你的吗？"
#   }
# }


###
// 与Doll进行语音聊天
POST {{BaseUrl}}/app/parent/start-voice-chat
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "mode": "parent_voice_chat", // parent_voice_chat
}

{
  "code": 0,
  "message": "success",
  "data": {
    "uid": "",
    "roomId": "1234567890",
    "roomToken": "1234567890",
    "expireTime": "2025-06-26T10:39:01"
  }
}

###

// 获取好友列表
POST {{BaseUrl}}/app/friend/list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success",
  "data": {
    "friends": [
      {
        "dollId": "zl",
        "dollName": "小酸奶",
        "nickname": "张三",
        "age": 3,
      }
    ]
  }
}

###

// 删除好友
POST {{BaseUrl}}/app/friend/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "zl",
  "friendDollID": "AP-FR-SF-SuanNai-99b5bc3e68a4"
}

{
  "code": 0,
  "message": "success",
  "data": {}
}

###
// 获取保存的wifi列表
POST {{BaseUrl}}/app/doll/get-ssid-list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success",
  "data": {
    "ssids": ["wifi1", "wifi2"]
  }
}

###
// 删除保存的wifi
POST {{BaseUrl}}/app/doll/delete-ssid
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "ssid": "wifi1"
}

{
  "code": 0,
  "message": "success",
  "data": {}
}

// OTA:额外的bizserver与aigcserver通信 redis
// 1. 通知确认更新 biz => aigcserver
//    redis channel: 
//    key=doll:ota-update:begin:{{dollId}} 
//    value={
//      "version": "1.0.0",
//      "url": "https://www.baidu.com",
//    }
// 2. 通知进度 aigcserver => biz
//    redis channel: 
//    key=doll:ota-update:process:{{dollId}} 
//    value={
//      "progress": 50,
//      "status":"processing", // processing, success, failed
//    }

###
// 获取硬件是否需要更新
GET {{BaseUrl}}/app/doll/get-ota-update?dollId={{DollID}}

{
  "code": 0,
  "message": "success",
  "data": {
    "flag": "noupdate", // noupdate: 不需要更新, needupdate: 需要更新, forceupdate: 强制更新
    "verCurrent": "1.0.0",
    "verNew": "1.0.0",
    "changelog": "更新内容",
  }
}

### 
// 更新固件，返回进度。需要有超时机制
GET {{BaseUrl}}/app/doll/ota-process-sse?dollId={{DollID}}&version=1.0.0

{
  "code": 0,
  "message": "success",
  "data": {
    "progress": 50,
    "status":"processing", // processing, success, failed
  }
}

###

// 与Doll进行语音聊天
// 调用AIGC Server 内网
@AIGCServerBaseUrl=""
POST {{AIGCServerBaseUrl}}/aigc/room-enter
Content-Type: application/json

{
  "dollId": "{{DollID}}",
  "mode": "parent_voice_chat", // parent_voice_chat
}

{
  "code": 0,
  "message": "success",
  "data": {
    "uid": "",
    "roomId": "1234567890",
    "roomToken": "1234567890",
    "expireTime": "2025-06-26T10:39:01"
  }
}

###
// 请求删除Doll保存的wifi
POST {{AIGCServerBaseUrl}}/aigc/doll/delete-ssid
Content-Type: application/json

{
  "dollId": "{{DollID}}",
  "ssid": "wifi1"
}

{
  "code": 0,
  "message": "success",
  "data": {}
}


//
//  Media Server
// 

@mediaServer = http://115.190.73.213:8960

###
post {{mediaServer}}/media/friend/add
content-type: application/json

{
    "myDollID":"d_1",
    "friendDollID":"d_2"
}

{
    "code": 0,
    "message": "success",
    "data": {
      "isSuccess": true,
      "audio": "http://success.mp3"
    }
}

// media server
// 接通、挂断提示音
###

get {{mediaServer}}/media/call/hint-audio

{
    "code": 0,
    "message": "success",
    "data": {
      "callStartAudio": "http://dialing.mp3",
      "callFinishAudio": "http://ringing.mp3"
    }
}


// 记录通话时间,通话start,over的接口.
### 

post {{mediaServer}}/media/call/start

{
  "dollId": "{{DollID}}",
  "friendDollID": "d_2",
  "startTime": "2025-06-26T10:39:01"
}

{
  "code": 0,
  "message": "success",
  "data": {}
}

###

post {{mediaServer}}/media/call/end

{
  "dollId": "{{DollID}}",
  "friendDollID": "d_2",
  "startTime": "2025-06-26T10:39:01",
  "duration": 30
}

{
  "code": 0,
  "message": "success",
  "data": {}
}


###

// 获取在线好友列表
get {{mediaServer}}/media/friend/list?dollId={{DollID}}

{
  "code": 0,
  "message": "success",
  "data": {
    "friends": [
      {
        "dollId": "zl",
        "nickname": "张三",
        "state":"online" // online, busy
      }
    ]
  }
}

### 

get {{mediaServer}}/media/doll/nickname?dollId={{DollID}}

{
  "code": 0,
  "message": "success",
  "data": {
    "nickname": "张三"
  }
}

###
// 获取音量电量状态
get {{mediaServer}}/media/doll/state?dollId={{DollID}}

{
  "code": 0,
  "message": "success",
  "data": {
    "volume": 50,
    "battery": 50
  }
}

###
// 上传玩偶保存的wifi列表
post {{mediaServer}}/media/doll/upload-ssid-list
content-type: application/json

{
    "dollId": "{{DollID}}",
    "ssids": ["wifi3", "wifi5"]
}

{
    "code": 0,
    "message": "success",
    "data": {}
}

###
// 获取玩偶当前角色信息
get {{mediaServer}}/media/doll/role?dollId={{DollID}}

{
  "code": 0,
  "message": "success",
  "data": {
    "roleId": "r_1",
    "roleName": "小酸奶",
    "roleStory": "我是一个智慧的故事讲述者",
    "roleInfo": "" // jsonString
  }
}

### 
// 更新当前硬件 ota 版本
post {{mediaServer}}/media/doll/ota-version
content-type: application/json

{
  "dollId": "{{DollID}}",
  "version": "1.0.0"
}

{
  "code": 0,
  "message": "success",
  "data": {}
}

###
// 游戏，获取过家家 step
get {{mediaServer}}/media/GJJ/step?dollId={{DollID}}&roleName=&step=

{
  "code": 0,
  "message": "success",
  "data": {
    "step": 0,
    "prompt":"",
    "taskGoal":"",
    "playerSoundMap":{
      "打针":["http://dianzhen.mp3"]
    },
    "emotionAudioMap":{
      "害怕":["http://pao.mp3"],
      "好奇":["http://pao.mp3"],
      "平静":["http://pao.mp3"]
    },
    "dollAudioMap":{
      "打针":["http://dianzhen.mp3"]
    }
  }
}
