@host = http://**************:8922

### 上传 APK
POST {{host}}/upload/apk
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="version"

1.0.0
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="changelog"

增加功能，修改 bug

1.0.0
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test_apk_v1.0.0.apk"
Content-Type: application/octet-stream

< ./test_apk_v1.0.0.apk
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 上传固件
POST {{host}}/upload/ota
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="version"

1.0.0
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test_ota_v1.0.0.ufw"
Content-Type: application/octet-stream

< ./test_ota_v1.0.0.ufw
------WebKitFormBoundary7MA4YWxkTrZu0gW--

HTTP/1.1 200 OK
Content-Type: application/json

{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "version": "1.0.0",
        "filename": "test_ota_v1.0.0.ufw",
        "download_url": "http://",
        "file_size": 1024,
    }
}

### 获取设备列表
// status: 'PlayMusic' | 'StoryTelling' | 'Idle' | 'Gaming' | 'Chating' | 'Offline' | 'VoiceChatWithFriend' | 'VoiceChatWithParent' | 'VoiceChatCalling'|'VoiceChatCallByOther';
// group: 'white'|'test'
// groups 以|分隔, 如果groups为空,则不限制group
GET {{host}}/device/list?page=1&page-size=5

HTTP/1.1 200 OK
Content-Type: application/json

{
    "code": 0,
    "message": "success",
    "data": {
        "page": 1,
        "page_size": 10,
        "total": 100,
        "list": [
            {
                "device_id": "1234567890",
                "device_name": "device1",
                "groups": ["white", "test"],
                "device_type": "SuanNai",
                "status": "Offline",
                "user_phone": "1234567890",
                "firmware_version":"1.0.0",
            }
        ]
    }
}

### 修改设备分组
POST {{host}}/device/group/upsert
Content-Type: application/json

{
    "id": "zl",
    "groups": []
}

HTTP/1.1 200 OK
Content-Type: application/json

{
    "code": 0,
    "message": "success"
}