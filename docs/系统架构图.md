# AIGC Server 系统架构图

## 整体系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        Client1[智能娃娃客户端1]
        Client2[智能娃娃客户端2]
        ClientN[智能娃娃客户端N]
        WebClient[Web测试客户端]
    end

    subgraph "AIGC Server 主进程"
        MainProcess[主进程 Main Process]
        TCPServer[TCP服务器<br/>Port: 8971]
        HTTPServer[HTTP服务器<br/>Port: 8970]
        ProcessManager[进程管理器<br/>Doll Process Manager]
        IPCMain[IPC管理器<br/>Main IPC Manager]
    end

    subgraph "工作进程集群"
        WorkerProcess1[工作进程1<br/>Doll Worker 1]
        WorkerProcess2[工作进程2<br/>Doll Worker 2]
        WorkerProcessN[工作进程N<br/>Doll Worker N]
    end

    subgraph "进程间通信"
        Redis[(Redis消息队列<br/>Port: 10001)]
        IPCQueue[IPC消息队列]
    end

    subgraph "AI服务集群"
        ASRService[ASR语音识别服务<br/>Port: 8910]
        LLMService[LLM大语言模型服务<br/>Port: 8989]
        TTSService[TTS语音合成服务<br/>Port: 8911]
        MediaService[媒体资源服务<br/>Port: 8960]
    end

    subgraph "RTC音视频服务"
        RTCEngine[火山引擎RTC SDK]
        RTCRoom[RTC房间管理]
    end

    %% 客户端连接
    Client1 -.->|TCP协议| TCPServer
    Client2 -.->|TCP协议| TCPServer
    ClientN -.->|TCP协议| TCPServer
    WebClient -.->|HTTP协议| HTTPServer

    %% 主进程内部连接
    TCPServer --> MainProcess
    HTTPServer --> MainProcess
    MainProcess --> ProcessManager
    MainProcess --> IPCMain

    %% 进程管理
    ProcessManager -.->|启动/停止| WorkerProcess1
    ProcessManager -.->|启动/停止| WorkerProcess2
    ProcessManager -.->|启动/停止| WorkerProcessN

    %% IPC通信
    IPCMain <--> Redis
    WorkerProcess1 <--> Redis
    WorkerProcess2 <--> Redis
    WorkerProcessN <--> Redis
    Redis --> IPCQueue

    %% 工作进程与AI服务
    WorkerProcess1 <-->|gRPC Stream| ASRService
    WorkerProcess1 <-->|gRPC Stream| LLMService
    WorkerProcess1 <-->|gRPC Stream| TTSService
    WorkerProcess1 <-->|HTTP| MediaService

    WorkerProcess2 <-->|gRPC Stream| ASRService
    WorkerProcess2 <-->|gRPC Stream| LLMService
    WorkerProcess2 <-->|gRPC Stream| TTSService
    WorkerProcess2 <-->|HTTP| MediaService

    %% RTC连接
    WorkerProcess1 <--> RTCEngine
    WorkerProcess2 <--> RTCEngine
    WorkerProcessN <--> RTCEngine
    RTCEngine --> RTCRoom

    %% 样式定义
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef serverStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef workerStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef aiStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef rtcStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef ipcStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class Client1,Client2,ClientN,WebClient clientStyle
    class MainProcess,TCPServer,HTTPServer,ProcessManager,IPCMain serverStyle
    class WorkerProcess1,WorkerProcess2,WorkerProcessN workerStyle
    class ASRService,LLMService,TTSService,MediaService aiStyle
    class RTCEngine,RTCRoom rtcStyle
    class Redis,IPCQueue ipcStyle
```

## 单个工作进程内部架构图

```mermaid
graph TB
    subgraph "工作进程 Doll Worker Process"
        DollController[娃娃控制器<br/>Doll Controller]
        
        subgraph "音频处理模块"
            AudioReceiver[音频接收器]
            AudioSender[音频发送器]
            AudioQueue[音频帧队列<br/>Voice Frame Queue]
            AudioProcessor[音频处理器<br/>Frame Producer/Consumer]
        end

        subgraph "AI服务客户端"
            GRPCClient[gRPC客户端管理器]
            ASRClient[ASR客户端]
            LLMClient[LLM客户端]
            TTSClient[TTS客户端]
        end

        subgraph "业务逻辑模块"
            LLMState[LLM状态管理器]
            ToolCallFactory[工具调用工厂]
            FriendsVoiceChat[好友语音聊天FSM]
            GroupChatTransport[群组聊天传输]
        end

        subgraph "RTC模块"
            RTCService[RTC服务]
            RTCEventHandler[RTC事件处理器]
        end

        subgraph "IPC模块"
            IPCHandler[IPC消息处理器]
            IPCManager[IPC管理器]
        end
    end

    subgraph "外部服务"
        ASRExternal[ASR服务]
        LLMExternal[LLM服务]
        TTSExternal[TTS服务]
        MediaExternal[媒体服务]
        RedisExternal[(Redis)]
        RTCExternal[RTC SDK]
    end

    %% 内部连接
    DollController --> AudioReceiver
    DollController --> AudioSender
    DollController --> LLMState
    DollController --> FriendsVoiceChat
    DollController --> GroupChatTransport

    AudioReceiver --> AudioQueue
    AudioQueue --> AudioProcessor
    AudioProcessor --> AudioSender

    DollController --> GRPCClient
    GRPCClient --> ASRClient
    GRPCClient --> LLMClient
    GRPCClient --> TTSClient

    LLMState --> ToolCallFactory
    DollController --> RTCService
    RTCService --> RTCEventHandler

    DollController --> IPCHandler
    IPCHandler --> IPCManager

    %% 外部连接
    ASRClient <-->|gRPC Stream| ASRExternal
    LLMClient <-->|gRPC Stream| LLMExternal
    TTSClient <-->|gRPC Stream| TTSExternal
    ToolCallFactory <-->|HTTP| MediaExternal
    IPCManager <--> RedisExternal
    RTCService <--> RTCExternal

    %% 数据流
    RTCEventHandler -.->|音频帧| AudioReceiver
    AudioSender -.->|音频帧| RTCService
    ASRClient -.->|识别文本| LLMState
    LLMState -.->|响应文本| TTSClient
    TTSClient -.->|合成音频| AudioQueue

    classDef controllerStyle fill:#ffebee,stroke:#c62828,stroke-width:3px
    classDef audioStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef aiStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef businessStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef rtcStyle fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef ipcStyle fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef externalStyle fill:#f5f5f5,stroke:#616161,stroke-width:2px

    class DollController controllerStyle
    class AudioReceiver,AudioSender,AudioQueue,AudioProcessor audioStyle
    class GRPCClient,ASRClient,LLMClient,TTSClient aiStyle
    class LLMState,ToolCallFactory,FriendsVoiceChat,GroupChatTransport businessStyle
    class RTCService,RTCEventHandler rtcStyle
    class IPCHandler,IPCManager ipcStyle
    class ASRExternal,LLMExternal,TTSExternal,MediaExternal,RedisExternal,RTCExternal externalStyle
```

## 音频处理流水线图

```mermaid
sequenceDiagram
    participant Client as 智能娃娃客户端
    participant RTC as RTC SDK
    participant Worker as 工作进程
    participant ASR as ASR服务
    participant LLM as LLM服务
    participant TTS as TTS服务
    participant Media as 媒体服务

    Note over Client,Media: 语音交互完整流程

    %% 音频输入流程
    Client->>RTC: 发送音频流
    RTC->>Worker: OnRemoteUserAudioFrame
    Worker->>Worker: 音频格式转换(16kHz)
    Worker->>ASR: SendASRAudio (gRPC Stream)
    ASR->>Worker: ASRResponse (识别文本)

    %% LLM处理流程
    Worker->>LLM: SendLLMMessage (gRPC Stream)
    LLM->>Worker: LLMResponse (响应文本+工具调用)
    
    %% 工具调用处理
    alt 包含工具调用
        Worker->>Media: GetMediaInfo (HTTP)
        Media->>Worker: 返回媒体信息
        Worker->>Media: DownloadFile (HTTP)
        Media->>Worker: 返回音频数据
        Worker->>Worker: 音频格式转换
    end

    %% TTS处理流程
    Worker->>TTS: SendTTSText (gRPC Stream)
    TTS->>Worker: TTSResponse (合成音频)
    Worker->>Worker: 音频重采样(8kHz)
    Worker->>Worker: 加入音频队列

    %% 音频输出流程
    Worker->>RTC: PushExternalAudioFrame
    RTC->>Client: 推送音频流

    Note over Client,Media: 音频处理完成
```

## 进程间通信架构图

```mermaid
graph LR
    subgraph "主进程"
        MainIPC[主进程IPC管理器]
        TCPHandler[TCP连接处理器]
        HTTPHandler[HTTP请求处理器]
    end

    subgraph "Redis消息队列"
        MainQueue[main队列]
        Worker1Queue[worker:doll1队列]
        Worker2Queue[worker:doll2队列]
        WorkerNQueue[worker:dollN队列]
    end

    subgraph "工作进程集群"
        Worker1IPC[工作进程1 IPC管理器]
        Worker2IPC[工作进程2 IPC管理器]
        WorkerNIPC[工作进程N IPC管理器]
    end

    %% IPC连接
    MainIPC <--> MainQueue
    Worker1IPC <--> Worker1Queue
    Worker2IPC <--> Worker2Queue
    WorkerNIPC <--> WorkerNQueue

    %% 跨进程通信
    MainQueue -.->|状态消息| Worker1Queue
    MainQueue -.->|控制消息| Worker2Queue
    Worker1Queue -.->|心跳消息| MainQueue
    Worker2Queue -.->|事件消息| MainQueue

    %% 处理器连接
    TCPHandler --> MainIPC
    HTTPHandler --> MainIPC

    %% 消息类型标注
    MainQueue -.- StatusMsg[状态消息<br/>控制消息<br/>配置消息]
    Worker1Queue -.- EventMsg[事件消息<br/>心跳消息<br/>数据消息]

    classDef mainStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef redisStyle fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef workerStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef msgStyle fill:#fff3e0,stroke:#f57c00,stroke-width:1px

    class MainIPC,TCPHandler,HTTPHandler mainStyle
    class MainQueue,Worker1Queue,Worker2Queue,WorkerNQueue redisStyle
    class Worker1IPC,Worker2IPC,WorkerNIPC workerStyle
    class StatusMsg,EventMsg msgStyle
```

## 工具调用系统架构图

```mermaid
graph TB
    subgraph "LLM响应处理"
        LLMResponse[LLM响应]
        ToolCallParser[工具调用解析器]
        ToolCallFactory[工具调用工厂]
    end

    subgraph "工具调用类型"
        PlayMusic[播放音乐<br/>PLAY_MUSIC]
        TellStory[讲故事<br/>TELL_STORY]
        PlayAudio[播放音频<br/>PLAY_AUDIO]
        AddFriend[添加好友<br/>ADD_FRIEND]
        AddFriendResult[好友结果<br/>ADD_FRIEND_RESULT]
    end

    subgraph "媒体处理"
        MediaService[媒体服务]
        AudioDownload[音频下载]
        AudioConvert[音频转换]
        PCMOutput[PCM音频输出]
    end

    subgraph "音频队列"
        VoiceFrameSource[音频帧源]
        AudioQueue[音频播放队列]
        AudioMixer[音频混合器]
    end

    %% 处理流程
    LLMResponse --> ToolCallParser
    ToolCallParser --> ToolCallFactory
    
    ToolCallFactory --> PlayMusic
    ToolCallFactory --> TellStory
    ToolCallFactory --> PlayAudio
    ToolCallFactory --> AddFriend
    ToolCallFactory --> AddFriendResult

    PlayMusic --> MediaService
    TellStory --> MediaService
    PlayAudio --> MediaService

    MediaService --> AudioDownload
    AudioDownload --> AudioConvert
    AudioConvert --> PCMOutput

    PCMOutput --> VoiceFrameSource
    VoiceFrameSource --> AudioQueue
    AudioQueue --> AudioMixer

    %% 好友相关流程
    AddFriend -.->|TCP请求| TCPClient[TCP客户端]
    TCPClient -.->|好友发现| FriendService[好友服务]
    FriendService -.->|添加结果| AddFriendResult

    classDef llmStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef toolStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef mediaStyle fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef audioStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef friendStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class LLMResponse,ToolCallParser,ToolCallFactory llmStyle
    class PlayMusic,TellStory,PlayAudio,AddFriend,AddFriendResult toolStyle
    class MediaService,AudioDownload,AudioConvert,PCMOutput mediaStyle
    class VoiceFrameSource,AudioQueue,AudioMixer audioStyle
    class TCPClient,FriendService friendStyle
```