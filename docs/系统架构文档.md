# AIGC Server 系统架构文档

## 1. 系统概述

AIGC Server 是一个基于 Go 语言开发的 AI 生成内容服务器，专门用于智能娃娃（AI Doll）的语音交互系统。该系统集成了语音识别（ASR）、大语言模型（LLM）、语音合成（TTS）和实时音视频通信（RTC）等多种 AI 服务，为智能娃娃提供完整的对话交互能力。

### 1.1 核心功能
- **多进程架构**：主进程负责连接管理，子进程处理具体的娃娃交互逻辑
- **实时语音交互**：支持语音识别、自然语言处理和语音合成的完整对话流程
- **RTC 音视频通信**：基于火山引擎 RTC SDK 的实时音频传输
- **工具调用系统**：支持播放音乐、讲故事、添加好友等扩展功能
- **群组语音聊天**：支持娃娃之间的语音通话功能
- **TCP/HTTP 双协议**：同时支持 TCP 和 HTTP 协议的客户端连接

## 2. 系统架构

### 2.1 整体架构设计

系统采用多进程架构，包含一个主进程和多个工作进程：

- **主进程（Main Process）**：负责 TCP/HTTP 服务器、连接管理、进程调度
- **工作进程（Worker Process）**：每个娃娃对应一个独立的工作进程，处理具体的 AI 交互逻辑

### 2.2 进程间通信（IPC）

系统使用基于 Redis 的消息队列实现进程间通信：
- 消息路由机制支持点对点和广播通信
- 支持消息类型化处理和异步消息传递
- 提供消息持久化和可靠性保证

## 3. 核心模块详解

### 3.1 主进程模块（cmd/main）

**主要职责：**
- 启动和管理 TCP/HTTP 服务器
- 处理客户端连接和协议解析
- 管理子进程的生命周期
- 提供系统监控和健康检查

**关键组件：**
- `main.go`：程序入口，初始化配置和服务
- `tcp/server.go`：TCP 服务器实现
- `http/main_http_server.go`：HTTP 服务器实现
- `process/doll_process_manager.go`：子进程管理器

### 3.2 工作进程模块（cmd/doll）

**主要职责：**
- 处理单个娃娃的所有交互逻辑
- 管理 RTC 连接和音频流处理
- 协调 ASR、LLM、TTS 服务调用
- 执行工具调用和扩展功能

**关键组件：**
- `main.go`：工作进程入口
- `doll_controller.go`：娃娃控制器，核心业务逻辑
- `rtc_service.go`：RTC 服务封装
- `llm_state.go`：LLM 状态管理

### 3.3 通信协议模块

#### 3.3.1 TCP 协议
```
协议格式：
+----------+----------+----------+----------+----------+----------+----------+----------+
| Header1  | Header2  | Header3  |   XOR    |        Body Length (4 bytes)        |
|   0xAB   |   0xAB   |   0xAB   | Checksum |                                      |
+----------+----------+----------+----------+----------+----------+----------+----------+
|                                    Body Data                                        |
+---------------------------------------------------------------------------------+
```

**特点：**
- 固定 8 字节头部
- XOR 校验确保数据完整性
- 小端序长度编码
- 支持数据包分片和重组

#### 3.3.2 gRPC 协议
系统与外部 AI 服务通过 gRPC 流式接口通信：

**ASR 服务（语音识别）：**
```protobuf
service ASR {
  rpc ASR(stream ASRRequest) returns (stream ASRResponse);
}
```

**LLM 服务（大语言模型）：**
```protobuf
service LLMChat {
  rpc Chat(stream LLMRequest) returns (stream LLMResponse);
}
```

**TTS 服务（语音合成）：**
```protobuf
service TTS {
  rpc TTS(stream TTSRequest) returns (stream TTSResponse);
}
```

### 3.4 音频处理流水线

#### 3.4.1 音频接收流程
1. **RTC 音频接收**：从 RTC SDK 接收远端音频帧
2. **格式转换**：将音频重采样为 ASR 所需格式（16kHz, 单声道）
3. **ASR 处理**：发送音频数据到语音识别服务
4. **文本处理**：将识别结果发送给 LLM 服务

#### 3.4.2 音频发送流程
1. **TTS 合成**：LLM 响应文本通过 TTS 服务合成语音
2. **音频队列**：合成的音频加入播放队列
3. **格式转换**：重采样为 RTC 推送格式（8kHz）
4. **RTC 推送**：通过 RTC SDK 推送音频到远端

### 3.5 工具调用系统

系统支持多种工具调用类型：

- **播放音乐**（`PLAY_MUSIC`）：从媒体服务获取音乐并播放
- **讲故事**（`TELL_STORY`）：获取故事音频内容
- **播放音频**（`PLAY_AUDIO`）：播放指定的音频文件
- **添加好友**（`ADD_FRIEND`）：处理娃娃间的好友关系
- **好友结果**（`ADD_FRIEND_RESULT`）：返回添加好友的结果

### 3.6 群组语音聊天

支持娃娃之间的实时语音通话：
- 基于 Redis 的群组管理
- 音频数据的实时转发
- 通话状态管理和提示音播放
- 支持家长来电等特殊场景

## 4. 数据流向

### 4.1 语音交互数据流
```
客户端 → TCP/RTC → 工作进程 → ASR服务 → LLM服务 → TTS服务 → 工作进程 → RTC/TCP → 客户端
```

### 4.2 进程间通信数据流
```
主进程 ←→ Redis消息队列 ←→ 工作进程1
                      ←→ 工作进程2
                      ←→ 工作进程N
```

## 5. 配置管理

### 5.1 配置文件结构
```yaml
server:          # 服务器基础配置
grpc:           # gRPC 服务端点配置
tcp:            # TCP 服务器配置
http_server:    # HTTP 服务器配置
http_client:    # HTTP 客户端配置
log:            # 日志配置
rtc:            # RTC 配置（AppID/AppKey）
redis:          # Redis 连接配置
debug:          # 调试选项
```

### 5.2 环境支持
- 支持多环境配置（dev/test/prod）
- 环境变量覆盖机制
- 配置热加载支持

## 6. 服务依赖

### 6.1 外部服务依赖
- **ASR 服务**：语音识别服务（172.31.32.2:8910）
- **LLM 服务**：大语言模型服务（172.31.32.2:8989）
- **TTS 服务**：语音合成服务（172.31.32.2:8911）
- **媒体服务**：音频资源服务（172.31.32.2:8960）
- **Redis**：消息队列和状态存储（172.31.32.2:10001）

### 6.2 SDK 依赖
- **火山引擎 RTC SDK**：实时音视频通信
- **音频处理库**：支持 MP3、WAV、FLAC 等格式

## 7. 部署架构

### 7.1 容器化部署
- 支持 Docker 容器化部署
- 多阶段构建优化镜像大小
- Kubernetes 部署配置

### 7.2 扩展性设计
- 水平扩展：支持多实例部署
- 负载均衡：通过连接分发实现负载均衡
- 资源隔离：每个娃娃独立进程，故障隔离

## 8. 监控和日志

### 8.1 日志系统
- 基于 Zap 的结构化日志
- 分级日志记录（Debug/Info/Warn/Error）
- 日志文件轮转和归档
- 进程级别的日志隔离

### 8.2 监控指标
- 连接数监控
- 进程状态监控
- 音频处理延迟监控
- 服务调用成功率监控

## 9. 安全性

### 9.1 认证授权
- RTC Token 认证机制
- HTTP 接口 Authorization 头验证
- 进程间通信安全

### 9.2 数据安全
- 音频数据加密传输
- 敏感信息脱敏处理
- 访问日志审计

## 10. 性能优化

### 10.1 音频处理优化
- 音频帧缓冲池
- 异步音频处理
- 音频格式转换优化

### 10.2 并发优化
- Goroutine 池管理
- 连接复用
- 内存池优化

## 11. 故障处理

### 11.1 容错机制
- gRPC 连接自动重连
- 音频流中断恢复
- 进程异常重启

### 11.2 降级策略
- 服务降级配置
- 默认响应机制
- 资源限流保护