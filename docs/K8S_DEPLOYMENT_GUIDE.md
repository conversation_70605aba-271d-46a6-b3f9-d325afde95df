# AIGC Server Kubernetes 部署指南

本文档详细介绍如何将 AIGC Server 部署到 Kubernetes 集群，实现弹性可伸缩的服务部署。

## 项目特点分析

### 架构特点

- **双可执行文件**：`aigc-server-main`（主进程）+ `aigc-server-doll`（子进程）
- **多端口监听**：8970（HTTP）、8971（TCP）
- **进程管理**：主进程根据业务需要动态启动子进程
- **依赖复杂**：需要 C/C++动态库（ByteRTC SDK）
- **外部服务**：依赖 Redis、gRPC 服务、HTTP 服务

### 部署挑战

1. **多进程架构**：容器化环境中的进程管理
2. **动态库依赖**：确保运行时库文件可用
3. **配置管理**：多环境配置的统一管理
4. **资源分配**：多子进程的资源需求预估
5. **网络通信**：外部服务访问和内部通信

## 部署架构设计

### 容器化策略

**选择：单容器多进程方案**

- 优点：保持原有进程管理逻辑，部署简单
- 缺点：不符合 k8s 最佳实践，但适合当前架构

### 资源规划

```
CPU: 500m-2000m（根据子进程数量动态调整）
Memory: 1Gi-4Gi（包含音频处理内存需求）
Storage: EmptyDir（临时文件、日志）
```

### 网络规划

```
Service Port 8970 -> Pod Port 8970 (HTTP)
Service Port 8971 -> Pod Port 8971 (TCP)
```

## 部署步骤

### 第一步：准备 Docker 镜像

1. **构建基础镜像**

```bash
# 进入项目目录
cd /path/to/aigc_server

# 构建镜像
./scripts/k8s/build-image.sh
```

2. **推送镜像到仓库**

```bash
# 推送到私有仓库（需要先配置仓库地址）
./scripts/k8s/push-image.sh
```

### 第二步：配置环境变量

1. **复制配置模板**

```bash
cp scripts/k8s/config-template.yaml scripts/k8s/config.yaml
```

2. **编辑配置文件**

```bash
vi scripts/k8s/config.yaml
# 修改以下关键配置：
# - 镜像仓库地址
# - Redis连接信息
# - 外部服务地址
# - 资源限制
```

### 第三步：部署到 k8s

1. **创建命名空间**

```bash
kubectl create namespace aigc-server
```

2. **部署服务**

```bash
./scripts/k8s/deploy.sh
```

3. **验证部署**

```bash
./scripts/k8s/check-status.sh
```

### 第四步：配置弹性伸缩

1. **部署 HPA**

```bash
kubectl apply -f scripts/k8s/hpa.yaml
```

2. **验证 HPA 状态**

```bash
kubectl get hpa -n aigc-server
```

## 配置文件说明

### 1. ConfigMap（configmap.yaml）

存储应用配置文件，包括：

- 服务器配置（端口、主机等）
- 外部服务地址（gRPC、HTTP、Redis）
- 日志配置
- RTC 配置

### 2. Secret（secret.yaml）

存储敏感信息，包括：

- Redis 密码
- API 密钥
- 证书信息

### 3. Deployment（deployment.yaml）

定义 Pod 模板，包括：

- 容器镜像和版本
- 资源限制和请求
- 环境变量
- 存储挂载
- 健康检查

### 4. Service（service.yaml）

定义服务访问，包括：

- ClusterIP 服务（集群内访问）
- LoadBalancer 服务（外部访问）
- 端口映射配置

### 5. HPA（hpa.yaml）

定义自动伸缩策略，包括：

- CPU 使用率阈值
- 内存使用率阈值
- 最小/最大 Pod 数量
- 扩缩容策略

## 监控和运维

### 日志管理

```bash
# 查看实时日志
kubectl logs -f deployment/aigc-server -n aigc-server

# 查看特定Pod日志
kubectl logs -f <pod-name> -n aigc-server

# 查看历史日志
kubectl logs --previous <pod-name> -n aigc-server
```

### 资源监控

```bash
# 查看Pod资源使用情况
kubectl top pods -n aigc-server

# 查看节点资源使用情况
kubectl top nodes

# 查看HPA状态
kubectl describe hpa aigc-server-hpa -n aigc-server
```

### 故障排查

```bash
# 查看Pod状态
kubectl get pods -n aigc-server -o wide

# 查看Pod详细信息
kubectl describe pod <pod-name> -n aigc-server

# 查看服务状态
kubectl get svc -n aigc-server

# 进入Pod内部调试
kubectl exec -it <pod-name> -n aigc-server -- /bin/bash
```

## 扩缩容策略

### 手动扩容

```bash
# 扩容到5个Pod
kubectl scale deployment aigc-server --replicas=5 -n aigc-server
```

### 自动扩容

HPA 会根据以下指标自动扩缩容：

- CPU 使用率 > 70%：扩容
- CPU 使用率 < 30%：缩容
- 内存使用率 > 80%：扩容
- 最小 Pod 数：1
- 最大 Pod 数：10

### 扩容考虑因素

1. **外部依赖**：确保 Redis、gRPC 服务能承受增加的连接
2. **资源限制**：确保集群有足够的 CPU/内存资源
3. **网络带宽**：音频数据传输需要足够的网络带宽
4. **存储 I/O**：日志写入和临时文件处理的 I/O 需求

## 安全考虑

### 网络安全

- 使用 NetworkPolicy 限制 Pod 间通信
- 配置 Ingress 进行外部访问控制
- 启用 TLS 加密（如需要）

### 访问控制

- 使用 RBAC 控制 k8s 资源访问
- Secret 加密存储敏感信息
- 定期轮换密钥和密码

### 容器安全

- 使用非 root 用户运行应用
- 启用容器安全扫描
- 定期更新基础镜像

## 性能优化

### 资源优化

1. **CPU**：根据实际负载调整 CPU 请求和限制
2. **内存**：监控内存使用，特别是音频处理部分
3. **存储**：使用快速存储（SSD）提升 I/O 性能

### 网络优化

1. **服务网格**：考虑使用 Istio 优化内部通信
2. **负载均衡**：优化 Service 负载均衡策略
3. **连接池**：合理配置外部服务连接池

### 应用优化

1. **健康检查**：优化健康检查频率和超时
2. **优雅关闭**：确保 Pod 优雅停止，避免服务中断
3. **预热策略**：考虑 Pod 启动预热，减少冷启动时间

## 备份和恢复

### 配置备份

```bash
# 备份所有配置
kubectl get all,configmap,secret -n aigc-server -o yaml > backup.yaml
```

### 应用恢复

```bash
# 从备份恢复
kubectl apply -f backup.yaml
```

## 常见问题解决

### Q1: Pod 启动失败

**排查步骤：**

1. 检查镜像是否正确：`kubectl describe pod <pod-name>`
2. 查看启动日志：`kubectl logs <pod-name>`
3. 验证配置文件：`kubectl get configmap -o yaml`

### Q2: 服务无法访问

**排查步骤：**

1. 检查 Service 配置：`kubectl get svc`
2. 验证端口映射：`kubectl describe svc aigc-server`
3. 测试 Pod 内服务：`kubectl exec -it <pod-name> -- netstat -tlnp`

### Q3: 自动扩容不工作

**排查步骤：**

1. 检查 metrics-server：`kubectl get --raw /apis/metrics.k8s.io/v1beta1/nodes`
2. 查看 HPA 状态：`kubectl describe hpa`
3. 验证资源使用情况：`kubectl top pods`

### Q4: 性能问题

**排查步骤：**

1. 监控资源使用：`kubectl top pods`
2. 查看应用日志中的性能指标
3. 检查外部依赖的响应时间

## 附录

### 相关命令速查

```bash
# 部署相关
kubectl apply -f scripts/k8s/
kubectl delete -f scripts/k8s/
kubectl rollout restart deployment/aigc-server -n aigc-server

# 监控相关
kubectl get pods -n aigc-server -w
kubectl logs -f deployment/aigc-server -n aigc-server
kubectl top pods -n aigc-server

# 调试相关
kubectl exec -it <pod-name> -n aigc-server -- /bin/bash
kubectl port-forward svc/aigc-server 8970:8970 -n aigc-server
kubectl describe pod <pod-name> -n aigc-server
```

### 学习资源

- [Kubernetes 官方文档](https://kubernetes.io/docs/)
- [HPA 详细说明](https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/)
- [ConfigMap 和 Secret](https://kubernetes.io/docs/concepts/configuration/)
- [网络策略](https://kubernetes.io/docs/concepts/services-networking/network-policies/)
