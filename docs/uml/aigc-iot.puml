@startuml 系统架构图
!theme minty

skinparam arrow {
    FontColor blue
}

cloud "client" as client {
    rectangle "求知(新)" as agent_new {
        component "EMQX client SDK" as emqx_client_sdk
        component "RTC client SDK" as rtc_client_sdk
    }
    rectangle "求知(旧)" as agent_old {
        component "涂鸦 client SDK" as tuya_client_sdk
    }
}
rectangle "AIGC service" as ai_logic_service {
    component "服务器间通信组件 gRpc(redis/Kafka)" as server_communication_component
    component "与agent control service通信组件" as agent_control_communication_component
    component "与RTC service通信组件" as rtc_communication_component
}
cloud "IOT service" as iot_service {
    rectangle "agent control service" as agent_control_service {
        component "EMQX server SDK" as emqx_server_sdk_agent_control
    }
    rectangle "RTC service" as rtc_service {
        component "RTC server SDK" as rtc_server_sdk
    }
    rectangle "涂鸦 适配 service" as tuya_adapter_service {
        component "涂鸦 server SDK" as tuya_server_sdk
        component "EMQX server SDK" as emqx_server_sdk_tuya
    }
}

ai_logic_service <--> iot_service : gRpc
agent_control_communication_component <--> agent_control_service : gRpc
rtc_communication_component <--> rtc_service : gRpc

rtc_server_sdk <--> rtc_client_sdk : rtc

emqx_server_sdk_agent_control <--> emqx_client_sdk : emqx
emqx_server_sdk_agent_control <--> emqx_server_sdk_tuya : emqx

tuya_server_sdk <--> tuya_client_sdk : tuya sdk




@enduml