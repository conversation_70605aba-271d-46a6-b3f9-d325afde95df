@startuml 系统架构图
!theme plain
skinparam linetype ortho
skinparam backgroundColor white
skinparam shadowing false

' 定义颜色
skinparam rectangle {
    BackgroundColor lightblue
    BorderColor black
}

skinparam cloud {
    BackgroundColor lightgray
    BorderColor black
}

skinparam database {
    BackgroundColor lightgreen
    BorderColor black
}

skinparam node {
    BackgroundColor yellow
    BorderColor black
}

skinparam component {
    BackgroundColor lightcoral
    BorderColor black
}

' 用户交互层
rectangle "交互" as interact
rectangle "语音" as voice

' 应用层
rectangle "APP (react)" as app_react

rectangle "APP 迁移的功能" as migration {
    note right
    1. 蓝牙绑定，wifi 配置
    2. 小玩偶聊天记录，对话内容总结
    3. 好友列表
    4. 角色切换
    5. 家长助手
    6. 游戏（存疑）
    end note
}

rectangle "APP (flutter)" as app_flutter

' 阿里云ACK
cloud "阿里云 ACK" as aliyun {
    rectangle "Biz Server\n(go)" as biz_go
    rectangle "Biz Server\n(java)" as biz_java
    rectangle "RTC/AI Server\n(go)" as rtc_ai
    rectangle "Media Server\n(go)" as media
    database "MySQL/Redis" as db
}

' IOT层
node "IOT Server" as iot

' 设备层
component "来知3\n(新)" as laizhi3
component "来知4\n(新)" as laizhi4
component "来知II\n(存量)" as laizhi2_1
component "来知II\n(存量)" as laizhi2_2

' 连接关系
interact --> app_react
voice --> app_react
app_react --> migration
migration --> app_flutter
app_flutter --> aliyun : JWT

' 云服务内部连接
biz_go --> db : JWT
biz_java --> db
rtc_ai --> media
media --> db

' IOT连接
rtc_ai --> iot
iot --> laizhi3
iot --> laizhi4
iot --> laizhi2_1
iot --> laizhi2_2

@enduml 