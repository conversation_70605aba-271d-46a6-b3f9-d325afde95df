# 系统架构图

```mermaid
flowchart TD
    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef appLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef cloudLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef iotLayer fill:#fff8e1,stroke:#e65100,stroke-width:2px
    classDef deviceLayer fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef dbLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    %% 用户交互层
    A[交互]:::userLayer
    B[语音]:::userLayer

    %% 应用层
    C["APP (react)"]:::appLayer
    D["APP 迁移的功能<br/>1. 蓝牙绑定，wifi 配置<br/>2. 小玩偶聊天记录，对话内容总结<br/>3. 好友列表<br/>4. 角色切换<br/>5. 家长助手<br/>6. 游戏（存疑）"]:::appLayer
    E["APP (flutter)"]:::appLayer

    %% 阿里云ACK
    subgraph Cloud ["阿里云 ACK"]
        F["Biz Server<br/>(go)"]:::cloudLayer
        G["Biz Server<br/>(java)"]:::cloudLayer
        H["RTC/AI Server<br/>(go)"]:::cloudLayer
        I["Media Server<br/>(go)"]:::cloudLayer
        J[("MySQL/Redis")]:::dbLayer
    end

    %% IOT层
    K["IOT Server"]:::iotLayer

    %% 设备层
    L["来知3<br/>(新)"]:::deviceLayer
    M["来知4<br/>(新)"]:::deviceLayer
    N["来知II<br/>(存量)"]:::deviceLayer
    O["来知II<br/>(存量)"]:::deviceLayer

    %% 连接关系
    A --> C
    B --> C
    C --> D
    D --> E
    E -->|JWT| Cloud

    %% 云服务内部连接
    F -->|JWT| J
    G --> J
    H --> I
    I --> J

    %% IOT连接
    H --> K
    K --> L
    K --> M
    K --> N
    K --> O
``` 