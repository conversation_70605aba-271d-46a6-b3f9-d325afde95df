# K8s 脚本配置功能说明

## 主要改进

### 1. Secret 配置支持
- 将敏感信息（`app_key` 和 `redis password`）从 ConfigMap 中分离到 Secret
- 自动从 `configs/prod.yaml` 中提取敏感配置
- 在 Deployment 中通过环境变量引用 Secret 值

### 2. 模板与输出分离
- **模板目录**: `scripts/k8s/configs/` - 存放 Kubernetes 配置模板
- **输出目录**: `k8s/configs/` - 存放生成的最终配置文件
- **动态生成**: ConfigMap 内容从 `configs/prod.yaml` 动态读取，移除敏感信息

### 3. kubectl 命令追踪
- 每个 kubectl 命令执行前都会显示完整的命令和解释
- 便于调试和理解脚本执行过程

### 4. 自动配置处理
- 自动移除配置文件中的敏感信息（`app_key`, `password`）
- 自动调整日志配置适配容器环境（`output: console`, `format: json`）
- 添加注释说明敏感信息的来源

## 目录结构

```
├── scripts/k8s/configs/          # 模板目录
│   ├── namespace.yaml            # 命名空间模板
│   ├── secret.yaml              # Secret 模板
│   ├── configmap.yaml           # ConfigMap 模板 (使用 ${CONFIG_CONTENT} 变量)
│   ├── deployment.yaml          # Deployment 模板
│   └── service.yaml             # Service 模板
├── k8s/configs/                 # 输出目录 (生成的最终配置)
│   ├── namespace.yaml           # 生成的命名空间配置
│   ├── secret.yaml              # 生成的 Secret 配置
│   ├── configmap.yaml           # 生成的 ConfigMap 配置 (动态内容)
│   ├── deployment.yaml          # 生成的 Deployment 配置
│   └── service.yaml             # 生成的 Service 配置
└── configs/
    └── prod.yaml                # 原始配置文件 (包含敏感信息)
```

## 配置生成流程

1. **读取原始配置**: 从 `configs/prod.yaml` 读取完整配置
2. **处理敏感信息**: 移除 `app_key` 和 `password` 行
3. **环境适配**: 调整日志配置适配容器环境
4. **模板替换**: 使用 `envsubst` 将处理后的内容填入模板
5. **输出配置**: 生成最终的 Kubernetes 配置文件

## 使用方法

### 生成配置文件
```bash
scripts/k8s/k8s.sh config
```

### 使用预生成配置部署
```bash
# 使用已存在的配置文件部署
scripts/k8s/k8s.sh deploy

# 重新生成配置后部署
scripts/k8s/k8s.sh deploy --config
```

### 调试模式
```bash
# 构建并部署调试版本
scripts/k8s/k8s.sh debug

# 进入调试容器
scripts/k8s/k8s.sh shell

# 在容器内手动启动应用
./aigc-server-main -c /app/configs/prod.yaml
```

## Secret 配置

脚本会自动从 `configs/prod.yaml` 文件中提取以下敏感信息：
- `rtc.app_key` → Secret 中的 `app_key`
- `redis.password` → Secret 中的 `redis_password`

这些值在 Deployment 中通过环境变量暴露，遵循 viper 的环境变量命名规则：
- `AIGC_RTC_APP_KEY` (对应配置路径 `rtc.app_key`)
- `AIGC_REDIS_PASSWORD` (对应配置路径 `redis.password`)

**自动配置加载**：
- Go 代码中的 viper 配置了环境变量前缀 `AIGC` 和自动环境变量加载
- 如果配置文件中有 `rtc.app_key` 或 `redis.password`，则优先使用配置文件中的值
- 如果配置文件中没有这些值，viper 会自动从对应的环境变量中读取
- 无需修改代码，Secret 中的值会自动作为配置的后备值

## 配置加载机制

### viper 配置优先级
根据 [Go 密钥处理最佳实践](https://blog.gitguardian.com/how-to-handle-secrets-in-go/)，viper 按以下优先级加载配置：

1. **配置文件值** - 最高优先级
2. **环境变量** - 次优先级  
3. **默认值** - 最低优先级

### 环境变量命名规则
- **前缀**: `AIGC`
- **路径转换**: 配置路径中的 `.` 替换为 `_`
- **示例映射**:
  ```
  rtc.app_key      → AIGC_RTC_APP_KEY
  redis.password   → AIGC_REDIS_PASSWORD
  server.port      → AIGC_SERVER_PORT
  log.level        → AIGC_LOG_LEVEL
  ```

### 配置策略建议
1. **开发环境**: 使用包含敏感信息的配置文件 (`prod.yaml`)
2. **容器环境**: Secret 自动注入为符合 viper 规则的环境变量

## 命令示例

```bash
# 生成配置文件
./k8s.sh config

# 预览部署（不实际执行）
./k8s.sh deploy --dry-run

# 完整部署流程
./k8s.sh deploy

# 调试模式部署
./k8s.sh debug --config

# 查看状态
./k8s.sh status

# 查看日志
./k8s.sh logs

# 清理部署
./k8s.sh clean --force
```

## 配置模板变量

脚本使用以下环境变量进行模板替换：
- `NAMESPACE` - Kubernetes 命名空间
- `APP_NAME` - 应用名称
- `IMAGE_TAG` - 镜像标签
- `REGISTRY` - 镜像仓库
- `REPLICAS` - 副本数量
- `APP_KEY` - RTC 应用密钥
- `REDIS_PASSWORD` - Redis 密码
- `CONFIG_CONTENT` - 处理后的配置文件内容（用于 ConfigMap） 