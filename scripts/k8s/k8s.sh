#!/bin/bash

# AIGC Server Kubernetes 统一管理脚本
# 集成构建、推送、部署、调试、状态查看、清理等所有功能

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
CONFIGS_DIR="${SCRIPT_DIR}/configs"
OUTPUT_CONFIGS_DIR="${PROJECT_ROOT}/k8s/configs"

# 项目默认配置 - 根据实际项目情况设定
DEFAULT_NAMESPACE="doll99"
DEFAULT_REGISTRY="doll-cn-beijing.cr.volces.com/doll99"
DEFAULT_APP_NAME="aigc-server"
DEFAULT_REPLICAS=1

# 运行时配置
NAMESPACE="${DEFAULT_NAMESPACE}"
REGISTRY="${DEFAULT_REGISTRY}"
APP_NAME="${DEFAULT_APP_NAME}"
REPLICAS="${DEFAULT_REPLICAS}"
IMAGE_TAG=""
COMMAND=""
SKIP_BUILD=false
SKIP_PUSH=false
DRY_RUN=false
FORCE=false
BUILD_DEBUG=false
REGENERATE_CONFIG=false

# 从prod.yaml中读取的敏感信息
APP_KEY=""
REDIS_PASSWORD=""

# kubectl执行封装函数
execute_kubectl() {
    local description="$1"
    shift
    echo "🔧 执行: $description"
    echo "📝 命令: kubectl $@"
    if [ "$DRY_RUN" = true ]; then
        echo "🎯 (预览模式，不实际执行)"
        return 0
    fi
    kubectl "$@"
}

# 使用说明
usage() {
    echo "🚀 AIGC Server Kubernetes 统一管理脚本"
    echo
    echo "用法: $0 <命令> [选项]"
    echo
    echo "命令:"
    echo "  config              生成K8s配置文件到 k8s/configs/ 目录"
    echo "  deploy              部署应用 (默认: 构建+推送+部署)"
    echo "  debug               构建调试镜像并部署 (不自动启动应用程序)"
    echo "  shell               进入Pod Shell"
    echo "  show                查看Pod配置文件"
    echo "  status              查看部署状态和日志"
    echo "  logs                查看实时日志"
    echo "  clean               清理部署"
    echo "  scale <数量>        扩缩容应用"
    echo "  restart             重启应用"
    echo
    echo "选项:"
    echo "  -t, --tag TAG       镜像标签 (默认: 当前日期)"
    echo "  -n, --namespace NS  命名空间 (默认: ${DEFAULT_NAMESPACE})"
    echo "  -r, --registry REG  镜像仓库 (默认: ${DEFAULT_REGISTRY})"
    echo "  -c, --config        重新生成配置文件 (仅用于deploy命令)"
    echo "  --skip-build        跳过构建"
    echo "  --skip-push         跳过推送"
    echo "  --dry-run           预览模式"
    echo "  -f, --force         强制执行"
    echo "  -h, --help          显示帮助"
    echo
    echo "示例:"
    echo "  $0 config                    # 生成K8s配置文件"
    echo "  $0 deploy                    # 使用已生成的配置文件部署"
    echo "  $0 deploy -c                 # 重新生成配置并部署"
    echo "  $0 deploy --skip-build       # 跳过构建直接部署"
    echo "  $0 debug                     # 构建调试镜像并部署"
    echo "  $0 shell                     # 进入Pod Shell"
    echo "  $0 status                    # 查看状态"
    echo "  $0 logs                      # 查看日志"
    echo "  $0 scale 3                   # 扩容到3个副本"
    echo "  $0 clean                     # 清理部署"
    echo
}

# 解析命令行参数
parse_args() {
    if [ $# -eq 0 ]; then
        usage
        exit 1
    fi
    
    # 处理help参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        usage
        exit 0
    fi
    
    COMMAND="$1"
    shift
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -c|--config)
                REGENERATE_CONFIG=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-push)
                SKIP_PUSH=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                if [[ "$COMMAND" == "scale" && -z "$REPLICAS_ARG" ]]; then
                    REPLICAS_ARG="$1"
                    shift
                else
                    echo "❌ 未知参数: $1"
                    usage
                    exit 1
                fi
                ;;
        esac
    done
    
    # 设置默认镜像标签
    if [ -z "$IMAGE_TAG" ]; then
        IMAGE_TAG=$(date +%Y%m%d)
    fi
}

# 检查环境
check_env() {
    echo "🔍 检查环境..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl未安装"
        exit 1
    fi
    
    # 检查kubectl连接
    if ! kubectl get nodes > /dev/null 2>&1; then
        echo "❌ 无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查配置模板目录
    if [ ! -d "$CONFIGS_DIR" ]; then
        echo "❌ 配置模板目录不存在: $CONFIGS_DIR"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 读取敏感配置
read_sensitive_config() {
    echo "🔐 读取敏感配置..."
    
    if [ ! -f "${PROJECT_ROOT}/configs/prod.yaml" ]; then
        echo "❌ 配置文件不存在: ${PROJECT_ROOT}/configs/prod.yaml"
        exit 1
    fi
    
    # 从prod.yaml中提取敏感信息
    APP_KEY=$(grep "app_key:" "${PROJECT_ROOT}/configs/prod.yaml" | sed 's/.*app_key: *//' | tr -d '"')
    REDIS_PASSWORD=$(grep "password:" "${PROJECT_ROOT}/configs/prod.yaml" | sed 's/.*password: *//' | tr -d '"')
    
    if [ -z "$APP_KEY" ] || [ -z "$REDIS_PASSWORD" ]; then
        echo "❌ 无法从配置文件中读取敏感信息"
        echo "   APP_KEY: $APP_KEY"
        echo "   REDIS_PASSWORD: $REDIS_PASSWORD"
        exit 1
    fi
    
    echo "✅ 敏感配置读取完成"
}

# 生成配置文件
generate_configs() {
    echo "📋 生成K8s配置文件..."
    
    # 确保输出目录存在
    mkdir -p "${OUTPUT_CONFIGS_DIR}"
    
    # 读取敏感配置
    read_sensitive_config
    
    # 处理配置文件内容用于ConfigMap
    echo "🔧 处理配置文件内容..."
    if [ ! -f "${PROJECT_ROOT}/configs/prod.yaml" ]; then
        echo "❌ 配置文件不存在: ${PROJECT_ROOT}/configs/prod.yaml"
        exit 1
    fi
    
    # 创建临时配置文件（移除敏感信息并适配容器环境）
    TEMP_CONFIG=$(mktemp)
    
    # 复制原配置文件并进行处理
    cp "${PROJECT_ROOT}/configs/prod.yaml" "${TEMP_CONFIG}"
    
    # 移除敏感信息行
    sed -i 's/app_key:.*/app_key: /' "${TEMP_CONFIG}"
    sed -i 's/password:.*/password: /' "${TEMP_CONFIG}"
    
    # 修改日志配置适配容器环境
    sed -i 's/output: file/output: console/' "${TEMP_CONFIG}"
    sed -i 's/format: console/format: json/' "${TEMP_CONFIG}"
    
    # 添加注释说明敏感信息来源
    # sed -i '/app_id:/a\  # app_key 从环境变量 AIGC_RTC_APP_KEY 自动加载' "${TEMP_CONFIG}"
    # sed -i '/port: 10001/a\  # password 从环境变量 AIGC_REDIS_PASSWORD 自动加载' "${TEMP_CONFIG}"
    
    # 读取处理后的配置内容，并添加适当的缩进
    CONFIG_CONTENT=$(sed '2,$ s/^/    /' "${TEMP_CONFIG}")
    
    # 清理临时文件
    rm -f "${TEMP_CONFIG}"
    
    # 设置环境变量用于模板替换
    export NAMESPACE="$NAMESPACE"
    export APP_NAME="$APP_NAME"
    export IMAGE_TAG="$IMAGE_TAG"
    export REGISTRY="$REGISTRY"
    export REPLICAS="$REPLICAS"
    export APP_KEY="$APP_KEY"
    export REDIS_PASSWORD="$REDIS_PASSWORD"
    export CONFIG_CONTENT="$CONFIG_CONTENT"
    
    echo "🔧 生成命名空间配置..."
    envsubst < "${CONFIGS_DIR}/namespace.yaml" > "${OUTPUT_CONFIGS_DIR}/namespace.yaml"
    
    echo "🔧 生成Secret配置..."
    envsubst < "${CONFIGS_DIR}/secret.yaml" > "${OUTPUT_CONFIGS_DIR}/secret.yaml"
    
    echo "🔧 生成ConfigMap配置..."
    envsubst < "${CONFIGS_DIR}/configmap.yaml" > "${OUTPUT_CONFIGS_DIR}/configmap.yaml"
    
    echo "🔧 生成Deployment配置..."
    envsubst < "${CONFIGS_DIR}/deployment.yaml" > "${OUTPUT_CONFIGS_DIR}/deployment.yaml"
    
    echo "🔧 生成Service配置..."
    envsubst < "${CONFIGS_DIR}/service.yaml" > "${OUTPUT_CONFIGS_DIR}/service.yaml"
    
    echo "✅ 配置文件生成完成，已保存到: ${OUTPUT_CONFIGS_DIR}"
    echo "📁 生成的文件:"
    ls -la "${OUTPUT_CONFIGS_DIR}/"
}

# 构建镜像
build_image() {
    if [ "$SKIP_BUILD" = true ]; then
        echo "⏭️  跳过构建"
        return
    fi
    
    echo "🔨 构建镜像..."
    cd "${PROJECT_ROOT}"
    
    # 检查可执行文件
    if [ ! -f "aigc-server-main" ] || [ ! -f "aigc-server-doll" ]; then
        echo "❌ 可执行文件不存在，请先运行 make build"
        exit 1
    fi
    
    # 根据是否调试模式选择构建参数
    if [ "$BUILD_DEBUG" = true ]; then
        echo "🐛 构建调试版本镜像 (不自动启动应用程序)"
        "${PROJECT_ROOT}/scripts/build-image.sh" "${IMAGE_TAG}" --debug
    else
        echo "🚀 构建生产版本镜像"
        "${PROJECT_ROOT}/scripts/build-image.sh" "${IMAGE_TAG}"
    fi
}

# 推送镜像
push_image() {
    if [ "$SKIP_PUSH" = true ]; then
        echo "⏭️  跳过推送"
        return
    fi
    
    echo "📤 推送镜像..."
    
    LOCAL_TAG="${APP_NAME}:${IMAGE_TAG}"
    REMOTE_TAG="${REGISTRY}/${APP_NAME}:${IMAGE_TAG}"
    
    # 检查本地镜像
    if ! docker images | grep -q "${APP_NAME}.*${IMAGE_TAG}"; then
        echo "❌ 本地镜像不存在: ${LOCAL_TAG}"
        exit 1
    fi
    
    # 标记并推送
    docker tag "${LOCAL_TAG}" "${REMOTE_TAG}"
    docker push "${REMOTE_TAG}"
    docker rmi "${REMOTE_TAG}" || true
    
    echo "✅ 镜像推送完成: ${REMOTE_TAG}"
}

# 获取Pod名称
get_pod_name() {
    kubectl -n "${NAMESPACE}" get pods -l app="${APP_NAME}" \
        -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo ""
}

# 等待Pod就绪
wait_pod_ready() {
    local pod_name="$1"
    local timeout="${2:-120}"
    
    echo "⏳ 等待Pod启动: $pod_name"
    
    if kubectl -n "${NAMESPACE}" wait --for=condition=Ready pod/"$pod_name" --timeout="${timeout}s"; then
        echo "✅ Pod已就绪"
        return 0
    else
        echo "⚠️  Pod启动超时，当前状态:"
        kubectl -n "${NAMESPACE}" get pod "$pod_name"
        return 1
    fi
}

# 部署应用
deploy_app() {
    echo "🚀 部署应用到 Kubernetes..."
    echo "📋 部署信息:"
    echo "  - 命名空间: ${NAMESPACE}"
    echo "  - 镜像: ${REGISTRY}/${APP_NAME}:${IMAGE_TAG}"
    echo "  - 副本数: ${REPLICAS}"
    
    # 检查是否需要重新生成配置
    if [ "$REGENERATE_CONFIG" = true ] || [ ! -d "${OUTPUT_CONFIGS_DIR}" ]; then
        echo "🔧 重新生成配置文件..."
        generate_configs
    else
        # 检查配置文件是否存在
        if [ ! -f "${OUTPUT_CONFIGS_DIR}/namespace.yaml" ] || \
           [ ! -f "${OUTPUT_CONFIGS_DIR}/secret.yaml" ] || \
           [ ! -f "${OUTPUT_CONFIGS_DIR}/configmap.yaml" ] || \
           [ ! -f "${OUTPUT_CONFIGS_DIR}/deployment.yaml" ] || \
           [ ! -f "${OUTPUT_CONFIGS_DIR}/service.yaml" ]; then
            echo "⚠️  配置文件不完整，重新生成..."
            generate_configs
        else
            echo "✅ 使用已存在的配置文件: ${OUTPUT_CONFIGS_DIR}"
        fi
    fi
    
    if [ "$DRY_RUN" = true ]; then
        echo "🎯 预览模式 - 显示将要应用的资源:"
        echo
        echo "--- Namespace ---"
        cat "${OUTPUT_CONFIGS_DIR}/namespace.yaml"
        echo
        echo "--- Secret ---"
        cat "${OUTPUT_CONFIGS_DIR}/secret.yaml"
        echo
        echo "--- ConfigMap ---"
        cat "${OUTPUT_CONFIGS_DIR}/configmap.yaml"
        echo
        echo "--- Deployment ---"
        cat "${OUTPUT_CONFIGS_DIR}/deployment.yaml"
        echo
        echo "--- Service ---"
        cat "${OUTPUT_CONFIGS_DIR}/service.yaml"
        return
    fi
    
    # 应用配置
    execute_kubectl "创建或更新命名空间" apply -f "${OUTPUT_CONFIGS_DIR}/namespace.yaml"
    execute_kubectl "创建或更新Secret(包含敏感配置)" apply -f "${OUTPUT_CONFIGS_DIR}/secret.yaml"
    execute_kubectl "创建或更新ConfigMap(应用配置)" apply -f "${OUTPUT_CONFIGS_DIR}/configmap.yaml"
    execute_kubectl "创建或更新Deployment(应用部署)" apply -f "${OUTPUT_CONFIGS_DIR}/deployment.yaml"
    execute_kubectl "创建或更新Service(服务暴露)" apply -f "${OUTPUT_CONFIGS_DIR}/service.yaml"
    
    echo "✅ 部署完成"
    
    # 等待部署就绪
    echo "⏳ 等待部署就绪..."
    sleep 30
    
    show_status
}

# 进入Pod Shell
enter_shell() {
    local pod_name=$(get_pod_name)
    
    if [ -z "$pod_name" ]; then
        echo "❌ 未找到Pod，请先创建调试Pod:"
        echo "   $0 debug"
        exit 1
    fi
    
    echo "🐚 进入Pod: ${pod_name}"
    execute_kubectl "进入Pod Shell进行调试" -n "${NAMESPACE}" exec -it "${pod_name}" -- /bin/bash
}

# 查看配置文件
show_config() {
    local pod_name=$(get_pod_name)
    
    if [ -z "$pod_name" ]; then
        echo "❌ 未找到Pod，请先创建调试Pod:"
        echo "   $0 debug"
        exit 1
    fi
    
    echo "⚙️  Pod配置文件:"
    echo "--- /app/configs/prod.yaml ---"
    execute_kubectl "查看Pod内的配置文件" -n "${NAMESPACE}" exec "${pod_name}" -- cat /app/configs/prod.yaml
    echo
    echo "--- 文件系统结构 ---"
    execute_kubectl "查看Pod内的文件结构" -n "${NAMESPACE}" exec "${pod_name}" -- ls -la /app/
    echo
    echo "--- 环境变量 (敏感信息) ---"
    execute_kubectl "查看Pod内的敏感环境变量" -n "${NAMESPACE}" exec "${pod_name}" -- env | grep -E "(AIGC_RTC_APP_KEY|AIGC_REDIS_PASSWORD)"
}

# 显示状态
show_status() {
    echo "📊 部署状态:"
    execute_kubectl "查看Deployment状态" -n "${NAMESPACE}" get deployment "${APP_NAME}" 2>/dev/null || echo "未找到Deployment"
    echo
    
    echo "🐳 Pod状态:"
    execute_kubectl "查看Pod状态和位置" -n "${NAMESPACE}" get pods -l app="${APP_NAME}" -o wide 2>/dev/null || echo "未找到Pod"
    echo
    
    echo "🌐 Service状态:"
    execute_kubectl "查看Service状态" -n "${NAMESPACE}" get svc -l app="${APP_NAME}" 2>/dev/null || echo "未找到Service"
    
    # 获取外部访问信息
    EXTERNAL_IP=$(kubectl -n "${NAMESPACE}" get svc "${APP_NAME}-external" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    if [ -n "$EXTERNAL_IP" ]; then
        echo
        echo "🔗 外部访问:"
        echo "  HTTP: http://${EXTERNAL_IP}:8970"
        echo "  TCP:  ${EXTERNAL_IP}:8971"
    fi
}

# 查看日志
show_logs() {
    echo "📝 应用日志:"
    execute_kubectl "查看应用实时日志" -n "${NAMESPACE}" logs -f -l app="${APP_NAME}" --all-containers=true
}

# 清理部署
clean_app() {
    if [ "$FORCE" = false ]; then
        echo "⚠️  确认删除操作"
        echo "即将删除命名空间 ${NAMESPACE} 中的 ${APP_NAME} 相关资源"
        read -p "确认继续？(y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 操作已取消"
            exit 0
        fi
    fi
    
    echo "🗑️  清理部署..."
    
    # 清理所有相关资源
    execute_kubectl "删除Deployment" -n "${NAMESPACE}" delete deployment "${APP_NAME}" --ignore-not-found=true
    execute_kubectl "删除Services" -n "${NAMESPACE}" delete service -l app="${APP_NAME}" --ignore-not-found=true
    execute_kubectl "删除ConfigMap" -n "${NAMESPACE}" delete configmap "${APP_NAME}-config" --ignore-not-found=true
    execute_kubectl "删除Secret" -n "${NAMESPACE}" delete secret "${APP_NAME}-secret" --ignore-not-found=true
    
    echo "✅ 清理完成"
}

# 扩缩容
scale_app() {
    if [ -z "$REPLICAS_ARG" ]; then
        echo "❌ 请指定副本数量"
        echo "用法: $0 scale <数量>"
        exit 1
    fi
    
    REPLICAS="$REPLICAS_ARG"
    echo "📊 扩缩容到 ${REPLICAS} 个副本..."
    
    execute_kubectl "扩缩容Deployment" -n "${NAMESPACE}" scale deployment "${APP_NAME}" --replicas="${REPLICAS}"
    execute_kubectl "等待扩缩容完成" -n "${NAMESPACE}" rollout status deployment/"${APP_NAME}"
    
    echo "✅ 扩缩容完成"
    show_status
}

# 重启应用
restart_app() {
    echo "🔄 重启应用..."
    execute_kubectl "重启Deployment" -n "${NAMESPACE}" rollout restart deployment/"${APP_NAME}"
    execute_kubectl "等待重启完成" -n "${NAMESPACE}" rollout status deployment/"${APP_NAME}"
    echo "✅ 重启完成"
}

# 主函数
main() {
    parse_args "$@"
    
    echo "🚀 AIGC Server Kubernetes 管理"
    echo "📦 命名空间: ${NAMESPACE}"
    echo "🏷️  镜像标签: ${IMAGE_TAG}"
    echo "🌐 镜像仓库: ${REGISTRY}"
    echo
    
    check_env
    
    case $COMMAND in
        config)
            generate_configs
            ;;
        deploy)
            build_image
            push_image
            deploy_app
            ;;
        debug)
            BUILD_DEBUG=true
            build_image
            push_image
            deploy_app
            echo
            echo "💡 调试版本已部署，使用以下命令:"
            echo "   $0 shell        # 进入Pod Shell手动启动应用"
            echo "   $0 logs         # 查看Pod日志"
            echo "   $0 status       # 查看Pod状态"
            ;;
        shell)
            enter_shell
            ;;
        show)
            show_config
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_app
            ;;
        scale)
            scale_app
            ;;
        restart)
            restart_app
            ;;
        *)
            echo "❌ 未知命令: $COMMAND"
            usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 