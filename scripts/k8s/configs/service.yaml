apiVersion: v1
kind: Service
metadata:
  name: ${APP_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${APP_NAME}
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8970
    targetPort: 8970
  - name: tcp
    port: 8971
    targetPort: 8971
  selector:
    app: ${APP_NAME}
---
apiVersion: v1
kind: Service
metadata:
  name: ${APP_NAME}-external
  namespace: ${NAMESPACE}
  labels:
    app: ${APP_NAME}
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 8970
    targetPort: 8970
  - name: tcp
    port: 8971
    targetPort: 8971
  selector:
    app: ${APP_NAME} 