apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${APP_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${APP_NAME}
    version: "${IMAGE_TAG}"
spec:
  replicas: ${REPLICAS}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ${APP_NAME}
  template:
    metadata:
      labels:
        app: ${APP_NAME}
        version: "${IMAGE_TAG}"
    spec:
      serviceAccountName: default
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      tolerations:
      - key: vci.vke.volcengine.com/node-type
        operator: Equal
        value: vci
        effect: NoSchedule
      containers:
      - name: ${APP_NAME}
        image: ${REGISTRY}/${APP_NAME}:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8970
        - name: tcp
          containerPort: 8971
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: AIGC_RTC_APP_KEY
          valueFrom:
            secretKeyRef:
              name: ${APP_NAME}-secret
              key: app_key
        - name: AIGC_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ${APP_NAME}-secret
              key: redis_password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
            vke.volcengine.com/eni-ip: "1"
          limits:
            memory: "1Gi"
            cpu: "1000m"
            vke.volcengine.com/eni-ip: "1"
        livenessProbe:
          tcpSocket:
            port: 8970
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 8970
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          tcpSocket:
            port: 8970
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          readOnlyRootFilesystem: false
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: ${APP_NAME}-config
          items:
          - key: prod.yaml
            path: prod.yaml
            mode: 0644
      - name: logs
        emptyDir: {}
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst 