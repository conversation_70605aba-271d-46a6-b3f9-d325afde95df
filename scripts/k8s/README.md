# AIGC Server Kubernetes 部署脚本

## 📋 概述

本目录包含AIGC Server在Kubernetes环境中的完整部署解决方案。基于[Kubernetes最佳实践](https://medium.com/@platform.engineers/kubernetes-configuration-best-practices-3e16ee6e733c)和[YAML配置最佳实践](https://mogenius.com/blog-posts/best-practices-for-writing-kubernetes-yaml-manifests)，提供统一、简洁、安全的K8s管理方案。

## 🗂️ 目录结构

```
scripts/k8s/
├── k8s.sh                          # 统一管理脚本 (主入口)
├── configs/                        # K8s配置模板
│   ├── namespace.yaml              # 命名空间模板  
│   ├── deployment.yaml             # Deployment模板
│   ├── debug-deployment.yaml       # 调试Pod模板
│   └── service.yaml                # Service模板
└── README.md                       # 本文档
```

### ✨ 核心特性

- **🎯 单一入口**: 一个脚本完成所有K8s操作
- **🐛 调试友好**: 专门的调试Pod，不运行应用程序
- **⚙️ 配置统一**: 基于外部模板，消除代码重复
- **🔒 安全合规**: 遵循K8s安全最佳实践
- **📊 智能监控**: 完整的健康检查和探针配置

## 🚀 快速开始

### 前置条件

```bash
# 必需工具
kubectl               # Kubernetes CLI
docker                # Docker镜像管理
envsubst              # 模板变量替换 (通常包含在 gettext 包中)

# 安装envsubst (如果缺失)
sudo apt-get install gettext-base   # Ubuntu/Debian
sudo yum install gettext             # CentOS/RHEL
```

### 基本部署

```bash
# 完整部署流程 (构建+推送+部署)
./scripts/k8s/k8s.sh deploy

# 跳过构建直接部署
./scripts/k8s/k8s.sh deploy --skip-build

# 预览部署配置
./scripts/k8s/k8s.sh deploy --dry-run
```

## 📖 命令详解

### 部署管理

#### `deploy` - 应用部署
```bash
# 完整部署 (构建+推送+部署)
./k8s.sh deploy

# 指定镜像标签
./k8s.sh deploy -t v1.0.0

# 跳过构建和推送
./k8s.sh deploy --skip-build --skip-push

# 预览模式 (不实际执行)
./k8s.sh deploy --dry-run
```

#### `status` - 状态查看
```bash
# 查看部署状态
./k8s.sh status

# 输出示例:
# 📊 部署状态:
# NAME         READY   UP-TO-DATE   AVAILABLE
# aigc-server  2/2     2            2
#
# 🐳 Pod状态:
# NAME                          READY   STATUS    RESTARTS
# aigc-server-7d4b8c9f4-abc12   1/1     Running   0
# aigc-server-7d4b8c9f4-def34   1/1     Running   0
```

#### `logs` - 日志查看
```bash
# 实时查看所有Pod日志
./k8s.sh logs
```

#### `scale` - 扩缩容
```bash
# 扩容到5个副本
./k8s.sh scale 5

# 缩容到1个副本
./k8s.sh scale 1
```

#### `restart` - 重启应用
```bash
# 滚动重启应用
./k8s.sh restart
```

#### `clean` - 清理资源
```bash
# 交互式清理
./k8s.sh clean

# 强制清理 (无确认)
./k8s.sh clean --force
```

### 调试功能

#### `debug` - 创建调试Pod
```bash
# 创建调试Pod (不运行应用程序)
./k8s.sh debug

# 创建后提示:
# 💡 调试Pod已创建，使用以下命令:
#    ./k8s.sh shell        # 进入Pod Shell
#    ./k8s.sh config       # 查看配置文件
#    ./k8s.sh exec <命令>  # 执行命令
```

#### `shell` - 进入Pod Shell
```bash
# 进入调试Pod或运行Pod的Shell
./k8s.sh shell

# 进入后可以:
# - 检查文件系统
# - 测试网络连接
# - 验证配置文件
# - 运行诊断命令
```

#### `exec` - 执行命令
```bash
# 在Pod中执行单个命令
./k8s.sh exec 'ls -la /app'
./k8s.sh exec 'cat /app/configs/prod.yaml'
./k8s.sh exec 'ps aux'
./k8s.sh exec 'netstat -tulpn'
```

#### `config` - 查看配置
```bash
# 查看Pod内的配置文件
./k8s.sh config

# 输出示例:
# ⚙️  Pod配置文件:
# --- /app/configs/prod.yaml ---
# server:
#   name: aigc-server
#   version: 0.1.0
# ...
# --- 文件系统结构 ---
# drwxr-xr-x 1 <USER> <GROUP>  4096 Jul 28 16:35 .
# -rwxrwxr-x 1 <USER> <GROUP> 32MB  Jul 25 17:36 aigc-server-doll
```

## ⚙️ 配置选项

### 环境变量配置

脚本支持以下配置项，可通过命令行参数覆盖：

```bash
# 默认配置 (在脚本中定义)
DEFAULT_NAMESPACE="doll99"                                   # 命名空间
DEFAULT_REGISTRY="doll-cn-beijing.cr.volces.com/doll99"     # 镜像仓库
DEFAULT_APP_NAME="aigc-server"                              # 应用名称
DEFAULT_REPLICAS=2                                          # 默认副本数

# 运行时覆盖
./k8s.sh deploy -n production -r my-registry.com/project -t v2.0.0
```

### 命令行选项

| 选项 | 长选项 | 描述 | 默认值 |
|------|--------|------|--------|
| `-t` | `--tag` | 镜像标签 | 当前日期 (YYYYMMDD) |
| `-n` | `--namespace` | K8s命名空间 | `doll99` |
| `-r` | `--registry` | 镜像仓库 | `doll-cn-beijing.cr.volces.com/doll99` |
| | `--skip-build` | 跳过构建 | false |
| | `--skip-push` | 跳过推送 | false |
| | `--dry-run` | 预览模式 | false |
| `-f` | `--force` | 强制执行 | false |
| `-h` | `--help` | 显示帮助 | |

## 🔧 配置模板

### 模板系统

脚本使用外部YAML模板配合 `envsubst` 进行变量替换，支持以下变量：

- `${NAMESPACE}` - 命名空间
- `${APP_NAME}` - 应用名称  
- `${IMAGE_TAG}` - 镜像标签
- `${REGISTRY}` - 镜像仓库
- `${REPLICAS}` - 副本数
- `${COMMAND_SECTION}` - 容器命令配置
- `${PROBES_SECTION}` - 健康检查配置

### ConfigMap自动生成

脚本自动从 `configs/prod.yaml` 生成K8s ConfigMap，并进行环境适配：

```yaml
# 原始配置 (configs/prod.yaml)
log:
  level: info
  format: console
  output: file

# 容器适配后
log:
  level: info  
  format: json      # console -> json (结构化日志)
  output: console   # file -> console (容器标准输出)
```

## 🛡️ 安全配置

### 安全最佳实践

脚本遵循Kubernetes安全最佳实践：

1. **非Root用户**: 容器以UID 1000运行
2. **安全上下文**: 禁用特权提升和特权模式
3. **资源限制**: CPU/内存请求和限制
4. **健康检查**: 完整的存活、就绪、启动探针
5. **只读根文件系统**: 仅日志目录可写
6. **SecComp Profile**: 使用运行时默认安全配置

### 资源配置

```yaml
# 生产环境 (deployment.yaml)
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "1Gi"
    cpu: "1000m"

# 调试环境 (debug-deployment.yaml)
resources:
  requests:
    memory: "128Mi"
    cpu: "100m"
  limits:
    memory: "256Mi"  
    cpu: "200m"
```

## 🐛 调试指南

### 常见问题排查

#### 1. Pod启动失败
```bash
# 查看Pod状态
./k8s.sh status

# 查看Pod事件
kubectl -n doll99 describe pod <pod-name>

# 查看容器日志
./k8s.sh logs
```

#### 2. 镜像拉取失败
```bash
# 检查镜像是否存在
docker images | grep aigc-server

# 检查镜像仓库访问
docker pull doll-cn-beijing.cr.volces.com/doll99/aigc-server:latest
```

#### 3. 配置问题
```bash
# 创建调试Pod检查配置
./k8s.sh debug
./k8s.sh config

# 查看ConfigMap内容
kubectl -n doll99 get configmap aigc-server-config -o yaml
```

#### 4. 网络连接问题
```bash
# 进入Pod测试网络
./k8s.sh shell

# 在Pod内测试
nslookup kubernetes.default.svc.cluster.local
curl -v http://aigc-server:8970/health
```

### 调试工作流

```bash
# 1. 创建调试环境
./k8s.sh debug

# 2. 检查基础环境
./k8s.sh exec 'ls -la /app'
./k8s.sh exec 'cat /etc/os-release'

# 3. 验证配置文件
./k8s.sh config

# 4. 测试网络连接
./k8s.sh exec 'ping 8.8.8.8'
./k8s.sh exec 'nslookup kubernetes.default'

# 5. 手动启动应用 (用于调试)
./k8s.sh shell
cd /app && ./aigc-server-main --config configs/prod.yaml

# 6. 清理调试环境
./k8s.sh clean
```

## 📊 监控和日志

### 应用监控

部署包含完整的健康检查配置：

```yaml
livenessProbe:          # 存活探针 - 应用是否运行
  tcpSocket:
    port: 8970
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:         # 就绪探针 - 应用是否准备好接收流量  
  tcpSocket:
    port: 8970
  initialDelaySeconds: 5
  periodSeconds: 5

startupProbe:           # 启动探针 - 应用启动检查
  tcpSocket:
    port: 8970
  initialDelaySeconds: 10
  failureThreshold: 30
```

### 日志管理

```bash
# 实时查看日志
./k8s.sh logs

# 查看特定Pod日志
kubectl -n doll99 logs -f deployment/aigc-server

# 查看历史日志
kubectl -n doll99 logs deployment/aigc-server --previous
```

### 服务访问

```bash
# 内部访问 (ClusterIP)
kubectl -n doll99 get svc aigc-server

# 外部访问 (LoadBalancer)
kubectl -n doll99 get svc aigc-server-external

# 端口转发 (本地测试)
kubectl -n doll99 port-forward svc/aigc-server 8970:8970
```

## 🔄 CI/CD集成

### GitLab CI示例

```yaml
deploy:
  stage: deploy
  script:
    - ./scripts/k8s/k8s.sh deploy --skip-build -t $CI_COMMIT_TAG
  only:
    - tags
```

### GitHub Actions示例

```yaml
- name: Deploy to Kubernetes
  run: |
    ./scripts/k8s/k8s.sh deploy --skip-build -t ${{ github.ref_name }}
```

## 🎯 最佳实践

### 开发流程

1. **本地测试**: 使用 `--dry-run` 预览配置
2. **调试优先**: 遇到问题先创建调试Pod
3. **渐进部署**: 先部署到测试环境
4. **监控观察**: 部署后观察状态和日志
5. **及时清理**: 开发完成后清理测试资源

### 配置管理

1. **单一源头**: 只维护 `configs/prod.yaml`
2. **版本控制**: 配置文件纳入Git管理
3. **环境隔离**: 使用不同的命名空间
4. **密钥分离**: 敏感信息使用K8s Secrets

### 安全要求

1. **最小权限**: 只授予必要的RBAC权限
2. **网络隔离**: 使用NetworkPolicy限制访问
3. **镜像安全**: 定期扫描和更新基础镜像
4. **配置加密**: 敏感配置使用加密存储

## 🆘 故障排除

### 常见错误

| 错误信息 | 原因 | 解决方法 |
|----------|------|----------|
| `kubectl not found` | kubectl未安装 | 安装kubectl工具 |
| `connection refused` | 无法连接集群 | 检查kubeconfig配置 |
| `image not found` | 镜像不存在 | 运行构建脚本或检查镜像名 |
| `configmap not found` | ConfigMap缺失 | 脚本会自动创建，检查权限 |
| `envsubst not found` | 缺少模板工具 | `apt install gettext-base` |

### 获取帮助

```bash
# 查看脚本帮助
./k8s.sh --help

# 查看K8s资源状态
kubectl -n doll99 get all

# 查看详细事件
kubectl -n doll99 get events --sort-by='.lastTimestamp'
```

---

## 📈 版本历史

### v2.0.0 (当前版本)
- ✅ 统一脚本入口，替代多个独立脚本
- ✅ 新增调试Pod功能，支持不运行应用程序的调试环境
- ✅ 配置模板外部化，消除代码重复
- ✅ 优化错误处理和状态检查
- ✅ 遵循Kubernetes安全最佳实践

### v1.0.0 (历史版本)
- 多脚本架构: `deploy.sh`, `status.sh`, `clean.sh`等
- 内联配置生成
- 基础功能实现

---

🎉 **现在您可以使用单一脚本完成所有K8s操作，开发效率大幅提升！** 