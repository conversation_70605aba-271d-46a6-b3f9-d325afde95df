#!/bin/bash

# AIGC Server Docker镜像构建脚本
# 用于构建包含所有依赖的Docker镜像

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
IMAGE_NAME="aigc-server"
DATE=$(date +%Y%m%d)
IMAGE_TAG=${1:-"${DATE}"}
DEBUG_MODE=false

# 检查是否为调试模式
if [ "$2" = "--debug" ]; then
    DEBUG_MODE=true
fi

FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

if [ "$DEBUG_MODE" = true ]; then
    echo "🐛 开始构建AIGC Server 调试版Docker镜像..."
else
    echo "🚀 开始构建AIGC Server Docker镜像..."
fi
echo "📁 项目根目录: ${PROJECT_ROOT}"
echo "🏷️  镜像名称: ${FULL_IMAGE_NAME}"

# 切换到项目根目录
cd "${PROJECT_ROOT}"

# 检查必要文件是否存在
echo "🔍 检查必要文件..."
if [ ! -f "aigc-server-main" ]; then
    echo "❌ 可执行文件 aigc-server-main 不存在，请先运行 make build"
    exit 1
fi

if [ ! -f "aigc-server-doll" ]; then
    echo "❌ 可执行文件 aigc-server-doll 不存在，请先运行 make build"
    exit 1
fi

if [ ! -d "lib" ]; then
    echo "❌ lib 目录不存在，请确保动态库文件已复制到 lib 目录"
    exit 1
fi

if [ ! -d "configs" ]; then
    echo "❌ configs 目录不存在"
    exit 1
fi

echo "✅ 所有必要文件检查完成"

# 构建Docker镜像
echo "🔨 开始构建Docker镜像..."

if [ "$DEBUG_MODE" = true ]; then
    # 调试模式：构建时添加debug参数
    docker build \
        --no-cache \
        --tag "${FULL_IMAGE_NAME}" \
        --file Dockerfile \
        --build-arg DEBUG_MODE=true \
        .
else
    # 生产模式：正常构建
    docker build \
        --no-cache \
        --tag "${FULL_IMAGE_NAME}" \
        --file Dockerfile \
        .
fi

# 如果构建失败，尝试使用多阶段构建
if [ $? -ne 0 ] && [ -f "Dockerfile.multistage" ]; then
    echo "⚠️  标准构建失败，尝试多阶段构建..."
    docker build \
        --no-cache \
        --tag "${FULL_IMAGE_NAME}" \
        --file Dockerfile.multistage \
        .
fi

if [ $? -eq 0 ]; then
    echo "✅ Docker镜像构建成功: ${FULL_IMAGE_NAME}"
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images "${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # 验证镜像
    echo "🔍 验证镜像内容..."
    docker run --rm "${FULL_IMAGE_NAME}" ls -la /app/
    
    echo "🎉 构建完成！"
    echo "💡 使用以下命令测试镜像:"
    echo "   docker run --rm -p 8970:8970 -p 8971:8971 ${FULL_IMAGE_NAME}"
else
    echo "❌ Docker镜像构建失败"
    exit 1
fi 