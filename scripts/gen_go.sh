#!/bin/bash

ProtoDir="./proto/protos"
BuildDir="./pkg/proto"

if [ ! -d "${BuildDir}" ]; then
  echo "proto目录不存在, 创建proto目录 ${BuildDir}"
  mkdir -p "${BuildDir}"
fi

# 遍历所有proto文件并生成go代码
for proto_file in ${ProtoDir}/*.proto; do
  protoc -I=${ProtoDir} \
    --go_out="${BuildDir}" \
    --go_opt=paths=source_relative \
    --go-grpc_out="${BuildDir}" \
    --go-grpc_opt=paths=source_relative \
    "${proto_file}"
done