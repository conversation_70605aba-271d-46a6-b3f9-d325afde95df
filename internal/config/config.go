package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	GRPC       GRPCConfig       `mapstructure:"grpc"`
	TCP        TCPConfig        `mapstructure:"tcp"`
	HTTPClient HttpClientConfig `mapstructure:"http_client"`
	Log        LogConfig        `mapstructure:"log"`
	Rtc        RtcConfig        `mapstructure:"rtc"`
	Redis      RedisConfig      `mapstructure:"redis"`
	Debug      DebugConfig      `mapstructure:"debug"`
	HTTPServer HTTPServerConfig `mapstructure:"http_server"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Name    string `mapstructure:"name"`
	Version string `mapstructure:"version"`
	Env     string `mapstructure:"env"`
}

// GRPCConfig gRPC服务配置
type GRPCConfig struct {
	// Port           int    `mapstructure:"port"`
	// Timeout        string `mapstructure:"timeout"`
	// MaxRecvMsgSize int    `mapstructure:"max_recv_msg_size"`
	// MaxSendMsgSize int    `mapstructure:"max_send_msg_size"`
	AsrHost string `mapstructure:"asr_host"`
	TtsHost string `mapstructure:"tts_host"`
	LlmHost string `mapstructure:"llm_host"`
}

// HTTPServerConfig HTTP服务配置
type HTTPServerConfig struct {
	Port int    `mapstructure:"port"`
	Host string `mapstructure:"host"`
}

// TCPConfig TCP服务配置
type TCPConfig struct {
	Port           int    `mapstructure:"port"`
	Host           string `mapstructure:"host"`
	MaxConnections int    `mapstructure:"max_connections"`
	ReadTimeout    string `mapstructure:"read_timeout"`
	WriteTimeout   string `mapstructure:"write_timeout"`
}

type HttpClientConfig struct {
	TimeoutSeconds int    `mapstructure:"timeout_seconds"`
	BaseURL        string `mapstructure:"base_url"`
	MediaBaseURL   string `mapstructure:"media_base_url"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `mapstructure:"level"`
	Format   string `mapstructure:"format"`
	Output   string `mapstructure:"output"`
	FilePath string `mapstructure:"file_path"`
}

// RtcConfig RTC配置
type RtcConfig struct {
	AppID  string `mapstructure:"app_id"`
	AppKey string `mapstructure:"app_key"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Password     string `mapstructure:"password"`
	Database     int    `mapstructure:"database"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
	MaxRetries   int    `mapstructure:"max_retries"`
}

// DebugConfig 调试配置
type DebugConfig struct {
	SaveAudio bool `mapstructure:"save_audio"`
}

// Load 加载配置
func Load(env string) (*Config, error) {
	// 初始化viper
	v := viper.New()
	v.SetConfigName(env)
	v.SetConfigType("yaml")
	v.AddConfigPath("configs")

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 支持环境变量覆盖
	v.SetEnvPrefix("AIGC")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 解析配置
	var cfg Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	LoadedConfig = &cfg

	return &cfg, nil
}

var LoadedConfig *Config
