package service

import (
	"aigc_server/pkg/logger"
	"context"
	"crypto/md5"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// CacheItem 缓存项
type CacheItem struct {
	Value     interface{} // 缓存的值
	ExpiresAt time.Time   // 过期时间
}

// CacheService 缓存服务
type CacheService struct {
	items   sync.Map     // 并发安全的map
	cleanup *time.Ticker // 清理定时器
	ctx     context.Context
	cancel  context.CancelFunc
}

var cacheServiceInstance *CacheService
var cacheServiceOnce sync.Once

// GetCacheServiceInstance 获取缓存服务单例
func GetCacheServiceInstance() *CacheService {
	cacheServiceOnce.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		cacheServiceInstance = &CacheService{
			cleanup: time.NewTicker(5 * time.Minute), // 每5分钟清理一次过期数据
			ctx:     ctx,
			cancel:  cancel,
		}
		// 启动清理协程
		go cacheServiceInstance.startCleanup()
	})
	return cacheServiceInstance
}

// Set 设置缓存，ttl为过期时间（秒），0表示永不过期
func (c *CacheService) Set(key string, value interface{}, ttl int) {
	var expiresAt time.Time
	if ttl > 0 {
		expiresAt = time.Now().Add(time.Duration(ttl) * time.Second)
	}

	item := CacheItem{
		Value:     value,
		ExpiresAt: expiresAt,
	}

	c.items.Store(key, item)
	logger.Debug("缓存设置成功", zap.String("key", key), zap.Int("ttl", ttl))
}

// Get 获取缓存
func (c *CacheService) Get(key string) (interface{}, bool) {
	value, exists := c.items.Load(key)
	if !exists {
		return nil, false
	}

	item := value.(CacheItem)

	// 检查是否过期
	if !item.ExpiresAt.IsZero() && time.Now().After(item.ExpiresAt) {
		c.items.Delete(key)
		logger.Debug("缓存已过期", zap.String("key", key))
		return nil, false
	}

	logger.Debug("缓存命中", zap.String("key", key))
	return item.Value, true
}

// Delete 删除缓存
func (c *CacheService) Delete(key string) {
	c.items.Delete(key)
	logger.Debug("缓存删除", zap.String("key", key))
}

// Clear 清空所有缓存
func (c *CacheService) Clear() {
	c.items.Range(func(key, value interface{}) bool {
		c.items.Delete(key)
		return true
	})
	logger.Info("清空所有缓存")
}

// GetSize 获取缓存大小
func (c *CacheService) GetSize() int {
	count := 0
	c.items.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// startCleanup 启动清理过期数据的协程
func (c *CacheService) startCleanup() {
	for {
		select {
		case <-c.cleanup.C:
			c.cleanupExpired()
		case <-c.ctx.Done():
			c.cleanup.Stop()
			return
		}
	}
}

// cleanupExpired 清理过期数据
func (c *CacheService) cleanupExpired() {
	now := time.Now()
	count := 0

	c.items.Range(func(key, value interface{}) bool {
		item := value.(CacheItem)
		if !item.ExpiresAt.IsZero() && now.After(item.ExpiresAt) {
			c.items.Delete(key)
			count++
		}
		return true
	})

	if count > 0 {
		logger.Debug("清理过期缓存", zap.Int("count", count))
	}
}

// Stop 停止缓存服务
func (c *CacheService) Stop() {
	c.cancel()
}

// GenerateKey 生成缓存key（MD5哈希）
func (c *CacheService) GenerateKey(prefix string, data ...string) string {
	combined := prefix
	for _, d := range data {
		combined += "|" + d
	}
	hash := md5.Sum([]byte(combined))
	return fmt.Sprintf("%x", hash)
}
