package service

import (
	"context"
	"sync/atomic"
	"time"

	"aigc_server/pkg/logger"
	proto "aigc_server/pkg/proto"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type TypeGrpcConnector string

const (
	TypeGrpcConnectorASR TypeGrpcConnector = "ASR"
	TypeGrpcConnectorLLM TypeGrpcConnector = "LLM"
	TypeGrpcConnectorTTS TypeGrpcConnector = "TTS"
)

// StreamHandler 流处理器接口
type StreamHandler interface {
	OnReceive(ctx context.Context, data interface{}) error
}

// ConnectorState 连接器状态
type ConnectorState int

const (
	StateDisconnected ConnectorState = iota // 未连接
	StateConnecting                         // 连接中
	StateConnected                          // 已连接
	StateReconnecting                       // 重连中
	StateClosed                             // 已关闭
)

func (s ConnectorState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateClosed:
		return "Closed"
	default:
		return "Unknown"
	}
}

// ConnectorConfig 连接器配置
type ConnectorConfig struct {
	Host               string        // 服务地址
	MaxRetries         int           // 最大重试次数，0表示无限重试
	InitialBackoff     time.Duration // 初始退避时间
	MaxBackoff         time.Duration // 最大退避时间
	BackoffMultiplier  float64       // 退避倍数
	ReconnectOnFailure bool          // 是否在失败时自动重连
}

// DefaultConnectorConfig 默认连接器配置
func DefaultConnectorConfig(host string) *ConnectorConfig {
	return &ConnectorConfig{
		Host:               host,
		MaxRetries:         0, // 无限重试
		InitialBackoff:     time.Second,
		MaxBackoff:         30 * time.Second,
		BackoffMultiplier:  2.0,
		ReconnectOnFailure: true,
	}
}

// BaseConnector 基础连接器（优化版，减少锁使用）
type BaseConnector struct {
	config    *ConnectorConfig
	conn      *grpc.ClientConn
	state     int32 // 使用atomic操作，对应ConnectorState
	ctx       context.Context
	cancel    context.CancelFunc
	closed    int32            // 使用atomic操作
	streamOps StreamOperations // 流操作实现
	handler   StreamHandler    // 流处理器
}

// NewBaseConnector 创建基础连接器
func NewBaseConnector(config *ConnectorConfig, streamOps StreamOperations, handler StreamHandler) *BaseConnector {
	bc := &BaseConnector{
		config:    config,
		streamOps: streamOps,
		handler:   handler,
	}
	// 初始化atomic字段
	atomic.StoreInt32(&bc.state, int32(StateDisconnected))
	atomic.StoreInt32(&bc.closed, 0)
	return bc
}

func (c *BaseConnector) GetConfig() *ConnectorConfig {
	return c.config
}

func (c *BaseConnector) GetState() ConnectorState {
	return ConnectorState(atomic.LoadInt32(&c.state))
}

func (c *BaseConnector) IsConnected() bool {
	return c.GetState() == StateConnected
}

func (c *BaseConnector) GetConnection() *grpc.ClientConn {
	return c.conn
}

func (c *BaseConnector) setState(state ConnectorState) {
	atomic.StoreInt32(&c.state, int32(state))
	logger.Debug("连接器状态变更",
		zap.String("connector", string(c.streamOps.getConnectorName())),
		zap.String("state", state.String()))
}

func (c *BaseConnector) isClosed() bool {
	return atomic.LoadInt32(&c.closed) != 0
}

func (c *BaseConnector) Start(ctx context.Context) error {
	if c.isClosed() {
		return grpc.ErrClientConnClosing
	}

	c.ctx, c.cancel = context.WithCancel(ctx)

	// 启动连接循环
	go c.connectLoop()

	return nil
}

func (c *BaseConnector) Stop() {
	// 使用atomic操作设置关闭标志，避免重复关闭
	if !atomic.CompareAndSwapInt32(&c.closed, 0, 1) {
		return // 已经关闭了
	}

	// 取消上下文，通知所有goroutine退出
	if c.cancel != nil {
		c.cancel()
	}

	// 简单等待一下让goroutine有时间退出，不使用waitgroup避免死锁
	time.Sleep(100 * time.Millisecond)

	// 关闭连接
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	// 清除流状态
	if c.streamOps != nil {
		c.streamOps.setStream(nil)
	}

	c.setState(StateClosed)
	logger.Info("连接器已停止", zap.String("connector", string(c.streamOps.getConnectorName())))
}

func (c *BaseConnector) connect() error {
	// 检查是否已关闭
	if c.isClosed() {
		return grpc.ErrClientConnClosing
	}

	c.setState(StateConnecting)

	conn, err := grpc.Dial(c.config.Host, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		c.setState(StateDisconnected)
		logger.Error("连接gRPC服务失败",
			zap.String("connector", string(c.streamOps.getConnectorName())),
			zap.String("host", c.config.Host),
			zap.Error(err))
		return err
	}

	c.conn = conn
	c.setState(StateConnected)

	logger.Info("连接gRPC服务成功",
		zap.String("connector", string(c.streamOps.getConnectorName())),
		zap.String("host", c.config.Host))
	go c.onConnectionReady()
	return nil
}

func (c *BaseConnector) connectLoop() {
	defer utils.TraceRecover()

	// 首次连接
	if err := c.connect(); err != nil {
		if !c.config.ReconnectOnFailure || c.isClosed() {
			return
		}
		// 首次连接失败，进入重连循环
		c.reconnectLoop()
		return
	}

	// 首次连接成功，无需重连循环
}

func (c *BaseConnector) reconnectLoop() {
	defer utils.TraceRecover()

	backoff := c.config.InitialBackoff
	attempt := 1

	for {
		// 优先检查context和关闭状态
		select {
		case <-c.ctx.Done():
			return
		default:
		}

		if c.isClosed() {
			return
		}

		// 检查最大重试次数
		if c.config.MaxRetries > 0 && attempt > c.config.MaxRetries {
			logger.Warn("达到最大重试次数，停止重连",
				zap.String("connector", string(c.streamOps.getConnectorName())),
				zap.Int("maxRetries", c.config.MaxRetries))
			return
		}

		c.setState(StateReconnecting)

		logger.Info("正在尝试重新连接",
			zap.String("connector", string(c.streamOps.getConnectorName())),
			zap.Int("attempt", attempt),
			zap.Duration("after", backoff))

		// 等待退避时间
		select {
		case <-time.After(backoff):
		case <-c.ctx.Done():
			return
		}

		// 再次检查是否已关闭
		if c.isClosed() {
			return
		}

		// 尝试重连
		if err := c.connect(); err != nil {
			// 增加退避时间
			backoff = time.Duration(float64(backoff) * c.config.BackoffMultiplier)
			if backoff > c.config.MaxBackoff {
				backoff = c.config.MaxBackoff
			}
			attempt++
			continue
		}

		// 重连成功
		logger.Info("重连成功",
			zap.String("connector", string(c.streamOps.getConnectorName())),
			zap.Int("attempt", attempt))
		return
	}
}

// HandleDisconnection 处理连接断开，触发重连
func (c *BaseConnector) HandleDisconnection(err error) {
	// 获取当前状态，用于判断是否需要重连
	currentState := c.GetState()

	// 关闭旧连接
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	// 设置状态为断开
	c.setState(StateDisconnected)

	// 清除流状态
	if c.streamOps != nil {
		c.streamOps.setStream(nil)
	}

	logger.Warn("检测到连接断开",
		zap.String("connector", string(c.streamOps.getConnectorName())),
		zap.Error(err))

	// 如果当前状态是已连接，且配置了自动重连，且未关闭，则启动重连
	if currentState == StateConnected && c.config.ReconnectOnFailure && !c.isClosed() {
		go c.reconnectLoop()
	}
}

// onConnectionReady 通用的连接就绪处理
func (c *BaseConnector) onConnectionReady() error {
	if err := c.initializeStream(); err != nil {
		logger.Error("初始化流失败",
			zap.String("connector", string(c.streamOps.getConnectorName())),
			zap.Error(err))
		c.HandleDisconnection(err)
		return err
	}
	c.startReceiveLoop()
	logger.Info("连接器已就绪",
		zap.String("connector", string(c.streamOps.getConnectorName())),
		zap.String("host", c.GetConfig().Host))
	return nil
}

// initializeStream 通用的流初始化
func (c *BaseConnector) initializeStream() error {
	conn := c.GetConnection()
	if conn == nil {
		return grpc.ErrClientConnClosing
	}

	stream, err := c.streamOps.createStream(conn, c.ctx)
	if err != nil {
		return err
	}

	c.streamOps.setStream(stream)
	return nil
}

// startReceiveLoop 通用的接收循环
func (c *BaseConnector) startReceiveLoop() {
	go func() {
		defer utils.TraceRecover()

		for {
			// 优先检查context是否取消
			select {
			case <-c.ctx.Done():
				return
			default:
			}

			// 检查是否已关闭
			if c.isClosed() {
				return
			}

			stream := c.streamOps.getStream()
			if stream == nil {
				return
			}

			response, err := c.streamOps.receiveFromStream(stream)
			if err != nil {
				// 不在关闭状态下才处理断开
				if !c.isClosed() {
					c.HandleDisconnection(err)
				}
				return
			}

			if c.handler != nil && !c.isClosed() {
				if err := c.handler.OnReceive(c.ctx, response); err != nil {
					logger.Error("处理响应失败",
						zap.String("connector", string(c.streamOps.getConnectorName())),
						zap.Error(err))
				}
			}
		}
	}()
}

// StreamOperations 流操作接口（子类需要实现的差异化部分）
type StreamOperations interface {
	createStream(conn *grpc.ClientConn, ctx context.Context) (interface{}, error) // 创建具体类型的流
	getStream() interface{}                                                       // 获取当前流
	setStream(stream interface{})                                                 // 设置流
	receiveFromStream(stream interface{}) (interface{}, error)                    // 从流接收数据
	getConnectorName() TypeGrpcConnector                                          // 获取连接器名称
}

// ASRStreamConnector ASR流连接器
type ASRStreamConnector struct {
	*BaseConnector
	stream proto.ASR_ASRClient // 存储proto.ASR_ASRClient，使用atomic.Value避免锁
}

// NewASRStreamConnector 创建ASR流连接器
func NewASRStreamConnector(config *ConnectorConfig, handler StreamHandler) *ASRStreamConnector {
	connector := &ASRStreamConnector{}

	// 将自己作为StreamOperations传给BaseConnector
	connector.BaseConnector = NewBaseConnector(config, connector, handler)
	return connector
}

// 实现StreamOperations接口
func (c *ASRStreamConnector) createStream(conn *grpc.ClientConn, ctx context.Context) (interface{}, error) {
	client := proto.NewASRClient(conn)
	return client.ASR(ctx)
}

func (c *ASRStreamConnector) getStream() interface{} {
	return c.stream
}

func (c *ASRStreamConnector) setStream(stream interface{}) {
	if stream == nil {
		c.stream = nil
		return
	}
	c.stream = stream.(proto.ASR_ASRClient)
}

func (c *ASRStreamConnector) receiveFromStream(stream interface{}) (interface{}, error) {
	asrStream := stream.(proto.ASR_ASRClient)
	return asrStream.Recv()
}

func (c *ASRStreamConnector) getConnectorName() TypeGrpcConnector {
	return TypeGrpcConnectorASR
}

// SendAudio 发送音频数据
func (c *ASRStreamConnector) SendAudio(audio []byte) error {
	if c.isClosed() {
		return grpc.ErrClientConnClosing
	}

	stream := c.stream
	if stream == nil {
		return grpc.ErrClientConnClosing
	}

	request := &proto.ASRRequest{
		Audio: audio,
	}

	error := stream.Send(request)
	if error != nil {
		c.HandleDisconnection(error)
		return error
	}
	return nil
}

// LLMStreamConnector LLM流连接器
type LLMStreamConnector struct {
	*BaseConnector
	stream proto.LLMChat_ChatClient // 存储proto.LLMChat_ChatClient
}

// NewLLMStreamConnector 创建LLM流连接器
func NewLLMStreamConnector(config *ConnectorConfig, handler StreamHandler) *LLMStreamConnector {
	connector := &LLMStreamConnector{}

	// 将自己作为StreamOperations传给BaseConnector
	connector.BaseConnector = NewBaseConnector(config, connector, handler)
	return connector
}

// 实现StreamOperations接口
func (c *LLMStreamConnector) createStream(conn *grpc.ClientConn, ctx context.Context) (interface{}, error) {
	client := proto.NewLLMChatClient(conn)
	return client.Chat(ctx)
}

func (c *LLMStreamConnector) getStream() interface{} {
	return c.stream
}

func (c *LLMStreamConnector) setStream(stream interface{}) {
	if stream == nil {
		c.stream = nil
		return
	}
	c.stream = stream.(proto.LLMChat_ChatClient)
}

func (c *LLMStreamConnector) receiveFromStream(stream interface{}) (interface{}, error) {
	llmStream := stream.(proto.LLMChat_ChatClient)
	return llmStream.Recv()
}

func (c *LLMStreamConnector) getConnectorName() TypeGrpcConnector {
	return TypeGrpcConnectorLLM
}

// SendMessage 发送消息
func (c *LLMStreamConnector) SendMessage(request *proto.LLMRequest) error {
	if c.isClosed() {
		return grpc.ErrClientConnClosing
	}

	stream := c.stream
	if stream == nil {
		return grpc.ErrClientConnClosing
	}

	error := stream.Send(request)
	if error != nil {
		c.HandleDisconnection(error)
		return error
	}
	return nil
}

// TTSStreamConnector TTS流连接器
type TTSStreamConnector struct {
	*BaseConnector
	stream proto.TTS_TTSClient // 存储proto.TTS_TTSClient
}

// NewTTSStreamConnector 创建TTS流连接器
func NewTTSStreamConnector(config *ConnectorConfig, handler StreamHandler) *TTSStreamConnector {
	connector := &TTSStreamConnector{}

	// 将自己作为StreamOperations传给BaseConnector
	connector.BaseConnector = NewBaseConnector(config, connector, handler)
	return connector
}

// 实现StreamOperations接口
func (c *TTSStreamConnector) createStream(conn *grpc.ClientConn, ctx context.Context) (interface{}, error) {
	client := proto.NewTTSClient(conn)
	return client.TTS(ctx)
}

func (c *TTSStreamConnector) getStream() interface{} {
	return c.stream
}

func (c *TTSStreamConnector) setStream(stream interface{}) {
	if stream == nil {
		c.stream = nil
		return
	}
	c.stream = stream.(proto.TTS_TTSClient)
}

func (c *TTSStreamConnector) receiveFromStream(stream interface{}) (interface{}, error) {
	ttsStream := stream.(proto.TTS_TTSClient)
	return ttsStream.Recv()
}

func (c *TTSStreamConnector) getConnectorName() TypeGrpcConnector {
	return TypeGrpcConnectorTTS
}

// SendText 发送文本
func (c *TTSStreamConnector) SendText(request *proto.TTSRequest) error {
	if c.isClosed() {
		return grpc.ErrClientConnClosing
	}

	stream := c.stream
	if stream == nil {
		return grpc.ErrClientConnClosing
	}

	error := stream.Send(request)
	if error != nil {
		c.HandleDisconnection(error)
		return error
	}
	return nil
}
