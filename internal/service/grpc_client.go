package service

import (
	"context"
	"encoding/json"
	"sync"
	"sync/atomic"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	proto "aigc_server/pkg/proto"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

var ClientGRPC *GRPCClient

type IGRPCClientHandler interface {
	OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error
	OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error
	OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error
}

// GRPCClientMultiHandler 多个处理器的组合
type GRPCClientMultiHandler struct {
	IGRPCClientHandler
	handlers []IGRPCClientHandler
}

func NewGRPCClientMultiHandler() *GRPCClientMultiHandler {
	return &GRPCClientMultiHandler{
		handlers: make([]IGRPCClientHandler, 0),
	}
}

func (h *GRPCClientMultiHandler) AddHandler(handler IGRPCClientHandler) {
	h.handlers = append(h.handlers, handler)
}

func (h *GRPCClientMultiHandler) RemoveHandler(handler IGRPCClientHandler) {
	h.handlers = utils.Filter(h.handlers, func(h IGRPCClientHandler) bool {
		return h != handler
	})
}

func (h *GRPCClientMultiHandler) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	GetEventEmitter().SyncEmit(types.EventType_OnLlmResponse, response)
	for _, handler := range h.handlers {
		if err := handler.OnLlmResponse(ctx, response); err != nil {
			return err
		}
	}
	return nil
}

func (h *GRPCClientMultiHandler) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	GetEventEmitter().SyncEmit(types.EventType_OnAsrResponse, response)
	for _, handler := range h.handlers {
		if err := handler.OnAsrResponse(ctx, response); err != nil {
			return err
		}
	}
	return nil
}

func (h *GRPCClientMultiHandler) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	logger.Debug("收到TTS响应", zap.Int32("index", response.MsgIndex), zap.Int32("segmentIndex", response.MsgSegmentIndex), zap.Int32("error_code", response.ErrorCode), zap.Bool("is_final", response.IsFinal), zap.Int("audio_size", len(response.Audio)), zap.Int32("request_id", response.RequestId))

	GetEventEmitter().SyncEmit(types.EventType_OnTtsResponse, response)
	for _, handler := range h.handlers {
		if err := handler.OnTtsResponse(ctx, response); err != nil {
			return err
		}
	}
	return nil
}

// StreamHandlerAdapter 流处理器适配器，将新接口转换为原来的处理逻辑
type StreamHandlerAdapter struct {
	client *GRPCClient
	sType  TypeGrpcConnector
}

func (s *StreamHandlerAdapter) OnReceive(ctx context.Context, data interface{}) error {
	if s.client.closed {
		return nil
	}
	switch s.sType {
	case TypeGrpcConnectorASR:
		if response, ok := data.(*proto.ASRResponse); ok {
			return s.client.OnAsrResponse(ctx, response)
		}
	case TypeGrpcConnectorLLM:
		if response, ok := data.(*proto.LLMResponse); ok {
			return s.client.OnLlmResponse(ctx, response)
		}
	case TypeGrpcConnectorTTS:
		if response, ok := data.(*proto.TTSResponse); ok {
			return s.client.OnTtsResponse(ctx, response)
		}
	}
	return nil
}

// GRPCClient 重构后的gRPC客户端管理器
type GRPCClient struct {
	IGRPCClientHandler
	cfg     *config.Config
	uid     string
	ctx     context.Context
	cancel  context.CancelFunc
	handler *GRPCClientMultiHandler

	// Connectors (使用新的流连接器)
	asrConnector *ASRStreamConnector
	llmConnector *LLMStreamConnector
	ttsConnector *TTSStreamConnector

	// TTS请求管理（优化后，减少锁使用）
	ttsIncrRequestId int32    // 使用atomic操作
	ttsResMap        sync.Map // 使用sync.Map避免读写锁

	initialized bool
	closed      bool
}

// NewGRPCClient 创建重构后的gRPC客户端
func NewGRPCClient(cfg *config.Config, uid string) (*GRPCClient, error) {
	md := metadata.New(map[string]string{
		"uid": uid,
	})
	// 创建上下文
	ctx := metadata.NewOutgoingContext(context.Background(), md)
	ctx, cancel := context.WithCancel(ctx)

	client := &GRPCClient{
		cfg:              cfg,
		uid:              uid,
		ctx:              ctx,
		cancel:           cancel,
		handler:          NewGRPCClientMultiHandler(),
		ttsIncrRequestId: 0,
		// ttsResMap 使用sync.Map，无需初始化
	}

	// 初始化连接器
	if err := client.initConnectors(); err != nil {
		logger.Error("初始化gRPC连接器失败", zap.Error(err))
		cancel()
		return nil, err
	}

	client.initialized = true
	return client, nil
}

// initConnectors 初始化所有连接器
func (c *GRPCClient) initConnectors() error {
	logger.Info("初始化gRPC连接器", zap.String("uid", c.uid))

	// 初始化ASR连接器
	if c.cfg.GRPC.AsrHost != "" {
		config := DefaultConnectorConfig(c.cfg.GRPC.AsrHost)
		handler := &StreamHandlerAdapter{client: c, sType: TypeGrpcConnectorASR}
		c.asrConnector = NewASRStreamConnector(config, handler)

		if err := c.asrConnector.Start(c.ctx); err != nil {
			logger.Error("启动ASR连接器失败", zap.Error(err))
		}
	}

	// 初始化LLM连接器
	if c.cfg.GRPC.LlmHost != "" {
		config := DefaultConnectorConfig(c.cfg.GRPC.LlmHost)
		handler := &StreamHandlerAdapter{client: c, sType: TypeGrpcConnectorLLM}
		c.llmConnector = NewLLMStreamConnector(config, handler)

		if err := c.llmConnector.Start(c.ctx); err != nil {
			logger.Error("启动LLM连接器失败", zap.Error(err))
		}
	}

	// 初始化TTS连接器
	if c.cfg.GRPC.TtsHost != "" {
		config := DefaultConnectorConfig(c.cfg.GRPC.TtsHost)
		handler := &StreamHandlerAdapter{client: c, sType: TypeGrpcConnectorTTS}
		c.ttsConnector = NewTTSStreamConnector(config, handler)

		if err := c.ttsConnector.Start(c.ctx); err != nil {
			logger.Error("启动TTS连接器失败", zap.Error(err))
		}
	}

	logger.Info("gRPC连接器初始化完成", zap.String("uid", c.uid))
	return nil
}

func (c *GRPCClient) AddHandler(handler IGRPCClientHandler) {
	c.handler.AddHandler(handler)
}

func (c *GRPCClient) RemoveHandler(handler IGRPCClientHandler) {
	c.handler.RemoveHandler(handler)
}

// Close 关闭所有连接
func (c *GRPCClient) Close() {
	if c.closed {
		return
	}
	c.closed = true

	// 取消上下文，通知所有 goroutine 退出
	c.cancel()

	// 关闭所有连接器
	if c.asrConnector != nil {
		c.asrConnector.Stop()
	}
	if c.llmConnector != nil {
		c.llmConnector.Stop()
	}
	if c.ttsConnector != nil {
		c.ttsConnector.Stop()
	}

	// 清理TTS响应映射
	c.ttsResMap.Range(func(key, value interface{}) bool {
		if ch, ok := value.(chan *proto.TTSResponse); ok {
			close(ch)
		}
		c.ttsResMap.Delete(key)
		return true
	})

	logger.Info("gRPC客户端已关闭", zap.String("uid", c.uid))
}

// SendASRAudio 发送音频数据到ASR服务
func (c *GRPCClient) SendASRAudio(audio []byte) error {
	if c.closed || c.asrConnector == nil {
		return nil
	}
	logger.Debug("发送ASR音频数据", zap.String("uid", c.uid), zap.Int("audio_size", len(audio)))

	return c.asrConnector.SendAudio(audio)
}

// SendLLMMessage 发送消息到LLM服务
func (c *GRPCClient) SendLLMMessage(content string, isFinal bool, toolCall *map[string]string) error {
	if c.closed || c.llmConnector == nil {
		return nil
	}
	logger.Debug("发送LLM消息", zap.String("uid", c.uid), zap.String("content", content))

	var reqType proto.LLMReqType
	var toolCallJSON string
	if toolCall != nil && len(*toolCall) > 0 {
		toolCallBytes, err := json.Marshal(*toolCall)
		if err != nil {
			logger.Error("序列化工具调用失败", zap.Error(err))
			return err
		}
		toolCallJSON = string(toolCallBytes)
		reqType = proto.LLMReqType_AIGC
	} else {
		toolCallJSON = "{}"
		reqType = proto.LLMReqType_USER
	}

	request := &proto.LLMRequest{
		Content:  content,
		IsFinal:  isFinal,
		ToolCall: toolCallJSON,
		Type:     reqType,
	}

	return c.llmConnector.SendMessage(request)
}

// SendTTSText 发送文本到TTS服务
func (c *GRPCClient) SendTTSText(text string, index int32, seqment_index int32, voiceType string) error {
	if c.closed || c.ttsConnector == nil {
		return nil
	}
	logger.Debug("发送TTS文本", zap.String("uid", c.uid), zap.String("text", text), zap.Int32("index", index), zap.Int32("segment_index", seqment_index), zap.String("voiceType", voiceType))

	request := &proto.TTSRequest{
		Text:            text,
		MsgIndex:        index,
		MsgSegmentIndex: seqment_index,
		VoiceType:       voiceType,
		RequestId:       0,
	}

	return c.ttsConnector.SendText(request)
}

// SendTTSStatelessForResponse 发送无状态TTS请求并等待响应
func (c *GRPCClient) SendTTSStatelessForResponse(text string, voiceType string) ([]*proto.TTSResponse, error) {
	if c.closed || c.ttsConnector == nil {
		return nil, nil
	}

	logger.Debug("发送TTS文本", zap.String("uid", c.uid), zap.String("text", text))

	// 生成请求ID（使用atomic操作）
	requestId := atomic.AddInt32(&c.ttsIncrRequestId, 1)
	responseChan := make(chan *proto.TTSResponse, 10) // 缓冲channel
	c.ttsResMap.Store(requestId, responseChan)

	// 发送请求后清理资源
	defer func() {
		if ch, loaded := c.ttsResMap.LoadAndDelete(requestId); loaded {
			if responseCh, ok := ch.(chan *proto.TTSResponse); ok {
				close(responseCh)
			}
		}
	}()

	request := &proto.TTSRequest{
		Text:      text,
		VoiceType: voiceType,
		RequestId: requestId,
	}

	err := c.ttsConnector.SendText(request)
	if err != nil {
		return nil, err
	}

	// 收集响应
	result := make([]*proto.TTSResponse, 0)
	timeout := 500 * time.Millisecond
	timer := time.NewTimer(timeout)
	defer timer.Stop()

	for {
		select {
		case res := <-responseChan:
			timer.Reset(timeout)
			if res != nil && len(res.Audio) > 0 {
				result = append(result, res)
			}
			if res != nil && res.IsFinal {
				return result, nil
			}
		case <-timer.C:
			return result, nil
		case <-c.ctx.Done():
			return result, c.ctx.Err()
		}
	}
}

// SendLLMToDisconnect 发送LLM断开连接消息
func (c *GRPCClient) SendLLMToDisconnect() error {
	disconnectContent := "<disconnect>"
	logger.Info("发送LLM断开连接消息", zap.String("disconnectContent", disconnectContent), zap.String("uid", c.uid))
	return c.SendLLMMessage(disconnectContent, true, nil)
}

// 实现IGRPCClientHandler接口
func (c *GRPCClient) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	return c.handler.OnAsrResponse(ctx, response)
}

func (c *GRPCClient) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	// 处理请求ID响应映射
	if response.RequestId != 0 {
		if ch, exists := c.ttsResMap.Load(response.RequestId); exists {
			if responseChan, ok := ch.(chan *proto.TTSResponse); ok {
				select {
				case responseChan <- response:
				default:
					// channel已满，丢弃
				}
			}
		}
	}
	return c.handler.OnTtsResponse(ctx, response)
}

func (c *GRPCClient) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	return c.handler.OnLlmResponse(ctx, response)
}
