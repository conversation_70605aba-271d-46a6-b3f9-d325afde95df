package service

import (
	"sync"
	"sync/atomic"

	"aigc_server/internal/worker/types"
	"aigc_server/pkg/utils"
)

type EventType = types.EventType

type EventHandlerCallback func(event EventType, data interface{})
type EventHandler struct {
	callback EventHandlerCallback
	id       int64
	owner    interface{}
}

type EventEmitter struct {
	incID  int64
	events map[EventType][]EventHandler
	mutex  sync.RWMutex
}

var (
	eventEmitter *EventEmitter
	once         sync.Once
)

func GetEventEmitter() *EventEmitter {
	once.Do(func() {
		eventEmitter = &EventEmitter{
			incID:  0,
			events: make(map[EventType][]EventHandler),
		}
	})
	return eventEmitter
}

func (e *EventEmitter) GetIncID() int64 {
	return atomic.AddInt64(&e.incID, 1)
}

func (e *EventEmitter) SyncEmit(event EventType, data interface{}) {
	defer utils.TraceRecover()
	e.mutex.RLock()
	handlers, ok := e.events[event]
	e.mutex.RUnlock()

	if !ok {
		return
	}
	for _, handler := range handlers {
		handler.callback(event, data)
	}
}

func (e *EventEmitter) On(event EventType, callback EventHandlerCallback, owner interface{}) int64 {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	handler := EventHandler{
		callback: callback,
		id:       e.GetIncID(),
		owner:    owner,
	}

	e.events[event] = append(e.events[event], handler)

	return handler.id
}

func (e *EventEmitter) Off(event EventType, id int64) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	handlers, ok := e.events[event]
	if !ok {
		return
	}
	for i, h := range handlers {
		if h.id == id {
			e.events[event] = append(handlers[:i], handlers[i+1:]...)
			break
		}
	}
}

func (e *EventEmitter) OffByOwner(owner interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	for event, handlers := range e.events {
		newHandlers := make([]EventHandler, 0)
		for _, h := range handlers {
			if h.owner != owner {
				newHandlers = append(newHandlers, h)
			}
		}
		e.events[event] = newHandlers
	}
}

func (e *EventEmitter) ClearEvent(event EventType) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	delete(e.events, event)
}

func (e *EventEmitter) Clear() {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.events = make(map[EventType][]EventHandler)
}
