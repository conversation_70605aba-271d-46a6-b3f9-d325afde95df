package service

import (
	"aigc_server/internal/config"
	"aigc_server/pkg/logger"
	MyRedis "aigc_server/pkg/myredis"
	"aigc_server/pkg/utils"
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	RedisKeyPrefix      = "aigc:groupchat"
	MemberToTeamHashKey = RedisKeyPrefix + ":member2team"
	QueueKeyPrefix      = RedisKeyPrefix + ":queue"
)

type GroupChatTeamType string

const (
	GroupChatTeamType_FriendsVoiceChat GroupChatTeamType = "friends_chat"
	GroupChatTeamType_ParentVoiceChat  GroupChatTeamType = "parent_chat"
)

var GroupChatServiceInstance *GroupChatService
var groupChatServiceOnce sync.Once

type GroupChatService struct {
	redisClient       *redis.Client
	rwmutexTransports sync.RWMutex
	Transports        map[string]*GroupChatTransport
}

func InitGroupChatService(ctxParent context.Context) *GroupChatService {
	groupChatServiceOnce.Do(func() {
		GroupChatServiceInstance = &GroupChatService{
			redisClient: MyRedis.GetClient(),
			Transports:  make(map[string]*GroupChatTransport),
		}
	})
	return GroupChatServiceInstance
}

func (s *GroupChatService) CreateTeam(ctx context.Context, members []string, teamType GroupChatTeamType) (string, error) {
	if len(members) < 2 {
		return "", errors.New("invalid members:" + strings.Join(members, ",") + " 成员必须至少2个")
	}
	teamId := s.BuildTeamId(members, teamType)

	for _, member := range members {
		err := s.redisClient.HSet(ctx, MemberToTeamHashKey, member, teamId).Err()
		if err != nil {
			return "", err
		}
	}
	return teamId, nil
}

func (s *GroupChatService) RemoveTeam(ctx context.Context, teamId string) {
	members, _ := s.ParseTeamId(teamId)
	for _, m := range members {
		s.redisClient.HDel(ctx, MemberToTeamHashKey, m)
	}
}

func (s *GroupChatService) FindTeam(ctx context.Context, member string) (string, error) {
	teamId, err := s.redisClient.HGet(ctx, MemberToTeamHashKey, member).Result()
	if err != nil || teamId == "" {
		return "", errors.New("未找到:" + member + " 的Team群组")
	}
	return teamId, nil
}
func (s *GroupChatService) CheckTeamValid(ctx context.Context, teamId string) error {
	members, _ := s.ParseTeamId(teamId)
	if len(members) < 2 {
		return errors.New("invalid teamId:" + teamId + " 成员必须至少2个")
	}
	for _, m := range members {
		foundTeamId, err := s.FindTeam(ctx, m)
		if err != nil {
			return err
		}
		if foundTeamId != teamId {
			return errors.New("member:" + m + " 不在 team:" + teamId + " 中, 但在 team:" + foundTeamId + " 中")
		}
	}
	return nil
}

func (s *GroupChatService) BuildTeamId(members []string, teamType GroupChatTeamType) string {
	membersSrt := strings.Join(members, "&")
	return fmt.Sprintf("%s|%s", membersSrt, teamType)
}
func (s *GroupChatService) ParseTeamId(teamId string) ([]string, GroupChatTeamType) {
	if teamId == "" {
		return []string{}, ""
	}
	parts := strings.Split(teamId, "|")
	if len(parts) != 2 {
		return []string{}, ""
	}
	return strings.Split(parts[0], "&"), GroupChatTeamType(parts[1])
}

func (s *GroupChatService) GetTransport(teamId string) *GroupChatTransport {
	s.rwmutexTransports.RLock()
	defer s.rwmutexTransports.RUnlock()
	return s.Transports[teamId]
}

func (s *GroupChatService) StartGroupChatTransport(ctxParent context.Context, memberMe string, dataChan chan []byte) (*GroupChatTransport, error) {
	ctx, cancel := context.WithCancel(ctxParent)
	transport := &GroupChatTransport{
		ctx:              ctx,
		cancel:           cancel,
		redisClient:      s.redisClient,
		GroupChatService: s,
		DataChan:         dataChan,
	}
	teamId, err := s.FindTeam(ctx, memberMe)
	if err != nil {
		return nil, err
	}
	transport.TeamId = teamId

	members, _ := s.ParseTeamId(teamId)
	for _, member := range members {
		if member != memberMe {
			transport.ToMember = member
		} else {
			transport.MemberMe = member
		}
	}
	if transport.ToMember == "" || transport.MemberMe == "" {
		logger.Error("StartGroupChatTransport Team成员不正确", zap.String("teamId", teamId))
		return nil, errors.New("Team成员不正确:" + teamId)
	}
	transport.ReceiveQueueKey = fmt.Sprintf("%s:%s:%s", QueueKeyPrefix, teamId, transport.MemberMe)
	transport.SendQueueKey = fmt.Sprintf("%s:%s:%s", QueueKeyPrefix, teamId, transport.ToMember)
	s.redisClient.Unlink(ctx, transport.ReceiveQueueKey, transport.SendQueueKey)

	s.rwmutexTransports.Lock()
	s.Transports[transport.TeamId] = transport
	s.rwmutexTransports.Unlock()

	go transport.Handle()

	return transport, nil
}

type GroupChatTransport struct {
	ctx              context.Context
	cancel           context.CancelFunc
	GroupChatService *GroupChatService
	redisClient      *redis.Client

	DataChan        chan []byte
	MemberMe        string
	ToMember        string
	TeamId          string
	ReceiveQueueKey string
	SendQueueKey    string
}

func (s *GroupChatTransport) Cleanup() {
	logger.Info("GroupChatTransport 清理中", zap.String("teamId", s.TeamId), zap.String("memberMe", s.MemberMe), zap.String("toMember", s.ToMember))
	ctx := context.Background()
	s.redisClient.Del(ctx, s.ReceiveQueueKey, s.SendQueueKey)
	s.GroupChatService.RemoveTeam(ctx, s.TeamId)
}

func (s *GroupChatTransport) TeamAvailable() bool {
	teamId, err := s.GroupChatService.FindTeam(s.ctx, s.MemberMe)
	if err != nil || teamId == "" || teamId != s.TeamId {
		logger.Error("GroupChatTransport 不可用", zap.String("foundTeamId", teamId), zap.String("teamId", s.TeamId), zap.String("memberMe", s.MemberMe), zap.String("toMember", s.ToMember))
		return false
	}
	return true
}

func (s *GroupChatTransport) SendBytes(data []byte) error {
	queueLen := s.redisClient.LLen(s.ctx, s.SendQueueKey).Val()
	if queueLen > 500 {
		logger.Error("GroupChat,发送队列已满", zap.String("queueKey", s.SendQueueKey), zap.Int64("queueLen", queueLen))
		s.Stop()
		return errors.New("队列已满")
	}
	err := s.redisClient.RPush(s.ctx, s.SendQueueKey, data).Err()
	if err != nil {
		return err
	}
	return nil
}
func (s *GroupChatTransport) Stop() {
	logger.Info("GroupChatTransport 停止中", zap.String("teamId", s.TeamId), zap.String("memberMe", s.MemberMe), zap.String("toMember", s.ToMember))
	s.cancel()
	s.GroupChatService.rwmutexTransports.Lock()
	delete(s.GroupChatService.Transports, s.TeamId)
	s.GroupChatService.rwmutexTransports.Unlock()
	s.Cleanup()
}
func (s *GroupChatTransport) Handle() {
	defer utils.TraceRecover()

	var debugSaveAudioChan chan []byte
	if config.LoadedConfig.Debug.SaveAudio {
		debugSaveAudioChan = utils.ContinueWriteAppendData(s.ctx, utils.GetGroupChatSaveFilePath(s.ToMember, s.MemberMe))
		// defer func() {
		// 	close(debugSaveAudioChan)
		// }()
	}
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			result, err := s.redisClient.BLPop(s.ctx, 500*time.Millisecond, s.ReceiveQueueKey).Result()
			if err == redis.Nil {
				continue
			}
			if err != nil {
				logger.Error("StartHandleGroupChat 获取消息失败", zap.Error(err))
				continue
			}
			if len(result) < 2 {
				logger.Error("StartHandleGroupChat 结果异常", zap.String("queueKey", s.ReceiveQueueKey), zap.Any("result", result))
				continue
			}
			select {
			case <-s.ctx.Done():
				return
			case s.DataChan <- []byte(result[1]):
				logger.Debug("GroupChatTransport 接收数据", zap.String("queueKey", s.ReceiveQueueKey), zap.Int("len", len(result[1])), zap.String("memberMe", s.MemberMe), zap.String("toMember", s.ToMember))
				if debugSaveAudioChan != nil {
					select {
					case debugSaveAudioChan <- []byte(result[1]):
					default:
						logger.Warn("GroupChatTransport ContinueWriteAppendData debugSaveAudioChan已满或已关闭,丢弃音频帧")
					}
				}
			default:
				logger.Error("StartHandleGroupChat 数据通道已满或已关闭", zap.String("queueKey", s.ReceiveQueueKey))
			}
		}
	}
}
