package service

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"go.uber.org/zap"
)

type MediaType string

const (
	MediaType_Story MediaType = "story"
	MediaType_Music MediaType = "music"
	MediaType_Audio MediaType = "audio"
)

var MediaUrlPathMap = map[MediaType]string{
	MediaType_Story: "tell-story",
	MediaType_Music: "play-music",
	MediaType_Audio: "play-audio",
}

// MediaInfo 媒体信息
type MediaInfo struct {
	Xid        string `json:"xid"`
	Name       string `json:"name"`
	Url        string `json:"url"`
	Content    string `json:"content"`
	MediaType  string `json:"media_type"`
	PCM        []byte `json:"pcm"`
	SampleRate int    `json:"sample_rate"`
}

type MediaInfoResponse struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    MediaInfo `json:"data"`
}

type TimbreInfoPayload struct {
	DollId   string `json:"dollId"`
	TimbreId string `json:"timbreId"`
}
type TimbreInfoResponse struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    TimbreInfoPayload `json:"data"`
}

type AddFriendResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		IsSuccess bool   `json:"isSuccess"`
		Audio     string `json:"audio"`
	} `json:"data"`
}

// CallHintAudioResponse 通话音频响应
type CallHintAudioResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		CallStartAudio  string `json:"callStartAudio"`
		CallFinishAudio string `json:"callFinishAudio"`
	} `json:"data"`
}

// CallStartRequest 开始通话请求
type CallStartRequest struct {
	DollId       string `json:"dollId"`
	FriendDollID string `json:"friendDollID"`
	StartTime    string `json:"startTime"`
}

// CallStartResponse 开始通话响应
type CallStartResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// CallEndRequest 结束通话请求
type CallEndRequest struct {
	DollId       string `json:"dollId"`
	FriendDollID string `json:"friendDollID"`
	StartTime    string `json:"startTime"`
	Duration     int    `json:"duration"`
}

// CallEndResponse 结束通话响应
type CallEndResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

type UploadWifiListResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type MediaService struct {
	baseURL             string
	cache               *CacheService // 缓存服务
	cachedCallHintAudio struct {
		startAudio  []byte
		finishAudio []byte
	}
}

var mediaServiceInstance *MediaService
var mediaServiceInstanceOnce sync.Once

func GetMediaServiceInstance() *MediaService {
	mediaServiceInstanceOnce.Do(func() {
		mediaServiceInstance = &MediaService{
			baseURL: config.LoadedConfig.HTTPClient.MediaBaseURL,
			cache:   GetCacheServiceInstance(), // 初始化缓存服务
		}
	})
	return mediaServiceInstance
}

func (m *MediaService) GetMediaInfo(ctx context.Context, mediaType MediaType, content string) (*MediaInfo, error) {
	apiPath := MediaUrlPathMap[mediaType]
	// 构建请求URL
	url := fmt.Sprintf("%s/%s?name=%s", m.baseURL, apiPath, content)

	// 请求媒体信息
	respBody, err := DoRequest(ctx, "GET", url, nil, map[string]string{
		"Authorization": "b837o#G@j@8GbFH@",
	}, 10)
	if err != nil {
		return nil, fmt.Errorf("请求媒体信息失败: %w,url: %s", err, url)
	}

	mediaInfoResp := MediaInfoResponse{}
	err = json.Unmarshal(respBody, &mediaInfoResp)
	if err != nil {
		return nil, fmt.Errorf("解析媒体信息失败: %w,url: %s", err, url)
	}

	logger.Info("获取工具调用成功", zap.Any("MediaInfo", mediaInfoResp.Data))
	mediaInfoResp.Data.SampleRate = constant.RtcPushAudioSampleRate

	return &mediaInfoResp.Data, nil
}

func (m *MediaService) StartSyncTimbre(ctx context.Context, dollId string) (string, error) {
	url := fmt.Sprintf("%s/timbre/info?dollId=%s", m.baseURL, dollId)
	body, err := DoRequest(ctx, "GET", url, nil, nil, 5)
	if err != nil {
		logger.Error("获取音色ID失败", zap.Error(err))
		return "", err
	} else {
		logger.Info("获取音色ID成功", zap.String("body", string(body)))
		result := TimbreInfoResponse{}
		err = json.Unmarshal(body, &result)
		if err != nil {
			return "", err
		}
		return result.Data.TimbreId, nil
	}
}

func (m *MediaService) RequestAddFriend(ctx context.Context, dollId string, friendId string) (*AddFriendResponse, error) {
	url := fmt.Sprintf("%s/friend/add", m.baseURL)
	reqBody := map[string]string{
		"myDollID":     dollId,
		"friendDollID": friendId,
	}
	body, err := DoRequest(ctx, "POST", url, reqBody, nil, 10)
	if err != nil {
		logger.Error("添加好友失败,http请求失败", zap.Error(err),
			zap.String("url", url),
			zap.Any("reqBody", reqBody),
			zap.Any("body", string(body)))
		return nil, err
	}
	result := AddFriendResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Error("添加好友失败,解析失败", zap.Error(err))
		return nil, err
	}
	return &result, nil
}

// GetHintAudio 获取通话音频（拨号音、响铃音）
func (m *MediaService) GetHintAudio(ctx context.Context) (*CallHintAudioResponse, error) {
	url := fmt.Sprintf("%s/call/hint-audio", m.baseURL)

	body, err := DoRequest(ctx, "GET", url, nil, nil, 10)
	if err != nil {
		logger.Error("获取通话音频失败", zap.Error(err), zap.String("url", url))
		return nil, fmt.Errorf("获取通话音频失败: %w", err)
	}

	result := CallHintAudioResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Error("解析通话音频响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析通话音频响应失败: %w", err)
	}

	logger.Info("获取通话音频成功", zap.Any("callVoice", result.Data))
	return &result, nil
}

func (m *MediaService) GetCachedCallHintAudio(ctx context.Context) ([]byte, []byte, error) {
	if m.cachedCallHintAudio.startAudio == nil || m.cachedCallHintAudio.finishAudio == nil {
		callHintAudio, err := m.GetHintAudio(ctx)
		if err != nil {
			return nil, nil, err
		}
		format := audio.AudioPCMFormat{
			SampleRate: constant.RtcPushAudioSampleRate,
			Channels:   constant.AudioChannel,
			BitDepth:   constant.SampleBitDepth,
		}
		startAudioUrl := callHintAudio.Data.CallStartAudio
		finishAudioUrl := callHintAudio.Data.CallFinishAudio
		startAudio, err := m.DownloadAudioToPCM(ctx, startAudioUrl, &format)
		if err != nil {
			logger.Error("获取开始音失败", zap.Error(err))
			return nil, nil, err
		}
		finishAudio, err := m.DownloadAudioToPCM(ctx, finishAudioUrl, &format)
		if err != nil {
			logger.Error("获取结束音失败", zap.Error(err))
			return nil, nil, err
		}
		m.cachedCallHintAudio.startAudio = startAudio
		m.cachedCallHintAudio.finishAudio = finishAudio
	}
	return m.cachedCallHintAudio.startAudio, m.cachedCallHintAudio.finishAudio, nil
}

// StartCall 开始通话记录
func (m *MediaService) StartCall(ctx context.Context, dollId, friendDollID, startTime string) (*CallStartResponse, error) {
	url := fmt.Sprintf("%s/call/start", m.baseURL)

	reqBody := CallStartRequest{
		DollId:       dollId,
		FriendDollID: friendDollID,
		StartTime:    startTime,
	}

	body, err := DoRequest(ctx, "POST", url, reqBody, nil, 10)
	if err != nil {
		logger.Error("开始通话记录失败", zap.Error(err),
			zap.String("url", url),
			zap.Any("reqBody", reqBody))
		return nil, fmt.Errorf("开始通话记录失败: %w", err)
	}

	result := CallStartResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Error("解析开始通话响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析开始通话响应失败: %w", err)
	}

	logger.Info("开始通话记录成功", zap.String("dollId", dollId),
		zap.String("friendDollID", friendDollID), zap.String("startTime", startTime))
	return &result, nil
}

// EndCall 结束通话记录
func (m *MediaService) EndCall(ctx context.Context, dollId, friendDollID, startTime string, duration int) (*CallEndResponse, error) {
	url := fmt.Sprintf("%s/call/end", m.baseURL)

	reqBody := CallEndRequest{
		DollId:       dollId,
		FriendDollID: friendDollID,
		StartTime:    startTime,
		Duration:     duration,
	}

	body, err := DoRequest(ctx, "POST", url, reqBody, nil, 10)
	if err != nil {
		logger.Error("结束通话记录失败", zap.Error(err),
			zap.String("url", url),
			zap.Any("reqBody", reqBody))
		return nil, fmt.Errorf("结束通话记录失败: %w", err)
	}

	result := CallEndResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Error("解析结束通话响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析结束通话响应失败: %w", err)
	}

	logger.Info("结束通话记录成功", zap.String("dollId", dollId),
		zap.String("friendDollID", friendDollID),
		zap.String("startTime", startTime),
		zap.Int("duration", duration))
	return &result, nil
}
func (m *MediaService) UploadWifiList(ctx context.Context, dollId string, ssids []string) error {
	url := fmt.Sprintf("%s/doll/upload-ssid-list", m.baseURL)
	reqBody := map[string]interface{}{
		"dollId": dollId,
		"ssids":  ssids,
	}
	body, err := DoRequest(ctx, "POST", url, reqBody, nil, 10)
	if err != nil {
		logger.Error("上传wifi列表Http请求失败", zap.Error(err), zap.String("url", url), zap.Any("reqBody", reqBody))
		return err
	}
	result := UploadWifiListResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Error("解析上传wifi列表响应失败", zap.Error(err), zap.String("body", string(body)))
		return fmt.Errorf("解析上传wifi列表响应失败: %w", err)
	}
	if result.Code != 0 {
		logger.Error("上传wifi列表失败", zap.String("url", url), zap.Any("reqBody", reqBody), zap.String("body", string(body)))
		return fmt.Errorf("上传wifi列表失败: %s", result.Message)
	}
	logger.Info("上传wifi列表成功", zap.String("url", url), zap.Any("reqBody", reqBody), zap.String("body", string(body)))
	return nil
}

func (m *MediaService) DownloadAudioToPCM(ctx context.Context, url string, dstFormat *audio.AudioPCMFormat) ([]byte, error) {
	// 生成缓存key，包含URL和音频格式参数
	cacheKey := m.cache.GenerateKey("audio_pcm", url,
		fmt.Sprintf("%d_%d_%d", dstFormat.SampleRate, dstFormat.Channels, dstFormat.BitDepth))

	// 先尝试从缓存获取
	if cachedData, exists := m.cache.Get(cacheKey); exists {
		logger.Info("音频PCM缓存命中", zap.String("url", url))
		return cachedData.([]byte), nil
	}

	// 缓存未命中，下载媒体文件
	mediaBytes, err := DownloadFile(ctx, url, map[string]string{}, 10, "")
	if err != nil {
		return nil, fmt.Errorf("请求媒体数据失败: %w", err)
	}

	// 转换为PCM
	pcm, err := audio.ProcessAudioToPCM(mediaBytes, *dstFormat)
	if err != nil {
		return nil, fmt.Errorf("转换PCM失败: %w", err)
	}

	// 存储到缓存，设置1小时过期时间
	m.cache.Set(cacheKey, pcm, 3600)
	logger.Info("音频PCM已缓存", zap.String("url", url), zap.Int("size", len(pcm)))

	return pcm, nil
}
