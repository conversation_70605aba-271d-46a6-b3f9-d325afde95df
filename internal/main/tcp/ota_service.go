package tcp

import (
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"aigc_server/pkg/utils"
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// OTA:额外的bizserver与aigcserver通信 redis
// 1. 通知确认更新 biz => aigcserver
//    redis channel:
//    key=doll:ota-update:begin:{{dollId}}
//    value={
//      "version": "1.0.0",
//      "url": "https://www.baidu.com",
//    }
// 2. 通知进度 aigcserver => biz
//    redis channel:
//    key=doll:ota-update:process:{{dollId}}
//    value={
//      "progress": 50,
//      "status":"processing", // processing, success, failed
//    }

// OtaUpdateRequest OTA更新请求结构
type OtaUpdateRequest struct {
	Version string `json:"version"`
	Url     string `json:"url"`
}

// OtaProcessUpdate OTA进度更新结构
type OtaProcessUpdate struct {
	Progress int    `json:"progress"`
	Status   string `json:"status"` // processing, success, failed
}

// OtaService OTA服务
type OtaService struct {
	ctx         context.Context
	cancel      context.CancelFunc
	dollId      string
	redisClient *redis.Client
	mutex       sync.RWMutex
	isRunning   bool
}

// NewOtaService 创建OTA服务实例
func NewOtaService(ctx context.Context, dollId string) *OtaService {
	serviceCtx, cancel := context.WithCancel(ctx)
	return &OtaService{
		ctx:         serviceCtx,
		cancel:      cancel,
		dollId:      dollId,
		redisClient: myredis.GetClient(),
		isRunning:   false,
	}
}

// Start 启动OTA服务
func (o *OtaService) Start() {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	if o.isRunning {
		logger.Warn("OTA服务已经在运行中", zap.String("dollId", o.dollId))
		return
	}

	o.isRunning = true
	logger.Info("启动OTA服务", zap.String("dollId", o.dollId))

	// 启动监听Redis频道
	go o.listenForOtaUpdateNotifications()
}

// Stop 停止OTA服务
func (o *OtaService) Stop() {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	if !o.isRunning {
		return
	}

	logger.Info("停止OTA服务", zap.String("dollId", o.dollId))
	o.isRunning = false
	o.cancel()
}

// listenForOtaUpdateNotifications 监听OTA更新通知
func (o *OtaService) listenForOtaUpdateNotifications() {
	defer utils.TraceRecover()
	// 构建订阅频道名称
	channel := fmt.Sprintf("doll:ota-update:begin:%s", o.dollId)

	logger.Info("开始订阅OTA更新通知频道", zap.String("dollId", o.dollId), zap.String("channel", channel))

	pubsub := o.redisClient.Subscribe(o.ctx, channel)
	defer pubsub.Unsubscribe(o.ctx, channel)
	defer pubsub.Close()

	for {
		select {
		case <-o.ctx.Done():
			logger.Info("OTA服务上下文已取消，停止监听", zap.String("dollId", o.dollId))
			return
		case msg := <-pubsub.Channel():
			if msg == nil {
				continue
			}
			logger.Info("接收到OTA更新通知", zap.String("dollId", o.dollId), zap.String("payload", msg.Payload))

			// 解析更新请求
			var updateReq OtaUpdateRequest
			err := json.Unmarshal([]byte(msg.Payload), &updateReq)
			if err != nil {
				logger.Error("解析OTA更新请求失败", zap.Error(err), zap.String("payload", msg.Payload))
				continue
			}

			// 处理OTA更新请求
			o.handleOtaUpdateRequest(&updateReq)
		}
	}
}

// handleOtaUpdateRequest 处理OTA更新请求
func (o *OtaService) handleOtaUpdateRequest(updateReq *OtaUpdateRequest) {
	logger.Info("处理OTA更新请求",
		zap.String("dollId", o.dollId),
		zap.String("version", updateReq.Version),
		zap.String("url", updateReq.Url))

	// 直接通过TCP发送OTA通知消息，不再使用IPC
	msg := CreateRequestDollMessage(MsgTypeOtaNotifyRequest)
	req := msg.(*DollOtaNotifyRequest)
	req.Version = updateReq.Version
	req.FirmwareUrl = updateReq.Url
	req.DollTypeValue = "99doll"

	data, err := json.Marshal(msg)
	if err != nil {
		logger.Error("序列化OTA通知消息失败", zap.Error(err), zap.String("dollId", o.dollId))
		o.PublishOtaProgress(0, "failed")
		return
	}

	err = TcpServerInstance.SendToClient(o.dollId, data)
	if err != nil {
		logger.Error("发送OTA通知失败", zap.Error(err), zap.String("dollId", o.dollId))
		// 发送失败状态
		o.PublishOtaProgress(0, "failed")
		return
	}

	logger.Info("成功发送OTA通知", zap.String("dollId", o.dollId))
	// 发送开始处理状态
	o.PublishOtaProgress(0, "processing")
}

// PublishOtaProgress 发布OTA进度更新
func (o *OtaService) PublishOtaProgress(progress int, status string) error {
	// 构建发布频道名称
	channel := fmt.Sprintf("doll:ota-update:process:%s", o.dollId)

	// 构建进度更新数据
	progressUpdate := OtaProcessUpdate{
		Progress: progress,
		Status:   status,
	}

	// 序列化数据
	data, err := json.Marshal(progressUpdate)
	if err != nil {
		logger.Error("序列化OTA进度更新数据失败", zap.Error(err))
		return err
	}

	// 发布到Redis频道
	err = o.redisClient.Publish(o.ctx, channel, string(data)).Err()
	if err != nil {
		logger.Error("发布OTA进度更新失败",
			zap.Error(err),
			zap.String("dollId", o.dollId),
			zap.String("channel", channel))
		return err
	}

	logger.Info("成功发布OTA进度更新",
		zap.String("dollId", o.dollId),
		zap.String("channel", channel),
		zap.Int("progress", progress),
		zap.String("status", status))

	return nil
}

// UpdateOtaProgress 更新OTA进度（供外部调用）
func (o *OtaService) UpdateOtaProgress(progress int, status string) {
	if !o.isRunning {
		logger.Warn("OTA服务未运行，无法发布进度更新", zap.String("dollId", o.dollId))
		return
	}

	err := o.PublishOtaProgress(progress, status)
	if err != nil {
		logger.Error("更新OTA进度失败", zap.Error(err), zap.String("dollId", o.dollId))
	}
}

// ProcessOtaProgress 处理OTA进度消息（从TCP消息处理）
func (o *OtaService) ProcessOtaProgress(progress int, status string) {
	logger.Info("处理OTA进度消息",
		zap.String("dollId", o.dollId),
		zap.Int("progress", progress),
		zap.String("status", status))

	// 更新进度到Redis
	o.UpdateOtaProgress(progress, status)
}

// GetChannelPrefix 获取频道前缀（用于测试或调试）
func (o *OtaService) GetChannelPrefix() string {
	return fmt.Sprintf("doll:ota-update:%s", o.dollId)
}

// IsRunning 检查服务是否正在运行
func (o *OtaService) IsRunning() bool {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	return o.isRunning
}
