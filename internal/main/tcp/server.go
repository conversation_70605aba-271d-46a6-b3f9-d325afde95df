package tcp

import (
	"context"
	"fmt"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

// TCPServer TCP服务器
type TCPServer struct {
	cfg               *config.Config
	server            net.Listener
	ipcManager        *ipc.IPCManager
	activeConnections int32
	connectionMap     map[string]*ConnectionHandler // dollId -> handler
	connectionMutex   sync.RWMutex
}

var TcpServerInstance *TCPServer
var instanceOnce sync.Once

// NewTCPServer 创建TCP服务器
func NewTCPServer(cfg *config.Config, ipcManager *ipc.IPCManager) *TCPServer {
	instanceOnce.Do(func() {
		TcpServerInstance = &TCPServer{
			cfg:           cfg,
			ipcManager:    ipcManager,
			connectionMap: make(map[string]*ConnectionHandler),
		}
	})
	return TcpServerInstance
}

// Start 启动服务器
func (s *TCPServer) Start(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.cfg.TCP.Host, s.cfg.TCP.Port)

	ln, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("启动TCP服务器失败: %w", err)
	}
	s.server = ln

	logger.Info("TCP服务器启动中",
		zap.String("addr", addr),
		zap.String("env", s.cfg.Server.Env),
		zap.Int("max_connections", s.cfg.TCP.MaxConnections),
	)

	// 启动监控协程
	go s.startMonitor(ctx)

	// 启动接受连接的协程
	go s.acceptConnections(ctx)

	return nil
}

// Stop 停止服务器
func (s *TCPServer) Stop() error {
	logger.Info("TCP服务器停止中")
	if s.server != nil {
		return s.server.Close()
	}
	return nil
}

// acceptConnections 接受连接
func (s *TCPServer) acceptConnections(ctx context.Context) {
	defer utils.TraceRecover()
	for {
		select {
		case <-ctx.Done():
			logger.Info("TCP服务器接受连接协程退出")
			return
		default:
		}

		conn, err := s.server.Accept()
		if err != nil {
			logger.Error("接受连接失败", zap.Error(err))
			continue
		}

		// 检查连接数限制
		if atomic.LoadInt32(&s.activeConnections) >= int32(s.cfg.TCP.MaxConnections) {
			logger.Warn("达到最大连接数限制，拒绝新连接",
				zap.Int32("active_connections", atomic.LoadInt32(&s.activeConnections)),
				zap.Int("max_connections", s.cfg.TCP.MaxConnections))
			conn.Close()
			continue
		}

		// 增加活跃连接数
		atomic.AddInt32(&s.activeConnections, 1)

		// 设置连接超时
		if s.cfg.TCP.ReadTimeout != "" {
			if timeout, err := time.ParseDuration(s.cfg.TCP.ReadTimeout); err == nil {
				conn.SetReadDeadline(time.Now().Add(timeout))
			}
		}
		if s.cfg.TCP.WriteTimeout != "" {
			if timeout, err := time.ParseDuration(s.cfg.TCP.WriteTimeout); err == nil {
				conn.SetWriteDeadline(time.Now().Add(timeout))
			}
		}

		// 启动连接处理协程
		go s.handleConnection(ctx, conn)
	}
}

// handleConnection 处理单个连接
func (s *TCPServer) handleConnection(ctx context.Context, conn net.Conn) {
	defer utils.TraceRecover()
	defer func() {
		defer utils.TraceRecover()
		atomic.AddInt32(&s.activeConnections, -1)
		if conn == nil {
			logger.Error("TCP连接为空,不再次关闭")
			return
		}
		conn.Close()
		logger.Info("TCP连接已关闭",
			zap.String("remote_addr", conn.RemoteAddr().String()),
			zap.Int32("remaining_connections", atomic.LoadInt32(&s.activeConnections)))
	}()

	// 创建连接处理器
	handler := NewConnectionHandler(ctx, s.cfg, s.ipcManager, s, conn)
	// 处理连接
	handler.Handle()

	// 从连接映射中移除
	if handler.dollId != "" {
		s.connectionMutex.Lock()
		delete(s.connectionMap, handler.dollId)
		s.connectionMutex.Unlock()
	}
	logger.Info("TCP连接结束,即将关闭", zap.String("doll_id", handler.dollId))
}

// startMonitor 启动监控协程
func (s *TCPServer) startMonitor(ctx context.Context) {
	defer utils.TraceRecover()
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Info("TCP服务器监控协程退出")
			return
		case <-ticker.C:
			active := atomic.LoadInt32(&s.activeConnections)
			logger.Info("TCP服务状态",
				zap.Int32("active_connections", active),
				zap.Int("max_connections", s.cfg.TCP.MaxConnections))
		}
	}
}

// GetConnectionHandler 获取指定娃娃ID的连接处理器
func (s *TCPServer) GetConnectionHandler(dollId string) *ConnectionHandler {
	s.connectionMutex.RLock()
	defer s.connectionMutex.RUnlock()
	return s.connectionMap[dollId]
}

// RegisterConnection 注册连接
func (s *TCPServer) RegisterConnection(dollId string, handler *ConnectionHandler) {
	s.connectionMutex.Lock()
	defer s.connectionMutex.Unlock()
	s.connectionMap[dollId] = handler
}
func (s *TCPServer) UnregisterConnection(dollId string) {
	s.connectionMutex.Lock()
	defer s.connectionMutex.Unlock()
	delete(s.connectionMap, dollId)
}

// SendToClient 向指定娃娃ID的客户端发送数据
func (s *TCPServer) SendToClient(dollId string, data []byte) error {
	handler := s.GetConnectionHandler(dollId)
	if handler == nil {
		return fmt.Errorf("未找到娃娃ID %s 的连接", dollId)
	}

	return handler.SendToClientByBytes(data)
}

// GetActiveConnections 获取活跃连接数
func (s *TCPServer) GetActiveConnections() int32 {
	return atomic.LoadInt32(&s.activeConnections)
}
