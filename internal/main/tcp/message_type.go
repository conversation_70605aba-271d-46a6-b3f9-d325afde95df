package tcp

import (
	"encoding/json"
)

type DollMessageType string

// 消息类型常量定义
const (
	MsgTypeCommonResponse         DollMessageType = "com_res"               // S -> C
	MsgTypeEnterRoomRequest       DollMessageType = "en_rm"                 // C -> S
	MsgTypeEnterRoomResponse      DollMessageType = "en_rm_res"             // S -> C
	MsgTypeHeartbeatRequest       DollMessageType = "htbt"                  // C -> S
	MsgTypeHeartbeatResponse      DollMessageType = "htbt_res"              // S -> C
	MsgTypeSetVolumeRequest       DollMessageType = "set_vol"               // S -> C
	MsgTypeSetVolumeResponse      DollMessageType = "set_vol_res"           // C -> S
	MsgTypeAddFriendRequest       DollMessageType = "add_friend_notify"     // S -> C
	MsgTypeAddFriendResponse      DollMessageType = "add_friend_notify_res" // C -> S
	MsgTypeVoiceInterruptRequest  DollMessageType = "voice_interrupt"       // C -> S
	MsgTypeVoiceInterruptResponse DollMessageType = "voice_interrupt_res"   // S -> C
	MsgTypeDiscoverFriendRequest  DollMessageType = "discover_friend"       // C -> S
	MsgTypeDiscoverFriendResponse DollMessageType = "discover_friend_res"   // S -> C
	MsgTypeWifiListUploadRequest  DollMessageType = "wifi_list_upload"      // C -> S
	MsgTypeWifiListUploadResponse DollMessageType = "wifi_list_upload_res"  // S -> C
	MsgTypeDelSavedWifiRequest    DollMessageType = "del_saved_wifi"        // C -> S
	MsgTypeDelSavedWifiResponse   DollMessageType = "del_saved_wifi_res"    // S -> C
	MsgTypeOtaNotifyRequest       DollMessageType = "ota_notify"            // C -> S
	MsgTypeOtaNotifyResponse      DollMessageType = "ota_notify_res"        // S -> C
	MsgTypeOtaProcessRequest      DollMessageType = "ota_process"           // C -> S
	MsgTypeOtaProcessResponse     DollMessageType = "ota_process_res"       // S -> C
	MsgTypeSensorAccRequest       DollMessageType = "sensor_acc"            // C -> S
	MsgTypeSensorAccResponse      DollMessageType = "sensor_acc_res"        // S -> C
)

type IDollMessage interface {
	GetType() DollMessageType
	// ToJson() ([]byte, error)
}

type DollMessage struct {
	Type DollMessageType `json:"type"`
}

func (m *DollMessage) GetType() DollMessageType {
	return m.Type
}

// func (m *DollMessage) ToJson() ([]byte, error) {
// 	return json.Marshal(m)
// }

type DollCommonResponseMessage struct {
	DollMessage
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// 消息类型定义
type DollEnterRoomRequest struct {
	DollMessage
	DollId      string `json:"dollId"`
	StartupWord string `json:"startupWord"`
}

type DollEnterRoomResponse struct {
	DollCommonResponseMessage
	Data struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

type DollHeartbeatRequest struct {
	DollMessage
	DollId   string `json:"dollId"`
	Battery  int    `json:"battery"`
	Volume   int    `json:"volume"`
	Charging bool   `json:"charging"`
	Version  string `json:"version"`
}
type DollHeartbeatResponse struct {
	DollCommonResponseMessage
	// Data struct {
	// 	Volume int `json:"volume"`
	// } `json:"data"`
}

type DollSetVolumeRequest struct {
	DollMessage
	Volume int `json:"volume"`
}
type DollSetVolumeResponse struct {
	DollCommonResponseMessage
	Data struct {
		Volume int `json:"volume"`
	} `json:"data"`
}

type DollAddFriendRequest struct {
	DollMessage
}

type DollAddFriendResponse struct {
	DollCommonResponseMessage
}

type DollDiscoverFriendRequest struct {
	DollMessage
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		FriendId string `json:"friendId"`
	} `json:"data"`
}
type DollDiscoverFriendResponse struct {
	DollCommonResponseMessage
}

type DollVoiceInterruptRequest struct {
	DollMessage
}

type DollVoiceInterruptResponse struct {
	DollCommonResponseMessage
}

type DollWifiListUploadRequest struct {
	DollMessage
	DollId string   `json:"dollId"`
	Ssids  []string `json:"ssids"`
}

type DollWifiListUploadResponse struct {
	DollCommonResponseMessage
}

type DollDelSavedWifiRequest struct {
	DollMessage
	Ssid string `json:"ssid"`
}

type DollDelSavedWifiResponse struct {
	DollCommonResponseMessage
	Data struct {
		DollId string `json:"dollId"`
		Ssid   string `json:"ssid"`
	} `json:"data"`
}

type DollOtaNotifyRequest struct {
	DollMessage
	Version       string `json:"version"`
	FirmwareUrl   string `json:"firmwareUrl"`
	DollTypeValue string `json:"dolltype"`
}

type DollOtaNotifyResponse struct {
	DollCommonResponseMessage
}

// 更新固件，返回进度。
type DollOtaStatusType string

const (
	DollOtaStatusTypeProcessing DollOtaStatusType = "processing"
	DollOtaStatusTypeSuccess    DollOtaStatusType = "success"
	DollOtaStatusTypeFailed     DollOtaStatusType = "failed"
)

type DollOtaProcessRequest struct {
	DollMessage
	Progress int               `json:"progress"`
	Status   DollOtaStatusType `json:"status"`
}

type DollOtaProcessResponse struct {
	DollCommonResponseMessage
}

// 加速度传感器数据结构
type AccelerometerData struct {
	Ts int64 `json:"ts"` // 毫秒时间戳
	X  int   `json:"x"`  // X轴加速度
	Y  int   `json:"y"`  // Y轴加速度
	Z  int   `json:"z"`  // Z轴加速度
}

type DollSensorAccRequest struct {
	DollMessage
	Acc []AccelerometerData `json:"acc"` // 加速度数据数组
}

type DollSensorAccResponse struct {
	DollCommonResponseMessage
}

func ParseDollMessage(data []byte) (interface{}, error) {
	// 首先解析基础消息获取type字段
	var baseMsg DollMessage
	if err := json.Unmarshal(data, &baseMsg); err != nil {
		return nil, err
	}

	// 根据type字段解析为具体的消息类型
	switch baseMsg.Type {
	case MsgTypeEnterRoomRequest:
		var msg DollEnterRoomRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeEnterRoomResponse:
		var msg DollEnterRoomResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeHeartbeatRequest:
		var msg DollHeartbeatRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeHeartbeatResponse:
		var msg DollHeartbeatResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeSetVolumeRequest:
		var msg DollSetVolumeRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeSetVolumeResponse:
		var msg DollSetVolumeResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeAddFriendRequest:
		var msg DollAddFriendRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeAddFriendResponse:
		var msg DollAddFriendResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeVoiceInterruptRequest:
		var msg DollVoiceInterruptRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeVoiceInterruptResponse:
		var msg DollVoiceInterruptResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeDiscoverFriendRequest:
		var msg DollDiscoverFriendRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeDiscoverFriendResponse:
		var msg DollDiscoverFriendResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeWifiListUploadRequest:
		var msg DollWifiListUploadRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeWifiListUploadResponse:
		var msg DollWifiListUploadResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeDelSavedWifiRequest:
		var msg DollDelSavedWifiRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeDelSavedWifiResponse:
		var msg DollDelSavedWifiResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeOtaNotifyRequest:
		var msg DollOtaNotifyRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeOtaNotifyResponse:
		var msg DollOtaNotifyResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeOtaProcessRequest:
		var msg DollOtaProcessRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeOtaProcessResponse:
		var msg DollOtaProcessResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeSensorAccRequest:
		var msg DollSensorAccRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeSensorAccResponse:
		var msg DollSensorAccResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	default:
		// 未知类型，返回基础消息
		return baseMsg, nil
	}
}

func CreateRequestDollMessage(msgType DollMessageType) IDollMessage {
	switch msgType {
	case MsgTypeEnterRoomRequest:
		return &DollEnterRoomRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeHeartbeatRequest:
		return &DollHeartbeatRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeSetVolumeRequest:
		return &DollSetVolumeRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeAddFriendRequest:
		return &DollAddFriendRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeVoiceInterruptRequest:
		return &DollVoiceInterruptRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeDiscoverFriendRequest:
		return &DollDiscoverFriendRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeWifiListUploadRequest:
		return &DollWifiListUploadRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeDelSavedWifiRequest:
		return &DollDelSavedWifiRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeOtaNotifyRequest:
		return &DollOtaNotifyRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeOtaProcessRequest:
		return &DollOtaProcessRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeSensorAccRequest:
		return &DollSensorAccRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	default:
		return nil
	}
}

func CreateResponseDollMessage(msgType DollMessageType, code int, message string) IDollMessage {
	switch msgType {
	case MsgTypeCommonResponse:
		return &DollCommonResponseMessage{
			DollMessage: DollMessage{
				Type: msgType,
			},
			Code:    code,
			Message: message,
		}
	case MsgTypeEnterRoomResponse:
		return &DollEnterRoomResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeHeartbeatResponse:
		return &DollHeartbeatResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeSetVolumeResponse:
		return &DollSetVolumeResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeAddFriendResponse:
		return &DollAddFriendResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeVoiceInterruptResponse:
		return &DollVoiceInterruptResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeDiscoverFriendResponse:
		return &DollDiscoverFriendResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeWifiListUploadResponse:
		return &DollWifiListUploadResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeDelSavedWifiResponse:
		return &DollDelSavedWifiResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeOtaNotifyResponse:
		return &DollOtaNotifyResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeOtaProcessResponse:
		return &DollOtaProcessResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeSensorAccResponse:
		return &DollSensorAccResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	default:
		return nil
	}
}
