package tcp

import (
	"context"
	"encoding/json"
	"errors"
	"net"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/process"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

// ConnectionHandler TCP连接处理器
type ConnectionHandler struct {
	ctx        context.Context
	cancel     context.CancelFunc
	cfg        *config.Config
	ipcManager *ipc.IPCManager
	conn       net.Conn
	dollId     string      // 当前连接的娃娃ID
	server     *TCPServer  // 服务器引用
	otaService *OtaService // OTA服务实例

	lastEnterRoomTime time.Time

	mutex sync.Mutex
}

// NewConnectionHandler 创建连接处理器
func NewConnectionHandler(parentCtx context.Context, cfg *config.Config, ipcManager *ipc.IPCManager, server *TCPServer, conn net.Conn) *ConnectionHandler {
	ctx, cancel := context.WithCancel(parentCtx)
	return &ConnectionHandler{
		ctx:        ctx,
		cancel:     cancel,
		cfg:        cfg,
		ipcManager: ipcManager,
		server:     server,
		conn:       conn,
	}
}

func (h *ConnectionHandler) Close() {
	if h.server != nil {
		h.server.UnregisterConnection(h.dollId)
	}
	logger.Info("关闭TCP连接, 取消上下文", zap.String("doll_id", h.dollId))
	h.cancel()
	h.dollId = ""
	h.server = nil
}

// startOtaServiceIfNeeded 如果需要则启动OTA服务
func (h *ConnectionHandler) startOtaServiceIfNeeded(dollId string) {
	if dollId == "" {
		return
	}

	if h.otaService != nil {
		// OTA服务已经在运行
		return
	}

	// 创建并启动OTA服务
	h.otaService = NewOtaService(h.ctx, dollId)
	h.otaService.Start()
	logger.Info("启动OTA服务", zap.String("doll_id", dollId))
}

// Handle 处理连接
func (h *ConnectionHandler) Handle() {
	defer func() {
		// 停止OTA服务
		if h.otaService != nil {
			h.otaService.Stop()
			h.otaService = nil
		}
	}()

	remoteAddr := h.conn.RemoteAddr().String()
	logger.Info("新的TCP连接建立", zap.String("remote_addr", remoteAddr))

	packetReader := NewPacketReader()

	retryCount := 0
	for {
		select {
		case <-h.ctx.Done():
			logger.Info("连接处理被取消", zap.String("remote_addr", remoteAddr), zap.Any("doll_id", h.dollId))
			return
		default:
		}

		packet, err := packetReader.ReadPacket(h.conn)
		if err != nil {
			if err.Error() == "EOF" {
				logger.Info("TCP客户端主动断开连接", zap.String("remote_addr", remoteAddr), zap.Any("doll_id", h.dollId))
				return
			}
			if nerr, ok := err.(net.Error); ok && nerr.Timeout() {
				logger.Warn("TCP客户端连接超时", zap.String("remote_addr", remoteAddr), zap.Any("doll_id", h.dollId))
			} else {
				logger.Error("TCP读取数据错误", zap.String("remote_addr", remoteAddr), zap.Any("doll_id", h.dollId), zap.Error(err))
			}
			retryCount++
			if retryCount > 60 {
				logger.Error("TCP连接错误, 关闭连接", zap.String("remote_addr", remoteAddr), zap.Any("doll_id", h.dollId), zap.Error(err))
				return
			}
			time.Sleep(1000 * time.Millisecond)
			continue
		}
		retryCount = 0

		// 解析消息
		msg, err := ParseDollMessage(packet)
		if err != nil {
			logger.Error("解析TCP消息失败", zap.Error(err))
			msg := CreateResponseDollMessage(MsgTypeCommonResponse, 1, "invalid request")
			h.SendMessageToClient(msg)
			time.Sleep(100 * time.Millisecond)
			continue
		}

		logger.Info("收到TCP消息", zap.Any("message", msg), zap.String("doll_id", h.dollId), zap.String("remote_addr", remoteAddr))

		// 处理不同类型的消息
		switch req := msg.(type) {
		case DollEnterRoomRequest:
			h.handleDollEnterRoom(h.ctx, &req)
		case DollWifiListUploadRequest:
			h.handleDollWifiListUpload(h.ctx, &req)
		case DollDelSavedWifiResponse:
			h.handleDollDelSavedWifiResponse(&req)
		case DollOtaNotifyResponse:
			h.handleDollOtaNotifyResponse(&req)
		case DollOtaProcessRequest:
			h.handleDollOtaProcessRequest(&req)
		case DollHeartbeatRequest:
			// logger.Info("处理娃娃心跳请求", zap.Any("message", req), zap.String("doll_id", h.dollId))
			h.handleDollHeartbeatRequest(h.ctx, &req)
		default:
			err := h.handleDollNormalMessage(h.ctx, &req)
			if err != nil {
				logger.Error("处理娃娃消息失败", zap.Error(err))
				msg := CreateResponseDollMessage(MsgTypeCommonResponse, 500, "internal server error")
				h.SendMessageToClient(msg)
			}
		}
	}
}

func (h *ConnectionHandler) handleDollHeartbeatRequest(ctx context.Context, cur *DollHeartbeatRequest) error {
	if cur.DollId == "" {
		logger.Error("娃娃ID为空, 心跳错误")
		msg := CreateResponseDollMessage(MsgTypeHeartbeatResponse, 1, "doll_id is empty")
		h.SendMessageToClient(msg)
		return nil
	}
	h.dollId = cur.DollId
	if h.server.GetConnectionHandler(cur.DollId) != h {
		h.server.UnregisterConnection(cur.DollId)
		h.server.RegisterConnection(cur.DollId, h)
	}
	// 在获得 dollId 时启动 OTA 服务
	h.startOtaServiceIfNeeded(cur.DollId)

	dollStatus := model.NewDollStatus(ctx, cur.DollId)
	dollStatus.Volume = cur.Volume
	dollStatus.Battery = cur.Battery
	dollStatus.Charging = cur.Charging
	dollStatus.VersionFirmware = cur.Version
	err := dollStatus.Save()
	logger.Info("娃娃状态同步客户端成功", zap.Any("data", cur))
	if err != nil {
		logger.Error("娃娃消息Save失败", zap.Any("data", cur), zap.Error(err))
		return err
	}
	h.handleDollNormalMessage(h.ctx, &cur)
	msg := CreateResponseDollMessage(MsgTypeHeartbeatResponse, 0, "success")
	h.SendMessageToClient(msg)
	return nil
}

// handleDollEnterRoom 处理娃娃进入房间请求
func (h *ConnectionHandler) handleDollEnterRoom(_ context.Context, req *DollEnterRoomRequest) {
	logger.Info("处理娃娃进入房间请求", zap.String("doll_id", req.DollId))
	if req.DollId == "" {
		logger.Error("娃娃ID为空, 拒绝连接")
		msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "doll_id is empty")
		h.SendMessageToClient(msg)
		return
	}

	// 记录当前连接的娃娃ID和进程key
	h.dollId = req.DollId

	// 在获得 dollId 时启动 OTA 服务
	h.startOtaServiceIfNeeded(req.DollId)

	// 注册连接到服务器
	exists := h.server.GetConnectionHandler(req.DollId)
	if exists != nil && exists == h {
		if time.Since(h.lastEnterRoomTime) < 3*time.Second {
			logger.Warn("同一Connection 重复EnterRoom, 忽略", zap.String("doll_id", req.DollId), zap.Any("conn", h.conn.RemoteAddr()))
			msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "重复连接, 请稍后再试")
			h.SendMessageToClient(msg)
			return
		}
		logger.Warn("同一Connection 重复EnterRoom, 重用连接", zap.String("doll_id", req.DollId), zap.Any("conn", h.conn.RemoteAddr()))
	} else {
		h.lastEnterRoomTime = time.Now()
		if exists != nil {
			logger.Info("关闭旧TCP连接", zap.String("doll_id", req.DollId), zap.Any("conn", h.conn.RemoteAddr()))
			exists.Close()
		}
		logger.Info("注册TCP连接到服务器", zap.String("doll_id", req.DollId), zap.Any("conn", h.conn.RemoteAddr()))
		h.server.RegisterConnection(req.DollId, h)
	}
	// 生成房间ID和Token（简化版本，不依赖外部服务）
	roomID := utils.GenerateRoomId(req.DollId)
	roomToken, expireTime, err := utils.GenerateRoomToken(h.cfg.Rtc.AppID, h.cfg.Rtc.AppKey, roomID, req.DollId)
	if err != nil {
		logger.Error("生成房间Token失败", zap.Error(err))
		msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "生成房间信息失败")
		h.SendMessageToClient(msg)
		return
	}

	logger.Info("生成房间信息",
		zap.String("doll_id", req.DollId),
		zap.String("room_id", roomID),
		zap.String("room_token", roomToken),
		zap.String("expire_time", expireTime),
	)

	// 启动子进程
	if err := process.DollProcessManagerInstance.StartDollProcess(req.DollId, roomID, types.StartupWordType(req.StartupWord)); err != nil {
		logger.Error("启动子进程失败", zap.Error(err))
		msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "启动子进程失败")
		h.SendMessageToClient(msg)
		return
	}

	// 构造响应
	msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 0, "success")
	resp := msg.(*DollEnterRoomResponse)
	resp.Data.DollId = req.DollId
	resp.Data.RoomId = roomID
	resp.Data.RoomToken = roomToken

	if err := h.SendMessageToClient(resp); err != nil {
		logger.Error("发送响应失败", zap.Error(err))
	}
}

// handleDollSettings 处理娃娃设置请求
func (h *ConnectionHandler) handleDollNormalMessage(ctx context.Context, req interface{}) error {
	// 通过IPC发送设置请求到worker进程
	if h.ipcManager != nil {
		if h.dollId == "" {
			return errors.New("娃娃ID为空, 无法通过IPC转发TCP请求")
		}
		if err := h.ipcManager.SendMessage(ctx, ipc.GetWorkerProcessID(h.dollId), ipc.MessageTypeDollMessage, req); err != nil {
			return errors.New("通过IPC转发TCP请求失败")
		}
	} else {
		return errors.New("IPC管理器未初始化")
	}
	return nil
}

func (h *ConnectionHandler) SendToClientByBytes(data []byte) error {
	go func() {
		defer utils.TraceRecover()
		bytes := Pack(data)
		h.mutex.Lock()
		defer h.mutex.Unlock()
		// logger.Info("发送数据到客户端", zap.Any("data", msg))
		_, err := h.conn.Write(bytes)
		if err != nil {
			logger.Error("发送TCP数据失败", zap.Error(err))
		} else {
			logger.Info("成功发送数据到TCP客户端", zap.String("data", string(data)), zap.String("doll_id", h.dollId), zap.String("remote_addr", h.conn.RemoteAddr().String()))
		}
	}()
	return nil
}

// SendMessageToClient 向客户端发送数据（供IPC回调使用）
func (h *ConnectionHandler) SendMessageToClient(msg IDollMessage) error {
	bytes, err := json.Marshal(msg)
	if err != nil {
		logger.Error("TCP序列化数据失败", zap.Error(err))
		return err
	}
	return h.SendToClientByBytes(bytes)
}

func (h *ConnectionHandler) handleDollWifiListUpload(ctx context.Context, req *DollWifiListUploadRequest) {
	logger.Info("处理娃娃wifi列表上传请求", zap.Any("req", req))

	if req.DollId == "" {
		msg := CreateResponseDollMessage(MsgTypeWifiListUploadResponse, 1, "doll_id is empty")
		h.SendMessageToClient(msg)
		return
	}

	err := service.GetMediaServiceInstance().UploadWifiList(ctx, req.DollId, req.Ssids)
	if err != nil {
		msg := CreateResponseDollMessage(MsgTypeWifiListUploadResponse, 1, err.Error())
		h.SendMessageToClient(msg)
		return
	}
	msg := CreateResponseDollMessage(MsgTypeWifiListUploadResponse, 0, "success")
	h.SendMessageToClient(msg)
}

func (h *ConnectionHandler) handleDollDelSavedWifiResponse(req *DollDelSavedWifiResponse) {
	logger.Info("处理娃娃删除保存的wifi请求", zap.Any("req", req))
	service.GetEventEmitter().SyncEmit(types.EventType_Del_Saved_Wifi_Response, req)
}

// handleDollOtaNotifyResponse 处理娃娃OTA通知响应
func (h *ConnectionHandler) handleDollOtaNotifyResponse(req *DollOtaNotifyResponse) {
	logger.Info("处理娃娃OTA通知响应", zap.Any("req", req))
	// OTA通知响应的处理逻辑，目前只记录日志
	if req.Code == 0 {
		logger.Info("娃娃确认接收OTA通知", zap.String("doll_id", h.dollId))
	} else {
		logger.Warn("娃娃拒绝OTA通知", zap.String("doll_id", h.dollId), zap.String("message", req.Message))
	}
}

// handleDollOtaProcessRequest 处理娃娃OTA进度请求
func (h *ConnectionHandler) handleDollOtaProcessRequest(req *DollOtaProcessRequest) {
	logger.Info("处理娃娃OTA进度请求", zap.Any("req", req))

	// 通过OTA服务处理进度更新
	if h.otaService != nil {
		h.otaService.ProcessOtaProgress(req.Progress, string(req.Status))
	} else {
		logger.Warn("OTA服务未初始化，无法处理进度更新", zap.String("doll_id", h.dollId))
	}

	// 发送响应
	msg := CreateResponseDollMessage(MsgTypeOtaProcessResponse, 0, "success")
	h.SendMessageToClient(msg)
}
