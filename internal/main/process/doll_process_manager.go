package process

import (
	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"aigc_server/pkg/utils"
	"context"
	"fmt"
	"os"
	"os/exec"
	"sync"
	"time"

	"go.uber.org/zap"
)

type DollProcessItem struct {
	DollId     string      `json:"doll_id"`
	RoomID     string      `json:"room_id"`
	ProcessKey string      `json:"process_key"`
	Process    *os.Process `json:"-"`
	StartAt    string      `json:"start_at"`
}

type DollProcessManager struct {
	cfg        *config.Config
	ctx        context.Context
	processMap map[string]*DollProcessItem
	childMutex *sync.Mutex
}

var DollProcessManagerInstance *DollProcessManager
var dollProcessManagerOnce sync.Once

func NewDollProcessManager(ctx context.Context, cfg *config.Config) *DollProcessManager {
	dollProcessManagerOnce.Do(func() {
		DollProcessManagerInstance = &DollProcessManager{
			ctx:        ctx,
			cfg:        cfg,
			processMap: make(map[string]*DollProcessItem),
			childMutex: &sync.Mutex{},
		}
		DollProcessManagerInstance.SaveProcessMap()
	})
	return DollProcessManagerInstance
}
func (m *DollProcessManager) GetDollProcessMap() map[string]*DollProcessItem {
	return m.processMap
}
func (m *DollProcessManager) GetDollProcess(processKey string) *DollProcessItem {
	return m.processMap[processKey]
}
func (m *DollProcessManager) GetDollProcessByDollId(dollId string) *DollProcessItem {
	return m.processMap[m.buildProcessKey(dollId)]
}
func (m *DollProcessManager) GetDollProcessByRoomId(roomID string) *DollProcessItem {
	for _, process := range m.processMap {
		if process.RoomID == roomID {
			return process
		}
	}
	return nil
}

func (m *DollProcessManager) SaveProcessMap() error {
	key := fmt.Sprintf("aigc:main:process_map:%s", ipc.GetIPCMainProcessID())
	err := myredis.GetClient().Set(m.ctx, key, utils.ToJsonIgnoreError(m.processMap), 10*time.Minute).Err()
	if err != nil {
		logger.Error("保存进程映射到Redis失败", zap.Error(err))
	}
	return err
}

func (m *DollProcessManager) CloseDollProcess(processKey string) error {
	m.childMutex.Lock()
	defer m.childMutex.Unlock()

	if item := m.GetDollProcess(processKey); item != nil {
		groupChatTeamId, _ := service.GroupChatServiceInstance.FindTeam(m.ctx, item.DollId)
		if groupChatTeamId != "" {
			service.GroupChatServiceInstance.RemoveTeam(m.ctx, groupChatTeamId)
			logger.Info("关闭子进程,删除群组", zap.String("groupChatTeamId", groupChatTeamId))
		}
		logger.Info("关闭子进程", zap.String("processKey", processKey),
			zap.Int("pid", item.Process.Pid),
			zap.String("uid", item.DollId),
			zap.String("roomID", item.RoomID),
		)
		err := utils.CloseProcess(item.Process.Pid, 5*time.Second)
		if err != nil {
			logger.Error("关闭子进程Error", zap.Error(err))
		}
		delete(m.processMap, processKey)
		m.SaveProcessMap()
	} else {
		logger.Warn("CloseDollProcess Manager中未找到进程Key", zap.String("processKey", processKey))
	}
	return nil
}

// startDollProcess 启动doll服务子进程的回调函数
func (m *DollProcessManager) StartDollProcess(uid, roomID string, startupWord types.StartupWordType) error {
	processKey := m.buildProcessKey(uid)

	if item := m.GetDollProcess(processKey); item != nil {
		logger.Info("doll服务子进程已存在,关闭旧进程", zap.String("processKey", processKey))
		m.CloseDollProcess(processKey)
	}

	dollPath, err := utils.GetDollExecutablePath()
	if err != nil {
		logger.Error("获取doll可执行文件路径失败", zap.Error(err))
		return err
	}

	cmd := exec.Command(
		dollPath,
		"--env", m.cfg.Server.Env,
		"--uid", uid,
		"--room_id", roomID,
		"--startup_word", string(startupWord),
	)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	logger.Info("正在启动doll服务子进程...",
		zap.String("path", dollPath),
		zap.String("processKey", processKey),
		zap.String("uid", uid),
		zap.String("room_id", roomID),
	)
	if err := cmd.Start(); err != nil {
		logger.Error("启动doll服务子进程失败", zap.Error(err))
		return err
	}

	m.childMutex.Lock()
	m.processMap[processKey] = &DollProcessItem{
		DollId:     uid,
		RoomID:     roomID,
		ProcessKey: processKey,
		Process:    cmd.Process,
		StartAt:    time.Now().Format(time.RFC3339),
	}
	m.SaveProcessMap()
	m.childMutex.Unlock()

	logger.Info("doll服务子进程已启动",
		zap.Int("pid", cmd.Process.Pid),
		zap.String("room_id", roomID),
		zap.String("processKey", processKey),
	)

	go func() {
		defer utils.TraceRecover()
		if err := cmd.Wait(); err != nil {
			logger.Error("doll服务子进程异常退出",
				zap.Error(err),
				zap.String("processKey", processKey),
			)
		}
		m.CloseDollProcess(processKey)
	}()

	return nil
}

// closeAllChildProcesses 关闭所有子进程
func (m *DollProcessManager) CloseAllChildProcesses() {
	logger.Info("正在关闭所有子进程...")

	m.childMutex.Lock()
	processes := make(map[string]*os.Process)
	for id, process := range m.processMap {
		processes[id] = process.Process
	}
	m.processMap = make(map[string]*DollProcessItem)
	m.SaveProcessMap()
	m.childMutex.Unlock()

	var wg sync.WaitGroup
	for id, process := range processes {
		wg.Add(1)
		go func(id string, process *os.Process) {
			defer wg.Done()

			logger.Info("正在关闭子进程",
				zap.String("id", id),
				zap.Int("pid", process.Pid),
			)

			if err := utils.CloseProcess(process.Pid, 5*time.Second); err != nil {
				logger.Error("关闭子进程失败",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
					zap.Error(err),
				)
			} else {
				logger.Info("子进程已关闭",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
				)
			}

			// m.childMutex.Lock()
			// delete(m.processMap, id)
			// m.childMutex.Unlock()
		}(id, process)
	}

	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("所有子进程已关闭")
	case <-time.After(10 * time.Second):
		logger.Error("部分子进程可能未正常关闭")
	}
}

// buildProcessKey generates a process ID from user ID
func (m *DollProcessManager) buildProcessKey(uid string) string {
	return fmt.Sprintf("rtc_%s", uid)
}
