package ipc

import (
	"context"

	"go.uber.org/zap"

	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
)

// TCPServerInterface TCP服务器接口
type TCPServerInterface interface {
	SendToClient(dollId string, data []byte) error
}

// MainMessageHandler 主进程消息处理器
type MainMessageHandler struct {
	tcpServer  TCPServerInterface
	ipcManager *ipc.IPCManager
}

// NewMainMessageHandler 创建主进程消息处理器
func NewMainMessageHandler(tcpServer TCPServerInterface, ipcManager *ipc.IPCManager) *MainMessageHandler {
	return &MainMessageHandler{
		tcpServer:  tcpServer,
		ipcManager: ipcManager,
	}
}

// HandleMessage 处理消息
func (h *MainMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("主进程收到消息",
		zap.Any("msg", msg),
	)

	// 根据消息类型处理业务逻辑
	switch msg.Type {
	case ipc.MessageTypeDollMessage:
		return h.handleDollMessage(msg)
	case ipc.MessageTypeStatus:
		return h.handleStatus(ctx, msg)
	case ipc.MessageTypeEvent:
		return h.handleEvent(ctx, msg)
	case ipc.MessageTypeError:
		return h.handleError(ctx, msg)
	case ipc.MessageTypeLog:
		return h.handleLog(ctx, msg)
	default:
		logger.Warn("未知的消息类型", zap.Any("type", msg.Type))
	}

	return nil
}

// handleDollMessage 处理娃娃消息
func (h *MainMessageHandler) handleDollMessage(msg *ipc.Message) error {
	logger.Info("MainIPC收到,处理娃娃消息,将转发tcp客户端",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	workerProcessID := msg.From
	dollId := ipc.GetDollIdFromProcessID(workerProcessID)
	if dollId == "" {
		logger.Error("娃娃消息缺少doll_id")
		return nil
	}

	if err := h.tcpServer.SendToClient(dollId, msg.Data); err != nil {
		logger.Error("发送数据到客户端失败", zap.Error(err))
	}

	return nil
}

// handleStatus 处理状态消息
func (h *MainMessageHandler) handleStatus(_ context.Context, msg *ipc.Message) error {
	logger.Info("处理状态消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加状态处理逻辑
	return nil
}

// handleEvent 处理事件消息
func (h *MainMessageHandler) handleEvent(_ context.Context, msg *ipc.Message) error {
	logger.Info("处理事件消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加事件处理逻辑
	return nil
}

// handleError 处理错误消息
func (h *MainMessageHandler) handleError(_ context.Context, msg *ipc.Message) error {
	logger.Error("收到错误消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加错误处理逻辑
	return nil
}

// handleLog 处理日志消息
func (h *MainMessageHandler) handleLog(_ context.Context, msg *ipc.Message) error {
	logger.Info("收到日志消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加日志处理逻辑
	return nil
}
