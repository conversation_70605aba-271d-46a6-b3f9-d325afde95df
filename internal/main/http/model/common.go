package model

// CommonResponse 通用响应结构
type CommonResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// SuccessResponse 成功响应
func SuccessResponse() CommonResponse {
	return CommonResponse{
		Code:    0,
		Message: "success",
	}
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) CommonResponse {
	return CommonResponse{
		Code:    code,
		Message: message,
	}
}
