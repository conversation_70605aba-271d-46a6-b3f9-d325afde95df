package model

// ###
// // 请求删除Doll保存的wifi
// POST {{AIGCServerBaseUrl}}/aigc/doll/delete-ssid
// Content-Type: application/json

// {
//   "dollId": "{{DollID}}",
//   "ssid": "wifi1"
// }

// {
//   "code": 0,
//   "message": "success",
//   "data": {}
// }

// DollDeleteSSIDRequest 删除Doll保存的wifi请求
type DollDeleteSSIDRequest struct {
	DollId string `json:"dollId" binding:"required"`
	Ssid   string `json:"ssid" binding:"required"`
}

// // DollDeleteSSIDResponse 删除Doll保存的wifi响应
// type DollDeleteSSIDResponse struct {
// 	CommonResponse
// }
