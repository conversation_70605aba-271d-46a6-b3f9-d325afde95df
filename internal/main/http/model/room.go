package model

// RoomEnterRequest 房间进入请求
type RoomEnterRequest struct {
	DollId string `json:"dollId" binding:"required"`
	Mode   string `json:"mode"`
}

// RoomEnterData 房间进入响应数据
type RoomEnterData struct {
	UID        string `json:"uid"`
	RoomID     string `json:"roomId"`
	RoomToken  string `json:"roomToken"`
	ExpireTime string `json:"expireTime"`
}

// RoomEnterResponse 房间进入响应
type RoomEnterResponse struct {
	CommonResponse
	Data RoomEnterData `json:"data"`
}
