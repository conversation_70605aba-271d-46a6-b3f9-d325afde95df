package handler

import (
	"aigc_server/internal/main/http/model"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DelSSIDHandler struct {
	ctx context.Context
}

func NewDelSSIDHandler(ctx context.Context) *DelSSIDHandler {
	return &DelSSIDHandler{
		ctx: ctx,
	}
}

func (h *DelSSIDHandler) DelSSID(c *gin.Context) {
	var req model.DollDeleteSSIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("请求参数错误", zap.Error(err))
		c.JSON(http.StatusOK, model.ErrorResponse(400, "请求参数错误"))
		return
	}

	msg := tcp.CreateRequestDollMessage(tcp.MsgTypeDelSavedWifiRequest)
	msg.(*tcp.DollDelSavedWifiRequest).Ssid = req.Ssid

	tcp.TcpServerInstance.SendToClient(req.DollId, []byte(utils.ToJsonIgnoreError(msg)))

	var once sync.Once // 确保只响应一次
	ctx, cancel := context.WithTimeout(h.ctx, 10*time.Second)
	defer cancel()

	cancelEvent := service.GetEventEmitter().On(types.EventType_Del_Saved_Wifi_Response, func(event types.EventType, data interface{}) {
		msg := data.(*tcp.DollDelSavedWifiResponse)
		if msg == nil {
			logger.Error("删除保存的wifi响应事件, 数据为空")
			return
		}
		if msg.Data.DollId != req.DollId {
			logger.Error("删除保存的wifi响应事件, dollId不匹配", zap.String("doll_id", req.DollId), zap.String("data_doll_id", msg.Data.DollId))
			return
		}
		if msg.Data.Ssid != req.Ssid {
			logger.Error("删除保存的wifi响应事件, ssid不匹配", zap.String("ssid", req.Ssid), zap.String("data_ssid", msg.Data.Ssid))
			return
		}

		once.Do(func() {
			defer cancel()
			if msg.Code != 0 {
				logger.Error("删除保存的wifi响应事件, doll返回错误", zap.String("doll_id", req.DollId))
				c.JSON(http.StatusOK, model.ErrorResponse(msg.Code, msg.Message))
				return
			}
			logger.Info("Http处理娃娃删除保存的wifi响应事件", zap.String("doll_id", req.DollId), zap.Any("data", data))
			c.JSON(http.StatusOK, model.SuccessResponse())
		})
	}, h)

	defer func() {
		service.GetEventEmitter().Off(types.EventType_Del_Saved_Wifi_Response, cancelEvent)
	}()

	<-ctx.Done()

	// 超时处理
	once.Do(func() {
		c.JSON(http.StatusOK, model.ErrorResponse(504, "timeout"))
	})
}
