package handler

import (
	"net/http"
	"time"

	"aigc_server/internal/main/http/model"

	"github.com/gin-gonic/gin"
)

// HealthHandler 健康检查处理器
type HealthHandler struct{}

// NewHealthHandler 创建健康检查处理器
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// Health 健康检查接口
func (h *HealthHandler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"message": "server is running",
	})
}

// HealthDetail 详细健康检查接口示例
func (h *HealthHandler) HealthDetail(c *gin.Context) {
	response := struct {
		model.CommonResponse
		Data struct {
			Status    string `json:"status"`
			Timestamp int64  `json:"timestamp"`
			Version   string `json:"version"`
		} `json:"data"`
	}{
		CommonResponse: model.SuccessResponse(),
	}

	response.Data.Status = "healthy"
	response.Data.Timestamp = time.Now().Unix()
	response.Data.Version = "1.0.0"

	c.<PERSON><PERSON>(http.StatusOK, response)
}
