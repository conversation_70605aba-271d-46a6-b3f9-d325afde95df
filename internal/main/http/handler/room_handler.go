package handler

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/main/http/model"
	"aigc_server/internal/main/process"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RoomHandler 房间处理器
type RoomHandler struct {
	ctx context.Context
	cfg *config.Config
}

// NewRoomHandler 创建房间处理器
func NewRoomHandler(ctx context.Context, cfg *config.Config) *RoomHandler {
	return &RoomHandler{
		ctx: ctx,
		cfg: cfg,
	}
}

// RoomEnter 处理房间进入请求
func (h *RoomHandler) RoomEnter(c *gin.Context) {
	defer utils.TraceRecover()

	var req model.RoomEnterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("解析JSON请求失败", zap.Error(err))
		c.<PERSON>(http.StatusOK, model.RoomEnterResponse{
			CommonResponse: model.ErrorResponse(400, "解析JSON请求失败"),
		})
		return
	}

	logger.Info("HTTP 收到进入房间请求", zap.Any("request", req))

	// 验证dollId参数
	if req.DollId == "" {
		c.JSON(http.StatusOK, model.RoomEnterResponse{
			CommonResponse: model.ErrorResponse(400, "dollId为空"),
		})
		return
	}

	rtcUID := req.DollId

	// 处理语音聊天模式
	if req.Mode == "parent_voice_chat" {
		rtcUID = fmt.Sprintf("%s.%s", req.Mode, req.DollId)
		processItem := process.DollProcessManagerInstance.GetDollProcessByDollId(req.DollId)
		if processItem == nil {
			logger.Error("语音聊天,但是对方小玩偶离线",
				zap.String("dollId", req.DollId),
				zap.Any("processMap", process.DollProcessManagerInstance.GetDollProcessMap()))
			c.JSON(http.StatusOK, model.RoomEnterResponse{
				CommonResponse: model.ErrorResponse(401, "小玩偶离线"),
			})
			return
		}

		teamId, err := service.GroupChatServiceInstance.CreateTeam(h.ctx, []string{rtcUID, req.DollId}, service.GroupChatTeamType_ParentVoiceChat)
		if err != nil {
			logger.Error("创建群组失败", zap.Error(err))
			c.JSON(http.StatusOK, model.RoomEnterResponse{
				CommonResponse: model.ErrorResponse(500, "内部服务器错误"),
			})
			return
		}
		logger.Info("语音聊天,创建群组成功", zap.String("teamId", teamId))
	}

	// 生成房间信息
	roomID := utils.GenerateRoomId(rtcUID)
	roomToken, expireTime, err := utils.GenerateRoomToken(h.cfg.Rtc.AppID, h.cfg.Rtc.AppKey, roomID, rtcUID)
	if err != nil {
		logger.Error("生成房间Token失败", zap.Error(err))
		c.JSON(http.StatusOK, model.RoomEnterResponse{
			CommonResponse: model.ErrorResponse(500, "生成房间信息失败"),
		})
		return
	}

	logger.Info("正在为用户启动子进程",
		zap.String("uid", rtcUID),
		zap.String("roomId", roomID),
		zap.String("expireTime", expireTime),
	)

	// 启动子进程
	if err := process.DollProcessManagerInstance.StartDollProcess(rtcUID, roomID, "Start"); err != nil {
		logger.Error("启动子进程失败", zap.Error(err))
		c.JSON(http.StatusOK, model.RoomEnterResponse{
			CommonResponse: model.ErrorResponse(500, "服务错误"),
		})
		return
	}

	logger.Info("进入房间成功",
		zap.String("uid", rtcUID),
		zap.String("roomId", roomID),
		zap.String("roomToken", roomToken),
	)

	// TODO: 延迟300ms返回响应
	time.Sleep(300 * time.Millisecond)

	// 构造成功响应
	response := model.RoomEnterResponse{
		CommonResponse: model.SuccessResponse(),
		Data: model.RoomEnterData{
			UID:        rtcUID,
			RoomID:     roomID,
			RoomToken:  roomToken,
			ExpireTime: expireTime,
		},
	}

	logger.Info("进入房间响应",
		zap.String("uid", rtcUID),
		zap.String("roomId", roomID),
		zap.String("roomToken", roomToken))

	c.JSON(http.StatusOK, response)
}
