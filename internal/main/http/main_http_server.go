package http

import (
	"context"
	"fmt"
	"net/http"

	"aigc_server/internal/config"
	"aigc_server/internal/main/http/router"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

// MainHTTPServer HTTP服务器
type MainHTTPServer struct {
	ctx    context.Context
	cfg    *config.Config
	server *http.Server
	router *router.Router
}

// NewMainHTTPServer 创建HTTP服务器
func NewMainHTTPServer(ctx context.Context, cfg *config.Config) *MainHTTPServer {
	// 创建路由管理器
	r := router.NewRouter(ctx, cfg)

	return &MainHTTPServer{
		ctx:    ctx,
		cfg:    cfg,
		router: r,
	}
}

// Start 启动服务器
func (s *MainHTTPServer) Start() error {
	// 创建HTTP服务器
	addr := fmt.Sprintf("%s:%d", s.cfg.HTTPServer.Host, s.cfg.HTTPServer.Port)
	s.server = &http.Server{
		Addr:    addr,
		Handler: s.router.GetEngine(),
	}

	logger.Info("HTTP服务器启动中",
		zap.String("addr", addr),
		zap.String("env", s.cfg.Server.Env),
	)

	// 启动服务器
	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *MainHTTPServer) Stop() error {
	logger.Info("HTTP服务器停止中")
	return s.server.Close()
}

// GetRouter 获取路由管理器，用于动态添加路由
func (s *MainHTTPServer) GetRouter() *router.Router {
	return s.router
}
