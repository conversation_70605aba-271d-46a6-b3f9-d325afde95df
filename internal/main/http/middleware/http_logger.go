package middleware

import (
	"time"

	"aigc_server/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// HTTPLogger 创建HTTP日志中间件
func HTTPLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		start := time.Now()

		// 执行请求
		c.Next()

		// 计算耗时
		duration := time.Since(start)

		// 记录日志 - 只记录关键信息，保持简洁
		logger.Info("HTTP请求",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("client_ip", c.ClientIP()),
			zap.Int("status", c.Writer.Status()),
			zap.Duration("duration", duration),
			zap.String("user_agent", c.Request.UserAgent()),
		)
	}
}
