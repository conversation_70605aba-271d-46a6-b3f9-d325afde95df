package router

import (
	"context"

	"aigc_server/internal/config"
	"aigc_server/internal/main/http/handler"
	"aigc_server/internal/main/http/middleware"

	"github.com/gin-gonic/gin"
)

// Router 路由管理器
type Router struct {
	engine         *gin.Engine
	roomHandler    *handler.RoomHandler
	healthHandler  *handler.HealthHandler
	delSSIDHandler *handler.DelSSIDHandler
}

// NewRouter 创建路由管理器
func NewRouter(ctx context.Context, cfg *config.Config) *Router {
	// 设置gin模式
	if cfg.Server.Env == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	engine := gin.New()

	// 添加中间件
	engine.Use(middleware.HTTPLogger()) // 使用自定义HTTP日志中间件
	engine.Use(gin.Recovery())

	// 创建处理器
	roomHandler := handler.NewRoomHandler(ctx, cfg)
	healthHandler := handler.NewHealthHandler()
	delSSIDHandler := handler.NewDelSSIDHandler(ctx)

	router := &Router{
		engine:         engine,
		roomHandler:    roomHandler,
		healthHandler:  healthHandler,
		delSSIDHandler: delSSIDHandler,
	}

	// 配置路由
	router.setupRoutes()

	return router
}

// setupRoutes 配置路由
func (r *Router) setupRoutes() {
	// API版本组
	v1 := r.engine.Group("/aigc")
	{
		// 房间相关路由
		v1.POST("/room-enter", r.roomHandler.RoomEnter)
		// 删除Doll保存的wifi路由
		v1.POST("/doll/delete-ssid", r.delSSIDHandler.DelSSID)

	}

	// 健康检查路由
	r.engine.GET("/health", r.healthHandler.Health)

	// 系统管理路由组
	admin := r.engine.Group("/admin")
	{
		admin.GET("/health-detail", r.healthHandler.HealthDetail)
	}

}

// GetEngine 获取gin引擎实例
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}

// AddRoute 动态添加路由的方法，方便后续扩展
func (r *Router) AddRoute(method, path string, handlers ...gin.HandlerFunc) {
	switch method {
	case "GET":
		r.engine.GET(path, handlers...)
	case "POST":
		r.engine.POST(path, handlers...)
	case "PUT":
		r.engine.PUT(path, handlers...)
	case "DELETE":
		r.engine.DELETE(path, handlers...)
	case "PATCH":
		r.engine.PATCH(path, handlers...)
	}
}

// AddRouteGroup 添加路由组的方法
func (r *Router) AddRouteGroup(basePath string, setupFunc func(*gin.RouterGroup)) {
	group := r.engine.Group(basePath)
	setupFunc(group)
}
