# HTTP 服务器架构说明

## 概述

重构后的 HTTP 服务器采用分层架构，使用 gin 框架，具有良好的可扩展性和可维护性。

## 目录结构

```
internal/main/http/
├── main_http_server.go    # 主HTTP服务器
├── model/                 # 数据模型层
│   ├── common.go         # 通用响应结构
│   └── room.go           # 房间相关模型
├── handler/              # 处理器层
│   ├── room_handler.go   # 房间处理器
│   └── health_handler.go # 健康检查处理器
└── router/               # 路由层
    └── router.go         # 路由配置
```

## 核心组件

### 1. Model 层 (model/)

定义请求响应数据结构，提供数据模型的统一管理。

### 2. Handler 层 (handler/)

处理具体业务逻辑，每个功能模块对应一个 handler。

### 3. Router 层 (router/)

管理路由配置，支持路由组和中间件。

## 如何添加新接口

### 1. 创建数据模型 (可选)

在 `model/` 目录下创建对应的模型文件：

```go
// model/user.go
package model

type UserCreateRequest struct {
    Name  string `json:"name" binding:"required"`
    Email string `json:"email" binding:"required,email"`
}

type UserResponse struct {
    CommonResponse
    Data UserData `json:"data"`
}

type UserData struct {
    ID    string `json:"id"`
    Name  string `json:"name"`
    Email string `json:"email"`
}
```

### 2. 创建处理器

在 `handler/` 目录下创建对应的处理器：

```go
// handler/user_handler.go
package handler

import (
    "net/http"
    "aigc_server/internal/main/http/model"
    "github.com/gin-gonic/gin"
)

type UserHandler struct {
    // 依赖注入
}

func NewUserHandler() *UserHandler {
    return &UserHandler{}
}

func (h *UserHandler) CreateUser(c *gin.Context) {
    var req model.UserCreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusOK, model.UserResponse{
            CommonResponse: model.ErrorResponse(400, "参数错误"),
        })
        return
    }

    // 业务逻辑处理...

    c.JSON(http.StatusOK, model.UserResponse{
        CommonResponse: model.SuccessResponse(),
        Data: model.UserData{
            ID:    "generated-id",
            Name:  req.Name,
            Email: req.Email,
        },
    })
}
```

### 3. 注册路由

在 `router/router.go` 中添加新的处理器和路由：

```go
// 在Router结构体中添加
type Router struct {
    engine        *gin.Engine
    roomHandler   *handler.RoomHandler
    healthHandler *handler.HealthHandler
    userHandler   *handler.UserHandler  // 新增
}

// 在NewRouter函数中初始化
userHandler := handler.NewUserHandler()
router := &Router{
    engine:        engine,
    roomHandler:   roomHandler,
    healthHandler: healthHandler,
    userHandler:   userHandler,  // 新增
}

// 在setupRoutes函数中添加路由
v1 := r.engine.Group("/aigc")
{
    // 现有路由...
    v1.POST("/user", r.userHandler.CreateUser)  // 新增
}
```

## 现有接口

### 房间接口

- `POST /aigc/room-enter` - 进入房间

### 健康检查接口

- `GET /health` - 基础健康检查
- `GET /admin/health-detail` - 详细健康检查

## 中间件

当前已配置的中间件：

- `gin.Logger()` - 请求日志
- `gin.Recovery()` - 错误恢复

添加自定义中间件示例：

```go
// 在NewRouter函数中添加
engine.Use(func(c *gin.Context) {
    // 自定义中间件逻辑
    c.Next()
})
```

## 动态路由管理

支持通过 `GetRouter()` 方法获取路由管理器，动态添加路由：

```go
server := NewMainHTTPServer(ctx, cfg, startChildProcess)
router := server.GetRouter()

// 动态添加单个路由
router.AddRoute("GET", "/custom", customHandler)

// 动态添加路由组
router.AddRouteGroup("/api/v2", func(group *gin.RouterGroup) {
    group.GET("/test", testHandler)
    group.POST("/test", createTestHandler)
})
```

## 错误处理

统一使用 `model.CommonResponse` 进行错误响应：

```go
// 成功响应
c.JSON(http.StatusOK, model.SuccessResponse())

// 错误响应
c.JSON(http.StatusOK, model.ErrorResponse(400, "错误信息"))
```

## 注意事项

1. 所有的业务逻辑应该在 handler 层处理
2. 使用 gin 的数据绑定进行参数验证
3. 统一使用 logger 记录日志
4. 保持 handler 的职责单一
5. 复杂的数据结构定义在 model 层
