package ipc

import (
	"aigc_server/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"

	"go.uber.org/zap"
)

// 进程ID常量
var (
	processIDMain     = "main"
	onceProcessIDMain sync.Once
)

// GetWorkerProcessID 获取worker进程ID
func GetWorkerProcessID(dollId string) string {
	return fmt.Sprintf("worker:%s", dollId)
}
func GetDollIdFromProcessID(processID string) string {
	if strings.HasPrefix(processID, "worker:") {
		return strings.TrimPrefix(processID, "worker:")
	}
	return ""
}

// IPCManager IPC管理器
type IPCManager struct {
	QueueManager *QueueManager
	Router       *MessageRouter
	ProcessID    string
}

func GetIPCMainProcessID() string {
	onceProcessIDMain.Do(func() {
		podName := os.Getenv("POD_NAME")
		if podName == "" {
			podName = os.Getenv("HOSTNAME")
		}
		processIDMain = fmt.Sprintf("main:%s", podName)
	})
	return processIDMain
}

// NewIPCManager 创建IPC管理器
func NewIPCManager(processID string) *IPCManager {
	qm := NewQueueManager(processID)
	router := NewMessageRouter(qm)

	return &IPCManager{
		QueueManager: qm,
		Router:       router,
		ProcessID:    processID,
	}
}

// Start 启动IPC服务
func (ipc *IPCManager) Start(ctx context.Context) {
	ipc.Router.Start(ctx)
}

// Stop 停止IPC服务
func (ipc *IPCManager) Stop() {
	ipc.QueueManager.ClearQueue(context.Background(), ipc.ProcessID)
}

// SendMessage 发送消息
func (ipc *IPCManager) SendMessageByBytes(ctx context.Context, targetProcessID string, msgType MessageType, data []byte) error {
	msg := NewMessage(msgType, ipc.ProcessID, targetProcessID, data)
	return ipc.QueueManager.SendMessage(ctx, msg)
}
func (ipc *IPCManager) SendMessage(ctx context.Context, targetProcessID string, msgType MessageType, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		logger.Error("IPC消息序列化失败", zap.Error(err))
		return err
	}
	return ipc.SendMessageByBytes(ctx, targetProcessID, msgType, jsonData)
}
