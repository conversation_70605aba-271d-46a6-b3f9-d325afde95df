package ipc

import (
	"context"
	"time"

	"go.uber.org/zap"

	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
)

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(ctx context.Context, msg *Message) error
}

// MessageRouter 消息路由器
type MessageRouter struct {
	queueManager *QueueManager
	handlers     []MessageHandler
}

// NewMessageRouter 创建消息路由器
func NewMessageRouter(queueManager *QueueManager) *MessageRouter {
	return &MessageRouter{
		queueManager: queueManager,
	}
}
func (mr *MessageRouter) AddHandler(handler MessageHandler) {
	mr.handlers = append(mr.handlers, handler)
}
func (mr *MessageRouter) RemoveHandler(handler MessageHandler) {
	for i, h := range mr.handlers {
		if h == handler {
			mr.handlers = append(mr.handlers[:i], mr.handlers[i+1:]...)
			break
		}
	}
}

// Start 启动消息循环
func (mr *MessageRouter) Start(ctx context.Context) {
	logger.Info("启动消息路由器")

	go func() {
		defer utils.TraceRecover()
		defer func() {
			logger.Info("消息路由器已停止")
		}()

		for {
			select {
			case <-ctx.Done():
				return
			default:
				// 接收消息，超时时间1秒
				msg, err := mr.queueManager.ReceiveMessage(ctx, 1*time.Second)
				if err != nil {
					logger.Error("接收消息失败", zap.Error(err))
					continue
				}

				if msg == nil {
					// 超时无消息，继续循环
					continue
				}
				for _, handler := range mr.handlers {
					if err := handler.HandleMessage(ctx, msg); err != nil {
						logger.Error("消息处理失败",
							zap.String("message_id", msg.ID),
							zap.Any("type", msg.Type),
							zap.String("from", msg.From),
							zap.Error(err),
						)
					}
				}

			}
		}
	}()
}
