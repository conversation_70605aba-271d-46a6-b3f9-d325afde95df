package ipc

type MessageType string

const (
	// TCP相关消息类型
	MessageTypeDollMessage           MessageType = "doll_message"             // 娃娃消息转发
	MessageTypeDollCrossProcessEvent MessageType = "doll_cross_process_event" // 娃娃跨进程事件消息
	// MessageTypeMainProcessEvent      MessageType = "main_process_event"       // 主进程事件消息

	// 系统消息类型
	MessageTypeStatus MessageType = "status" // 状态消息
	MessageTypeEvent  MessageType = "event"  // 事件消息
	MessageTypeError  MessageType = "error"  // 错误消息
	MessageTypeLog    MessageType = "log"    // 日志消息
)
