package model

import (
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"context"
	"encoding/json"
	"io"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	RedisHSetKey = "doll:doll_status"
)

type DollStateType string

const (
	DollStateTypeIdle    DollStateType = "Idle"
	DollStateTypeSinging DollStateType = "Singing"
	DollStateTypeGaming  DollStateType = "Gaming"
	// DollStateTypeChating             DollStateType = "Chating"
	DollStateTypeStorytelling        DollStateType = "StoryTelling"
	DollStateTypeVoiceChatWithFriend DollStateType = "VoiceChatWithFriend"
	// DollStateTypeVoiceChatWithParent DollStateType = "VoiceChatWithParent"
	DollStateTypeVoiceChatCalling     DollStateType = "VoiceChatCalling"
	DollStateTypeVoiceChatCallByOther DollStateType = "VoiceChatCallByOther"
	DollStateTypeOffline              DollStateType = "Offline"
)

var mutex sync.RWMutex

type DollStatus struct {
	DollId          string        `json:"dollId"`
	Battery         int           `json:"battery"`
	Volume          int           `json:"volume"`
	Charging        bool          `json:"charging"`
	DollState       DollStateType `json:"dollState"`
	PlayGameName    string        `json:"playGameName"`
	VersionFirmware string        `json:"versionFirmware"`
	Timestamp       string        `json:"timestamp"`
	IsSounding      bool          `json:"isSounding"`

	redisKey string
	ctx      context.Context

	redisClient *redis.Client
}

func NewDollStatus(ctx context.Context, dollId string) *DollStatus {
	status := &DollStatus{
		DollId:      dollId,
		redisKey:    dollId,
		ctx:         ctx,
		redisClient: myredis.GetClient(),
	}
	status.Load()
	return status
}

func (s *DollStatus) SetState(dollState DollStateType) error {
	s.PlayGameName = ""
	if s.DollState == dollState {
		return nil
	}
	if dollState == DollStateTypeOffline {
		s.IsSounding = false
	}
	s.DollState = dollState
	err := s.Save()
	if err != nil {
		return err
	}
	return nil
}

func (s *DollStatus) SetVolume(volume int) error {
	s.Volume = volume
	err := s.Save()
	if err != nil {
		return err
	}
	return nil
}

func (s *DollStatus) SetPlayGameName(playGameName string) error {
	s.PlayGameName = playGameName
	s.DollState = DollStateTypeGaming
	err := s.Save()
	if err != nil {
		return err
	}
	return nil
}
func (s *DollStatus) SetSounding(sounding bool) error {
	s.IsSounding = sounding
	err := s.Save()
	if err != nil {
		return err
	}
	return nil
}

func (s *DollStatus) Save() error {
	mutex.Lock()
	defer mutex.Unlock()
	s.Timestamp = time.Now().Format(time.RFC3339)
	jsonStr, err := json.Marshal(s)
	if err != nil {
		logger.Error("娃娃状态序列化失败", zap.String("dollId", s.DollId), zap.Error(err))
		return err
	}
	err = s.redisClient.HSet(s.ctx, RedisHSetKey, s.redisKey, jsonStr).Err()
	s.redisClient.HExpire(s.ctx, RedisHSetKey, 7*24*time.Hour, s.redisKey)
	if err != nil {
		logger.Error("娃娃状态Redis保存失败",
			zap.String("redisField", s.redisKey),
			zap.Any("status", jsonStr),
			zap.Error(err))
		return err
	}
	logger.Info("娃娃Status.Save",
		zap.String("redisField", s.redisKey),
		zap.String("status", string(jsonStr)))
	return nil
}
func (s *DollStatus) Load() error {
	jsonStr, err := s.redisClient.HGet(s.ctx, RedisHSetKey, s.redisKey).Result()
	if err == redis.Nil || err == io.EOF || err == context.Canceled || jsonStr == "" {
		return nil
	}
	if err != nil {
		logger.Error("娃娃状态加载失败", zap.String("dollId", s.DollId), zap.Error(err))
		return err
	}
	err = json.Unmarshal([]byte(jsonStr), s)
	if err != nil {
		return err
	}
	return nil
}
