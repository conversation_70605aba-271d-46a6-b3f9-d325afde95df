package voicesource

import (
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
)

type SourceType string

const (
	SourceType_Base  SourceType = "base"
	SourceType_TTS   SourceType = "tts"
	SourceType_Media SourceType = "media"
	SourceType_Input SourceType = "input"
)

type IVoiceFrameSource interface {
	GetType() SourceType
	GetID() int64
	AddAudioBytes(audioBytes []byte)
	GetLen() int
	IsTailReached() bool
	IsDone() bool
	IsDiscard() bool
	AddListener(listener IListener)
	RemoveListener(listener IListener)
	GetFormat() *audio.AudioPCMFormat
	GetFrameSize() int
	NextFrame(paddedSize int) ([]byte, error)
	GetState() SourceState
	GetEnqueueTime() int64
	OnEnqueue()
	OnDequeue()
	ReadyForPlay()
	SetDone()
	SetFromLLMToolType(fromLLMToolType types.LLMToolStateType)
	GetFromLLMToolType() types.LLMToolStateType

	GetLoopCount() int
	SetLoopCount(loopCount int)
}
