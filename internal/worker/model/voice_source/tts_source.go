package voicesource

import (
	"aigc_server/internal/constant"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/proto"
	"aigc_server/pkg/utils"
	"context"
	"time"

	"go.uber.org/zap"
)

type TtsSource struct {
	BaseSource
	Index        int32
	SegmentIndex int32

	ctx    context.Context
	cancel context.CancelFunc
}

func NewTtsSource(index int32, segmentIndex int32) *TtsSource {
	format := &audio.AudioPCMFormat{
		SampleRate: constant.TtsRecordSampleRate,
		BitDepth:   constant.SampleBitDepth,
		Channels:   constant.AudioChannel,
	}
	ctx, cancel := context.WithCancel(context.Background())
	source := &TtsSource{
		BaseSource:   *NewBaseSource(format, SourceType_TTS),
		Index:        index,
		SegmentIndex: segmentIndex,
		ctx:          ctx,
		cancel:       cancel,
	}
	go source.FetchTts()
	return source
}

//	func (s *TtsSource) OnStateChange(state SourceState) {
//		if state == SourceState_Done {
//			s.cancel()
//		}
//		s.BaseSource.OnStateChange(state)
//	}
func (s *TtsSource) OnDequeue() {
	s.BaseSource.OnDequeue()
	s.cancel()
}

func (s *TtsSource) FetchTts() error {
	defer utils.TraceRecover()

	timeout := time.Millisecond * time.Duration(5000+s.SegmentIndex*2000)
	intervalTimer := time.AfterFunc(timeout, func() {
		logger.Info("TtsSource等待res超时,取消", zap.Any("source", s))
		s.cancel()
	})
	defer intervalTimer.Stop()

	// timerToForcePlay := time.AfterFunc(timeout, func() {
	// 	logger.Info("TtsSource等待res超时,强制开始play", zap.Any("source", s))
	// 	s.ReadyForPlay()
	// })
	// defer timerToForcePlay.Stop()

	handleId := service.GetEventEmitter().On(types.EventType_OnTtsResponse, func(event types.EventType, data interface{}) {
		response := data.(*proto.TTSResponse)
		if response.MsgIndex != s.Index || response.MsgSegmentIndex != s.SegmentIndex {
			return
		}
		if response.ErrorCode != 0 {
			logger.Warn("TTS响应ErrorCode", zap.Int32("error_code", response.ErrorCode), zap.Any("TTS Response", response))
			s.cancel()
			intervalTimer.Stop()
			return
		}
		if len(response.Audio) > 0 {
			s.AddAudioBytes(response.Audio)
			s.ReadyForPlay()
			// timerToForcePlay.Stop()
		}
		if response.IsFinal {
			s.cancel()
			intervalTimer.Stop()
		} else {
			intervalTimer.Reset(time.Millisecond * 3000)
		}
	}, s)
	defer service.GetEventEmitter().Off(types.EventType_OnTtsResponse, handleId)

	<-s.ctx.Done()
	if s.State == SourceState_Prepare {
		s.SetDone()
	}
	return nil
}
