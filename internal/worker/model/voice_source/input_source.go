package voicesource

import (
	"aigc_server/internal/constant"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/utils"
	"errors"
	"sync/atomic"
	"time"
)

type InputSource struct {
	BaseSource
	InputControlChan chan []byte
	HasInputDone     atomic.Bool
}

func NewInputSource() *InputSource {
	format := &audio.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		BitDepth:   constant.SampleBitDepth,
		Channels:   constant.AudioChannel,
	}
	source := &InputSource{
		BaseSource:       *NewBaseSource(format, SourceType_Input),
		InputControlChan: make(chan []byte, 5),
	}
	go source.FetchInput()
	return source
}

func (s *InputSource) FetchInput() {
	defer utils.TraceRecover()
	defer func() {
		s.HasInputDone.Store(true)
		if s.State == SourceState_Prepare {
			s.SetDone()
		}
	}()

	timeoutDur := time.Duration(3000) * time.Millisecond
	timer := time.NewTimer(timeoutDur)
	defer timer.Stop()

	s.HasInputDone.Store(false)
	for {
		select {
		case <-timer.C:
			return
		case data, ok := <-s.InputControlChan:
			if !ok {
				return
			}
			timer.Reset(timeoutDur)
			s.AddAudioBytes(data)
			s.ReadyForPlay()
		}
	}
}

func (s *InputSource) NextFrame(paddedSize int) ([]byte, error) {
	if s.IsDiscard() {
		return nil, errors.New("discard")
	}
	if !s.HasInputDone.Load() && s.IsTailReached() {
		return nil, nil
	}
	bytes, err := s._NextFrameNotSetDone(paddedSize)
	if s.HasInputDone.Load() {
		if s.IsTailReached() {
			s.SetDone()
		}
	}
	return bytes, err
}
