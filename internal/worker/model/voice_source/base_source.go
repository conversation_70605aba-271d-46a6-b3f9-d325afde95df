package voicesource

import (
	"aigc_server/internal/constant"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"errors"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

type ISourceIterator interface {
	NextFrame(needFrameSize int) ([]byte, int, error)
	IsDone() bool
}
type IListener interface {
	OnStateChange(source *BaseSource, state SourceState)
	OnDequeue(source *BaseSource)
}

type ListenerImpl struct {
	IListener
	onStateChange func(source *BaseSource, state SourceState)
	onDequeue     func(source *BaseSource)
}

func (l *ListenerImpl) OnStateChange(source *BaseSource, state SourceState) {
	if l.onStateChange != nil {
		l.onStateChange(source, state)
	}
}

func (l *ListenerImpl) OnDequeue(source *BaseSource) {
	if l.onDequeue != nil {
		l.onDequeue(source)
	}
}

func NewIListener(onStateChange func(source *BaseSource, state SourceState), onDequeue func(source *BaseSource)) IListener {
	return &ListenerImpl{
		onStateChange: onStateChange,
		onDequeue:     onDequeue,
	}
}

type SourceState string

const (
	// SourceState_Init    SourceState = "init"
	SourceState_Prepare SourceState = "prepare"
	SourceState_Playing SourceState = "playing"
	// SourceState_Discard SourceState = "discard"
	SourceState_Done SourceState = "done"
)

var globalID int64

type BaseSource struct {
	IVoiceFrameSource
	AudioBytes  []byte       `json:"-"`
	mutex       sync.RWMutex `json:"-"`
	Format      *audio.AudioPCMFormat
	FrameSize   int
	listeners   []IListener
	State       SourceState
	EnqueueTime int64

	CurFrameStartIndex int

	FromLLMToolType types.LLMToolStateType
	LoopCount       int
	ID              int64
	IsDiscarded     bool
	SourceType      SourceType
}

func NewBaseSource(format *audio.AudioPCMFormat, sourceType SourceType) *BaseSource {
	frameSize := format.Channels * format.SampleRate * format.BitDepth / 8 / constant.RtcFrameRate
	source := &BaseSource{
		Format:          format,
		FrameSize:       frameSize,
		listeners:       make([]IListener, 0),
		State:           SourceState_Prepare,
		FromLLMToolType: types.LLMToolStateType_NO_TOOL,
		LoopCount:       0,
		ID:              atomic.AddInt64(&globalID, 1),
		SourceType:      sourceType,
	}

	return source
}
func NewBaseSourceFromOneAudio(audioBytes []byte, sourceType SourceType, format *audio.AudioPCMFormat) *BaseSource {
	source := NewBaseSource(format, sourceType)
	source.AddAudioBytes(audioBytes)
	source.ReadyForPlay()
	return source
}

func (s *BaseSource) GetType() SourceType {
	return s.SourceType
}

func (s *BaseSource) GetID() int64 {
	return s.ID
}
func (s *BaseSource) AddAudioBytes(audioBytes []byte) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if s.AudioBytes == nil {
		s.AudioBytes = make([]byte, 0)
	}
	s.AudioBytes = append(s.AudioBytes, audioBytes...)
}
func (s *BaseSource) GetAudioBytes() []byte {
	return s.AudioBytes
}
func (s *BaseSource) GetLen() int {
	if s.AudioBytes == nil {
		return 0
	}
	return len(s.AudioBytes)
}
func (s *BaseSource) GetFormat() *audio.AudioPCMFormat {
	return s.Format
}
func (s *BaseSource) GetFrameSize() int {
	return s.FrameSize
}
func (s *BaseSource) _NextFrameNotSetDone(paddedSize int) ([]byte, error) {
	if s.IsDiscard() {
		return nil, errors.New("discard")
	}
	if s.State == SourceState_Prepare {
		return nil, nil
	}
	if s.State != SourceState_Playing {
		return nil, errors.New("not playing")
	}
	needFrameSize := s.FrameSize - paddedSize
	if needFrameSize <= 0 {
		return nil, errors.New("needFrameSize <= 0")
	}
	if s.IsDone() {
		return nil, errors.New("done")
	}
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	// 直接使用len(s.AudioBytes)而不是调用GetLen()，避免重入锁
	audioLen := len(s.AudioBytes)
	if s.CurFrameStartIndex >= audioLen {
		return nil, errors.New("done")
	}
	iStart := s.CurFrameStartIndex
	iEnd := min(iStart+needFrameSize, audioLen)
	s.CurFrameStartIndex = iEnd

	if s.CurFrameStartIndex == audioLen {
		if s.GetLoopCount() == 0 {
			// 不循环
		} else {
			logger.Info("BaseSource 循环播放", zap.Int("loopCount", s.GetLoopCount()), zap.Any("source", s))
			if s.GetLoopCount() > 0 {
				s.SetLoopCount(s.GetLoopCount() - 1)
			}
			s.CurFrameStartIndex = 0
		}

	}
	return s.AudioBytes[iStart:iEnd], nil
}
func (s *BaseSource) NextFrame(paddedSize int) ([]byte, error) {
	data, err := s._NextFrameNotSetDone(paddedSize)
	if err != nil {
		return data, err
	}
	if s.State == SourceState_Playing && s.IsTailReached() {
		s.SetDone()
	}
	return data, nil
}
func (s *BaseSource) IsTailReached() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	// 直接使用len(s.AudioBytes)而不是调用GetLen()，避免重入锁
	if s.AudioBytes == nil {
		return true
	}
	return s.CurFrameStartIndex >= len(s.AudioBytes)
}
func (s *BaseSource) ReadyForPlay() {
	s.SetState(SourceState_Playing)
}

func (s *BaseSource) IsDone() bool {
	return s.State == SourceState_Done
}
func (s *BaseSource) IsDiscard() bool {
	return s.IsDiscarded
}

func (s *BaseSource) AddListener(listener IListener) {
	s.listeners = append(s.listeners, listener)
}

func (s *BaseSource) RemoveListener(listener IListener) {
	s.listeners = utils.Filter(s.listeners, func(l IListener) bool {
		return l != listener
	})
}

func (s *BaseSource) SetDone() {
	s.SetState(SourceState_Done)
}

func (s *BaseSource) OnEnqueue() {
	s.EnqueueTime = time.Now().UnixMilli()
	logger.Debug("Source 入队", zap.Any("source", s))
}
func (s *BaseSource) OnDequeue() {
	if s.IsDiscarded {
		logger.Warn("Source 重复discard", zap.Any("source", s))
		return
	}
	s.IsDiscarded = true
	for _, listener := range s.listeners {
		listener.OnDequeue(s)
	}

	logger.Debug("Source 出队", zap.Any("source", s))
}
func (s *BaseSource) GetEnqueueTime() int64 {
	return s.EnqueueTime
}

func (s *BaseSource) SetState(state SourceState) {
	if s.State == state {
		return
	}
	s.State = state
	s.OnStateChange(state)
}

func (s *BaseSource) OnStateChange(state SourceState) {
	for _, listener := range s.listeners {
		listener.OnStateChange(s, state)
	}
}

func (s *BaseSource) GetState() SourceState {
	return s.State
}

func (s *BaseSource) SetFromLLMToolType(fromLLMToolType types.LLMToolStateType) {
	s.FromLLMToolType = fromLLMToolType
}
func (s *BaseSource) GetFromLLMToolType() types.LLMToolStateType {
	return s.FromLLMToolType
}

func (s *BaseSource) GetLoopCount() int {
	return s.LoopCount
}
func (s *BaseSource) SetLoopCount(loopCount int) {
	s.LoopCount = loopCount
}
