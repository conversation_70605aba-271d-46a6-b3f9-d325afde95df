package voicesource

import (
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"sync"

	"go.uber.org/zap"
)

type VoiceSourceMix struct {
	Sources []IVoiceFrameSource
	Mutex   sync.Mutex
}

func NewMixSourceSet() *VoiceSourceMix {
	return &VoiceSourceMix{
		Sources: make([]IVoiceFrameSource, 0),
	}
}

func (s *VoiceSourceMix) AddSource(source IVoiceFrameSource) {
	s.Mutex.Lock()
	s.Sources = append(s.Sources, source)
	s.Mutex.Unlock()
	source.OnEnqueue()
}

func (s *VoiceSourceMix) RemoveSource(source IVoiceFrameSource) {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	s.Sources = utils.Filter(s.Sources, func(s IVoiceFrameSource) bool {
		return s.GetID() != source.GetID()
	})
}
func (s *VoiceSourceMix) Clear() {
	s.Mutex.Lock()
	sources := make([]IVoiceFrameSource, len(s.Sources))
	copy(sources, s.Sources)
	s.Sources = make([]IVoiceFrameSource, 0)
	s.Mutex.Unlock()
	for _, source := range sources {
		source.OnDequeue()
	}
}

func (s *VoiceSourceMix) DiscardSource(source IVoiceFrameSource) {
	if source == nil {
		return
	}
	s.RemoveSource(source)
	source.OnDequeue()
}

func (s *VoiceSourceMix) GetSources() []IVoiceFrameSource {
	return s.Sources
}

func (s *VoiceSourceMix) GetNextFrames(format *audio.AudioPCMFormat) [][]byte {
	var result [][]byte
	useSources := make([]IVoiceFrameSource, len(s.Sources))
	copy(useSources, s.Sources)
	for _, source := range useSources {
		frameData, err := source.NextFrame(0)
		if err != nil {
			logger.Error("获取下一帧失败", zap.Error(err), zap.Any("source", source))
			s.DiscardSource(source)
			continue
		}
		if source.IsDone() {
			s.DiscardSource(source)
			continue
		}
		if frameData == nil {
			continue
		}
		frameData, err = audio.ResamplePCM(frameData, *source.GetFormat(), *format)
		if err != nil {
			logger.Error("重采样失败", zap.Error(err))
			s.DiscardSource(source)
			continue
		}
		result = append(result, frameData)
	}
	return result
}
