package voicesource

import (
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"sync"

	"go.uber.org/zap"
)

type VoiceSourceBackground struct {
	BGMSource IVoiceFrameSource
	mutex     sync.RWMutex
}

func NewVoiceSourceBackground() *VoiceSourceBackground {
	return &VoiceSourceBackground{
		BGMSource: nil,
	}
}

func (s *VoiceSourceBackground) GetNextFrame(format *audio.AudioPCMFormat) []byte {
	s.mutex.RLock()
	if s.BGMSource == nil {
		s.mutex.RUnlock()
		return nil
	}
	source := s.BGMSource
	s.mutex.RUnlock()
	bytes, err := source.NextFrame(0)
	if err != nil {
		s.Stop()
		logger.Error("获取下一帧失败", zap.Error(err), zap.Any("source", source))
		return nil
	}
	if format != nil && source.GetFormat() != nil && len(bytes) > 0 {
		bytes, err = audio.ResamplePCM(bytes, *source.GetFormat(), *format)
		if err != nil {
			logger.Error("VoiceSourceBackground 重采样失败", zap.Error(err), zap.Any("source", source))
			return nil
		}
	}
	return bytes
}

func (s *VoiceSourceBackground) Play(source IVoiceFrameSource, loopCount int) {
	s.Stop()
	s.mutex.Lock()
	s.BGMSource = source
	s.mutex.Unlock()
	if source != nil {
		logger.Info("VoiceSourceBackground 播放背景音乐", zap.Any("source", source))
		source.SetLoopCount(loopCount)
		source.OnEnqueue()
	}
}

func (s *VoiceSourceBackground) Stop() IVoiceFrameSource {
	s.mutex.Lock()
	if s.BGMSource == nil {
		s.mutex.Unlock()
		return nil
	}
	source := s.BGMSource
	s.BGMSource = nil
	s.mutex.Unlock()
	if source != nil {
		logger.Info("VoiceSourceBackground 停止背景音乐", zap.Any("source", source))
		source.OnDequeue()
	}
	return source
}
