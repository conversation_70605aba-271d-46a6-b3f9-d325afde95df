package voicesource

import (
	"aigc_server/internal/constant"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"

	"go.uber.org/zap"
)

type MediaSource struct {
	BaseSource
	MediaType    service.MediaType
	MediaName    string
	Index        int32
	SegmentIndex int32

	MediaInfo *service.MediaInfo
}

func NewMediaSource(content string, mediaType service.MediaType, llmToolType types.LLMToolStateType, index int32, segmentIndex int32) *MediaSource {
	format := &audio.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		BitDepth:   constant.SampleBitDepth,
		Channels:   constant.AudioChannel,
	}
	source := &MediaSource{
		BaseSource:   *NewBaseSource(format, SourceType_Media),
		MediaName:    content,
		MediaType:    mediaType,
		Index:        index,
		SegmentIndex: segmentIndex,
	}
	source.SetFromLLMToolType(llmToolType)
	go source.FetchMedia()
	return source
}

func (s *MediaSource) FetchMedia() error {
	defer utils.TraceRecover()

	ctx := context.Background()
	mediaInfo, err := service.GetMediaServiceInstance().GetMediaInfo(ctx, s.MediaType, s.MediaName)
	if err != nil {
		logger.Error("获取媒体信息失败", zap.Error(err))
		s.SetDone()
		return err
	}
	logger.Info("获得媒体信息成功", zap.Any("mediaInfo", mediaInfo))
	s.MediaInfo = mediaInfo
	pcm, err := service.GetMediaServiceInstance().DownloadAudioToPCM(ctx, mediaInfo.Url, s.Format)
	if err != nil {
		logger.Error("下载媒体数据失败", zap.Error(err))
		s.SetDone()
		return err
	}
	s.AddAudioBytes(pcm)
	s.ReadyForPlay()

	return nil
}
