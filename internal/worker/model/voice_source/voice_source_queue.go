package voicesource

import (
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"sync"

	"go.uber.org/zap"
)

type VoiceSourceQueue struct {
	Sources []IVoiceFrameSource
	Mutex   sync.RWMutex
}

func NewVoiceSourceQueue() *VoiceSourceQueue {
	return &VoiceSourceQueue{
		Sources: make([]IVoiceFrameSource, 0),
	}
}

func (q *VoiceSourceQueue) GetCurrentSource() IVoiceFrameSource {
	q.Mutex.RLock()
	defer q.Mutex.RUnlock()
	if len(q.Sources) == 0 {
		return nil
	}
	return q.Sources[0]
}
func (q *VoiceSourceQueue) AddSource(source IVoiceFrameSource) {
	q.Mutex.Lock()
	q.Sources = append(q.Sources, source)
	q.Mutex.Unlock()
	source.OnEnqueue()
}
func (q *VoiceSourceQueue) Clear() {
	logger.Info("VoiceSourceQueue Clear")
	q.Mutex.Lock()
	copedSources := make([]IVoiceFrameSource, len(q.Sources))
	copy(copedSources, q.Sources)
	q.Sources = make([]IVoiceFrameSource, 0)
	q.Mutex.Unlock()
	for _, source := range copedSources {
		source.OnDequeue()
	}
}

func (q *VoiceSourceQueue) DiscardSource(source IVoiceFrameSource) {
	q.Mutex.Lock()
	// 直接在这里执行RemoveSource的逻辑，避免重复获取锁
	q.Sources = utils.Filter(q.Sources, func(s IVoiceFrameSource) bool {
		return s.GetID() != source.GetID()
	})
	q.Mutex.Unlock()
	source.OnDequeue()
}

func (q *VoiceSourceQueue) GetNextFrame(targetFormat *audio.AudioPCMFormat) []byte {
	paddingFrameSize := 0
	var result []byte
	var format *audio.AudioPCMFormat
	var frameSize int
	for range 10 {
		source := q.GetCurrentSource()
		if source == nil {
			break
		}
		sourceFormat := source.GetFormat()
		if format == nil {
			format = sourceFormat
		} else if sourceFormat == nil ||
			format.BitDepth != sourceFormat.BitDepth ||
			format.Channels != sourceFormat.Channels ||
			format.SampleRate != sourceFormat.SampleRate {
			logger.Warn("VoiceSourceQueue GetNextFrame source 格式不匹配", zap.Any("source", source), zap.Any("format", format), zap.Any("sourceFormat", sourceFormat))
			break
		}
		if frameSize == 0 {
			frameSize = source.GetFrameSize()
		}
		frameData, err := source.NextFrame(paddingFrameSize)
		if err != nil {
			logger.Error("获取下一帧失败", zap.Error(err), zap.Any("source", source))
			q.DiscardSource(source)
			continue
		}
		if frameData != nil {
			if result == nil {
				result = frameData
			} else {
				result = append(result, frameData...)
			}
		}
		if source.IsDone() {
			q.DiscardSource(source)
			continue
		}
		if frameData == nil {
			break
		}
		paddingFrameSize += len(frameData)
		if paddingFrameSize >= frameSize {
			break
		}
	}
	if result != nil && format != nil && targetFormat != nil {
		var err error
		result, err = audio.ResamplePCM(result, *format, *targetFormat)
		if err != nil {
			logger.Error("VoiceSourceQueue GetNextFrame 重采样失败", zap.Error(err))
			return nil
		}
	}
	return result
}

func (q *VoiceSourceQueue) LLMResetIndexFilter(llmIndex int32) int {
	q.Mutex.RLock()
	discards := make([]IVoiceFrameSource, 0)
	for _, source := range q.Sources {
		switch cur := source.(type) {
		case *TtsSource:
			if cur.Index >= 0 && cur.Index < llmIndex {
				discards = append(discards, source)
			}
		case *MediaSource:
			if cur.Index >= 0 && cur.Index < llmIndex {
				discards = append(discards, source)
			}
		}
	}
	q.Mutex.RUnlock()
	for _, source := range discards {
		logger.Info("VoiceSourceQueue 丢弃source", zap.Any("source", source), zap.Int32("llmIndex", llmIndex))
		// source.OnDequeue()
		q.DiscardSource(source)
	}
	return len(discards)
}
