package model

import (
	"aigc_server/internal/constant"
	voicesource "aigc_server/internal/worker/model/voice_source"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

type VoiceSourceModel struct {
	MainQueue    *voicesource.VoiceSourceQueue
	MixSourceSet *voicesource.VoiceSourceMix
	Background   *voicesource.VoiceSourceBackground
	TargetFormat *audio.AudioPCMFormat

	ResetInterruptIndex int32
	AsrInterruptTime    atomic.Int64
	FrameSize           int
}

func NewVoiceSourceModel(format *audio.AudioPCMFormat) *VoiceSourceModel {
	frameSize := format.Channels * format.SampleRate * format.BitDepth / 8 / constant.RtcFrameRate
	return &VoiceSourceModel{
		MainQueue:           voicesource.NewVoiceSourceQueue(),
		MixSourceSet:        voicesource.NewMixSourceSet(),
		Background:          voicesource.NewVoiceSourceBackground(),
		TargetFormat:        format,
		ResetInterruptIndex: 0,
		AsrInterruptTime:    atomic.Int64{},
		FrameSize:           frameSize,
	}
}

func (m *VoiceSourceModel) AddSourceMain(source voicesource.IVoiceFrameSource) voicesource.IVoiceFrameSource {
	m.MainQueue.AddSource(source)
	return source
}
func (m *VoiceSourceModel) AddSourceMix(source voicesource.IVoiceFrameSource) voicesource.IVoiceFrameSource {
	m.MixSourceSet.AddSource(source)
	return source
}
func (m *VoiceSourceModel) PlayBackground(source voicesource.IVoiceFrameSource, loopCount int) {
	m.Background.Play(source, loopCount)
}
func (m *VoiceSourceModel) StopBackground() voicesource.IVoiceFrameSource {
	return m.Background.Stop()
}

func (m *VoiceSourceModel) SetLLMResetInterruptIndex(index int32) {
	logger.Info("VoiceFrameModel 重置 ResetIndex", zap.Int32("index", index))
	m.ResetInterruptIndex = index
	m.MainQueue.LLMResetIndexFilter(index)
}

func (m *VoiceSourceModel) SetAsrInterrupt() {
	if m.AsrInterruptTime.Load() > 0 {
		return
	}
	cur := m.MainQueue.GetCurrentSource()
	if cur != nil {
		if cur.GetFromLLMToolType() == types.LLMToolStateType_TELL_STORY ||
			cur.GetFromLLMToolType() == types.LLMToolStateType_PLAY_MUSIC ||
			cur.GetFromLLMToolType() == types.LLMToolStateType_PLAY_AUDIO {
			logger.Info("VoiceFrameModel 设置 ASR 中断, 当前Source为Media工具调用, 跳过", zap.Any("source", cur))
			return
		}
	}
	logger.Info("VoiceFrameModel 设置 ASR 中断")
	m.AsrInterruptTime.Store(time.Now().UnixMilli())
}
func (m *VoiceSourceModel) ResumeAsrInterrupt() {
	if m.AsrInterruptTime.Load() == 0 {
		return
	}
	logger.Info("VoiceFrameModel 恢复 ASR 中断")
	m.AsrInterruptTime.Store(0)
}

// func (m *VoiceFrameModel) ClearMainQueue() {
// 	m.MainQueue.Clear()
// }
// func (m *VoiceFrameModel) ClearMixSourceSet() {
// 	m.MixSourceSet.Clear()
// }

func (m *VoiceSourceModel) Clear() {
	m.MainQueue.Clear()
	m.MixSourceSet.Clear()
	m.Background.Stop()
}
func (m *VoiceSourceModel) ClearMainAndMix() {
	m.MainQueue.Clear()
	m.MixSourceSet.Clear()
}

func (m *VoiceSourceModel) NextFrames(format *audio.AudioPCMFormat) [][]byte {
	var frameDatas [][]byte = make([][]byte, 0)
	if m.AsrInterruptTime.Load() > 0 && time.Now().UnixMilli()-m.AsrInterruptTime.Load() > 10000 {
		logger.Info("VoiceFrameModel Asr 中断超时, Resume")
		m.ResumeAsrInterrupt()
	}
	if m.AsrInterruptTime.Load() == 0 {
		mainFrame := m.MainQueue.GetNextFrame(format)
		if mainFrame != nil {
			frameDatas = append(frameDatas, mainFrame)
		}
	}
	mixFrames := m.MixSourceSet.GetNextFrames(format)
	frameDatas = append(frameDatas, mixFrames...)
	bgmFrame := m.Background.GetNextFrame(format)
	if bgmFrame != nil {
		frameDatas = append(frameDatas, bgmFrame)
	}

	return frameDatas
}

func (m *VoiceSourceModel) NextMixedFrame() []byte {
	datas := m.NextFrames(m.TargetFormat)
	if len(datas) == 0 {
		return nil
	}
	if len(datas) == 1 {
		return datas[0]
	}
	mixed, err := audio.MixAudioFrames(datas, *m.TargetFormat)
	if err != nil {
		logger.Error("混音失败", zap.Error(err))
		return nil
	}

	return mixed
}

func PaddingEmptyFrame(frameSize int) []byte {
	emptyFrameData := make([]byte, frameSize)
	for i := range emptyFrameData {
		if i%2 == 0 {
			emptyFrameData[i] = 0x80
		} else {
			emptyFrameData[i] = 0x00
		}
	}
	return emptyFrameData
}
