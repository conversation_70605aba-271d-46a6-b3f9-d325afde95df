package model

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"aigc_server/pkg/utils"
	"context"
	"encoding/json"

	"go.uber.org/zap"
)

type DollCmdParams struct {
	DollId      string
	RoomId      string
	StartupWord types.StartupWordType
}
type DollInfo struct {
	DollId      string                `json:"doll_id"`
	RoomId      string                `json:"room_id"`
	ServerUid   string                `json:"server_uid"`
	TimbreId    string                `json:"timbre_id"`
	StartupWord types.StartupWordType `json:"startup_word"`
}

func NewDollInfo(ctx context.Context, params DollCmdParams) *DollInfo {
	return &DollInfo{
		DollId:      params.DollId,
		RoomId:      params.RoomId,
		ServerUid:   utils.GetRTCServerUserId(params.DollId),
		TimbreId:    "",
		StartupWord: params.StartupWord,
	}
}

func (d *DollInfo) StartSyncTimbre(ctx context.Context) error {
	timbreId, err := service.GetMediaServiceInstance().StartSyncTimbre(ctx, d.DollId)
	if err != nil {
		logger.Error("同步音色ID失败", zap.Error(err))
	} else {
		d.TimbreId = timbreId
	}
	go d.ListenChannelForTimbreInfo(ctx)
	return nil
}

func (d *DollInfo) ListenChannelForTimbreInfo(ctx context.Context) error {
	defer utils.TraceRecover()
	// redis 订阅
	logger.Info("开始订阅channel音色ID", zap.String("dollId", d.DollId))
	channel := "doll:timbre:select"
	pubsub := myredis.GetClient().Subscribe(ctx, channel)
	defer pubsub.Unsubscribe(ctx, channel)
	defer pubsub.Close()
	for {
		select {
		case <-ctx.Done():
			return nil
		case msg := <-pubsub.Channel():
			logger.Info("接收channel音色ID成功", zap.String("msg", msg.Payload))
			payload := service.TimbreInfoPayload{}
			err := json.Unmarshal([]byte(msg.Payload), &payload)
			if err != nil {
				logger.Error("解析channel音色ID失败", zap.Error(err))
			} else if payload.DollId == d.DollId {
				d.TimbreId = payload.TimbreId
			}
		}
	}
}
