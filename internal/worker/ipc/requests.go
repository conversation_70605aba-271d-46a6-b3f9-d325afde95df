package ipc

import (
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"context"

	"go.uber.org/zap"
)

var IpcRequestInstance *IpcRequest

type IpcRequest struct {
	ipcManager                  *ipc.IPCManager
	SetVolumeResErrorRetryCount int
}

func NewIpcRequest(ipcManager *ipc.IPCManager) *IpcRequest {
	IpcRequestInstance = &IpcRequest{
		ipcManager: ipcManager,
	}
	return IpcRequestInstance
}

func (h *IpcRequest) DoSetVolume(ctx context.Context, dollId string, volume int) error {

	logger.Info("发送音量设置请求", zap.String("doll_id", dollId), zap.Int("volume", volume))
	msg := tcp.CreateRequestDollMessage(tcp.MsgTypeSetVolumeRequest)
	msg.(*tcp.DollSetVolumeRequest).Volume = volume
	h.ipcManager.SendMessage(ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage, msg)

	return nil
}

func (h *IpcRequest) DoAddFriend(ctx context.Context) error {
	logger.Info("发送给ipc->tcp添加好友请求")
	msg := tcp.CreateRequestDollMessage(tcp.MsgTypeAddFriendRequest)
	h.ipcManager.SendMessage(ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage, msg)

	return nil
}

func (h *IpcRequest) DoCrossProcessEmitEvent(ctx context.Context, toDoll string, eventType types.EventType, data string) error {
	logger.Info("跨进程事件通知", zap.String("toDoll", toDoll), zap.String("eventType", string(eventType)), zap.String("data", string(data)))
	msg := types.DollProcessEvent{
		EventType: eventType,
		Data:      data,
	}
	processID := ipc.GetWorkerProcessID(toDoll)
	h.ipcManager.SendMessage(ctx, processID, ipc.MessageTypeDollCrossProcessEvent, msg)
	return nil
}
