package llm

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	proto "aigc_server/pkg/proto"
	"aigc_server/pkg/utils"
	"context"
	"encoding/json"

	"go.uber.org/zap"
)

type LLMState struct {
	ctx        context.Context
	grpcClient *service.GRPCClient
	dollId     string

	LLMUsingTimbreId string // 当前LLM使用的音色ID

	// ToolCall 管理
	toolCallFactory *ToolCallFactory
	// toolCallQueue    []*ToolCallTask
	// queueMutex       sync.RWMutex

	MsgToolCallStateMapping map[int32]types.LLMToolStateType

	voiceFrameModel *model.VoiceSourceModel
}

func NewLLMState(ctx context.Context, dollId string, grpcClient *service.GRPCClient, voiceFrameModel *model.VoiceSourceModel) *LLMState {
	state := &LLMState{
		ctx:                     ctx,
		grpcClient:              grpcClient,
		dollId:                  dollId,
		MsgToolCallStateMapping: make(map[int32]types.LLMToolStateType),
		voiceFrameModel:         voiceFrameModel,
	}
	state.toolCallFactory = NewToolCallFactory(state)
	return state
}

// buildLLMToolStateFromLLMResp 从LLM响应构建工具调用任务
func (s *LLMState) buildLLMToolStateFromLLMResp(resp *proto.LLMResponse) []ToolCall {
	if resp.ToolCall == "" {
		// logger.Warn("LLM响应工具调用为空", zap.Any("LLMResponse", resp))
		return nil
	}

	// 解析工具调用JSON
	var toolCallMap map[types.LLMToolStateType]string
	if err := json.Unmarshal([]byte(resp.ToolCall), &toolCallMap); err != nil {
		logger.Error("解析工具调用JSON失败", zap.Error(err), zap.String("toolCall", resp.ToolCall))
		return nil
	}
	if len(toolCallMap) == 0 {
		return nil
	}
	logger.Info("解析工具调用JSON成功", zap.Any("LLMResponse", resp), zap.Any("toolCallMap", toolCallMap))

	tasks := make([]ToolCall, 0)

	// 为每个工具调用创建任务
	for toolName, content := range toolCallMap {
		if toolName == types.LLMToolStateType_NO_TOOL {
			continue
		}
		toolCall := s.toolCallFactory.CreateToolCall(toolName, content, resp.MsgIndex, resp.MsgSegmentIndex)
		if toolCall == nil {
			logger.Warn("跳过未知的工具调用", zap.Any("toolName", toolName), zap.String("content", content))
			continue
		}

		tasks = append(tasks, toolCall)
	}

	return tasks
}

// ProcessLLMResponse 处理LLM响应并执行工具调用
func (s *LLMState) ProcessLLMResponse(resp *proto.LLMResponse) {
	tasks := s.buildLLMToolStateFromLLMResp(resp)
	if len(tasks) > 0 {
		// 同步执行任务
		for _, task := range tasks {
			go s.executeToolCallTask(task)
		}
	}
}

// executeToolCallTask 执行单个工具调用任务
func (s *LLMState) executeToolCallTask(task ToolCall) {
	defer utils.TraceRecover()
	logger.Info("开始执行工具调用任务", zap.Any("task", task))

	err := task.Run(s.ctx)
	if err != nil {
		logger.Error("工具调用任务执行失败", zap.Error(err))
		return
	}
}
