package llm

import (
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

// ToolCallFactory 工具调用工厂
type ToolCallFactory struct {
	State *LLMState
}

func NewToolCallFactory(state *LLMState) *ToolCallFactory {
	return &ToolCallFactory{
		State: state,
	}
}

// CreateToolCall 根据工具名称创建工具调用实例
func (f *ToolCallFactory) CreateToolCall(toolType types.LLMToolStateType, content string, index int32, segmentIndex int32) ToolCall {
	var tool ToolCall = nil
	switch toolType {
	case types.LLMToolStateType_TELL_STORY:
		tool = NewTellStoryToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_MUSIC:
		tool = NewPlayMusicToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_AUDIO:
		tool = NewPlayAudioToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_MIX_AUDIO:
		tool = NewPlayMixAudioToolCall(f.State, content)
	case types.LLMToolStateType_VOLUME:
		tool = NewVolumeToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_GAME:
		tool = NewPlayGameToolCall(f.State, content)
	case types.LLMToolStateType_STOP_GAME:
		tool = NewStopGameToolCall(f.State, content)
	case types.LLMToolStateType_ADD_FRIEND:
		tool = NewAddFriendToolCall(f.State, content)
	case types.LLMToolStateType_CALLING_FRIEND_NOTIFY:
		tool = NewCallingFriendNotifyToolCall(f.State, content)
	// case types.LLMToolStateType_CALLING_FRIEND_RESULT:
	// tool = NewCallingFriendResponseToolCall(f.State, content)
	// case types.LLMToolStateType_CALL_BY_FRIEND_REQ:
	// 	tool = NewCallByFriendRequestToolCall(f.State, content)
	case types.LLMToolStateType_CALL_BY_FRIEND_RESP:
		tool = NewCallByFriendResponseToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_BACKGROUND_MUSIC:
		tool = NewPlayBackgroundMusicToolCall(f.State, content)
	case types.LLMToolStateType_STOP_BACKGROUND_MUSIC:
		tool = NewStopBackgroundMusicToolCall(f.State, content)
	case types.LLMToolStateType_CHANGE_TTS_TIMBRE:
		tool = NewChangeTtsTimbreToolCall(f.State, content)
	case types.LLMToolStateType_RESET_TTS_TIMBRE:
		tool = NewResetTtsTimbreToolCall(f.State, content)
	default:
		logger.Warn("未知的工具调用类型", zap.String("toolName", string(toolType)))
		return nil
	}
	if tool != nil { // 统一设置index和segmentIndex
		f.State.MsgToolCallStateMapping[index] = toolType // 反向映射,index => toolType

		tool.SetIndex(index)
		tool.SetSegmentIndex(segmentIndex)
		logger.Info("创建工具调用实例", zap.Any("tool", (tool)))
	}
	return tool
}
