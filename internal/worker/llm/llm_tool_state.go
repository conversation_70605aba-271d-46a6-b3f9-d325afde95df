package llm

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/model"
	voicesource "aigc_server/internal/worker/model/voice_source"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

// ToolCall 定义工具调用接口
type ToolCall interface {
	// Run 执行工具调用，返回是否生成了音频数据
	Run(ctx context.Context) error
	// GetType 获取工具类型
	GetType() types.LLMToolStateType
	GetIndex() int32
	GetSegmentIndex() int32
	GetContent() string
	SetIndex(index int32)
	SetSegmentIndex(segmentIndex int32)
}

// ToolCallResult 工具调用结果
// type ToolCallResult struct {
// 	MediaInfo *service.MediaInfo `json:"media_info"`
// }

// BaseToolCall 基础工具调用实现
type BaseToolCall struct {
	ToolCall
	State        *LLMState `json:"-"`
	ToolType     types.LLMToolStateType
	Index        int32 // LLM响应索引
	SegmentIndex int32 // LLM响应段索引
	Content      string
}

func (b *BaseToolCall) GetType() types.LLMToolStateType {
	return b.ToolType
}

func (b *BaseToolCall) GetIndex() int32 {
	return b.Index
}
func (b *BaseToolCall) GetSegmentIndex() int32 {
	return b.SegmentIndex
}
func (b *BaseToolCall) GetContent() string {
	return b.Content
}
func (b *BaseToolCall) SetIndex(index int32) {
	b.Index = index
}
func (b *BaseToolCall) SetSegmentIndex(segmentIndex int32) {
	b.SegmentIndex = segmentIndex
}

// TellStoryToolCall 讲故事工具调用
type TellStoryToolCall struct {
	BaseToolCall
}

func NewTellStoryToolCall(state *LLMState, content string) *TellStoryToolCall {
	return &TellStoryToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_TELL_STORY,
			Content:  content,
		},
	}
}

func (t *TellStoryToolCall) Run(ctx context.Context) error {
	dollStatus := model.NewDollStatus(ctx, t.State.dollId)
	dollStatus.SetState(model.DollStateTypeStorytelling)

	source := t.State.voiceFrameModel.AddSourceMain(voicesource.NewMediaSource(t.Content, service.MediaType_Story, t.ToolType, t.Index, t.SegmentIndex))
	source.AddListener(voicesource.NewIListener(nil, func(source *voicesource.BaseSource) {
		dollStatus = model.NewDollStatus(ctx, t.State.dollId)
		dollStatus.SetState(model.DollStateTypeIdle)
	}))
	return nil
}

// PlayMusicToolCall 播放音乐工具调用
type PlayMusicToolCall struct {
	BaseToolCall
}

func NewPlayMusicToolCall(state *LLMState, content string) *PlayMusicToolCall {
	return &PlayMusicToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_PLAY_MUSIC,
			Content:  content,
		},
	}
}

func (p *PlayMusicToolCall) Run(ctx context.Context) error {
	dollStatus := model.NewDollStatus(ctx, p.State.dollId)
	dollStatus.SetState(model.DollStateTypeSinging)

	source := p.State.voiceFrameModel.AddSourceMain(voicesource.NewMediaSource(p.Content, service.MediaType_Music, p.ToolType, p.Index, p.SegmentIndex))
	source.AddListener(voicesource.NewIListener(nil, func(source *voicesource.BaseSource) {
		dollStatus = model.NewDollStatus(ctx, p.State.dollId)
		dollStatus.SetState(model.DollStateTypeIdle)
	}))
	return nil
}

// PlayAudioToolCall 播放音频工具调用
type PlayAudioToolCall struct {
	BaseToolCall
}

func NewPlayAudioToolCall(state *LLMState, content string) *PlayAudioToolCall {
	return &PlayAudioToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_PLAY_AUDIO,
			Content:  content,
		},
	}
}

func (p *PlayAudioToolCall) Run(ctx context.Context) error {
	p.State.voiceFrameModel.AddSourceMain(voicesource.NewMediaSource(p.Content, service.MediaType_Audio, p.ToolType, p.Index, p.SegmentIndex))

	return nil
}

type PlayMixAudioToolCall struct {
	BaseToolCall
}

func NewPlayMixAudioToolCall(state *LLMState, content string) *PlayMixAudioToolCall {
	return &PlayMixAudioToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_PLAY_MIX_AUDIO,
			Content:  content,
		},
	}
}

func (p *PlayMixAudioToolCall) Run(ctx context.Context) error {
	p.State.voiceFrameModel.AddSourceMix(voicesource.NewMediaSource(p.Content, service.MediaType_Audio, p.ToolType, p.Index, p.SegmentIndex))

	return nil
}

// VolumeToolCall 音量控制工具调用
type VolumeToolCall struct {
	BaseToolCall
}

func NewVolumeToolCall(state *LLMState, content string) *VolumeToolCall {
	return &VolumeToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_VOLUME,
			Content:  content,
		},
	}
}

func (v *VolumeToolCall) Run(ctx context.Context) error {
	logger.Info("执行音量控制", zap.String("content", v.Content))
	if v.Content == "" {
		logger.Error("音量控制内容为空")
		return fmt.Errorf("音量控制内容为空")
	}

	setValue, err := strconv.Atoi(v.Content)
	if err != nil {
		logger.Error("音量控制解析失败2", zap.String("content", v.Content), zap.Error(err))
		return err
	}

	dollStatus := model.NewDollStatus(ctx, v.State.dollId)
	err = dollStatus.SetVolume(setValue)
	if err != nil {
		logger.Error("音量控制Redis保存失败", zap.Error(err))
		return err
	}
	err = ipc.IpcRequestInstance.DoSetVolume(ctx, v.State.dollId, setValue)
	if err != nil {
		logger.Error("音量控制IPC保存失败", zap.Error(err))
		return err
	}
	logger.Info("llm音量控制保存成功", zap.String("dollId", v.State.dollId), zap.Int("volume", setValue))

	return nil
}

type PlayGameToolCall struct {
	BaseToolCall
}

func NewPlayGameToolCall(state *LLMState, content string) *PlayGameToolCall {
	return &PlayGameToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_PLAY_GAME,
			Content:  content,
		},
	}
}

func (p *PlayGameToolCall) Run(ctx context.Context) error {
	dollStatus := model.NewDollStatus(ctx, p.State.dollId)
	dollStatus.SetPlayGameName(p.Content)

	return nil
}

type StopGameToolCall struct {
	BaseToolCall
}

func NewStopGameToolCall(state *LLMState, content string) *StopGameToolCall {

	return &StopGameToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_STOP_GAME,
			Content:  content,
		},
	}
}

func (s *StopGameToolCall) Run(ctx context.Context) error {
	dollStatus := model.NewDollStatus(ctx, s.State.dollId)
	dollStatus.SetState(model.DollStateTypeIdle)

	return nil
}

type AddFriendToolCall struct {
	BaseToolCall
}

func NewAddFriendToolCall(state *LLMState, content string) *AddFriendToolCall {
	return &AddFriendToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_ADD_FRIEND,
			Content:  content,
		},
	}
}

func (a *AddFriendToolCall) Run(ctx context.Context) error {
	ipc.IpcRequestInstance.DoAddFriend(ctx)
	return nil
}

// CallingFriendNotifyToolCall 呼叫朋友请求工具调用
type CallingFriendNotifyToolCall struct {
	BaseToolCall
}

func NewCallingFriendNotifyToolCall(state *LLMState, content string) *CallingFriendNotifyToolCall {
	return &CallingFriendNotifyToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_CALLING_FRIEND_NOTIFY,
			Content:  content,
		},
	}
}

func (c *CallingFriendNotifyToolCall) Run(ctx context.Context) error {
	logger.Info("执行呼叫朋友请求", zap.String("content", c.Content))

	service.GetEventEmitter().SyncEmit(types.EventType_Calling_Friend_Notify, c.Content)

	return nil
}

// CallByFriendResponseToolCall 被朋友呼叫响应工具调用
type CallByFriendResponseToolCall struct {
	BaseToolCall
}

func NewCallByFriendResponseToolCall(state *LLMState, content string) *CallByFriendResponseToolCall {
	return &CallByFriendResponseToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_CALL_BY_FRIEND_RESP,
			Content:  content,
		},
	}
}

func (c *CallByFriendResponseToolCall) Run(ctx context.Context) error {
	logger.Info("LLMState 执行被朋友呼叫响应", zap.String("content", c.Content))

	service.GetEventEmitter().SyncEmit(types.EventType_Call_By_Friend_LLM_RESP, c.Content)

	return nil
}

type PlayBackgroundMusicToolCall struct {
	BaseToolCall
}

func NewPlayBackgroundMusicToolCall(state *LLMState, content string) *PlayBackgroundMusicToolCall {
	return &PlayBackgroundMusicToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_PLAY_BACKGROUND_MUSIC,
			Content:  content,
		},
	}
}

func (p *PlayBackgroundMusicToolCall) Run(ctx context.Context) error {
	logger.Info("执行播放背景音乐", zap.String("content", p.Content))

	if p.Content == "" {
		logger.Error("播放背景音乐内容为空")
		return fmt.Errorf("播放背景音乐内容为空")
	}
	// if p.State.dollId == "JL-FR-SF-SuanNai-99af630016f4" {
	// 	logger.Info("跳过播放背景音乐", zap.String("content", p.Content))
	// 	return nil
	// }

	p.State.voiceFrameModel.PlayBackground(voicesource.NewMediaSource(p.Content, service.MediaType_Music, p.ToolType, p.Index, p.SegmentIndex), -1)

	return nil
}

type StopBackgroundMusicToolCall struct {
	BaseToolCall
}

func NewStopBackgroundMusicToolCall(state *LLMState, content string) *StopBackgroundMusicToolCall {
	return &StopBackgroundMusicToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_STOP_BACKGROUND_MUSIC,
			Content:  content,
		},
	}
}

func (s *StopBackgroundMusicToolCall) Run(ctx context.Context) error {
	logger.Info("执行停止背景音乐", zap.String("content", s.Content))

	s.State.voiceFrameModel.StopBackground()

	return nil
}

type ChangeTtsTimbreToolCall struct {
	BaseToolCall
}

func NewChangeTtsTimbreToolCall(state *LLMState, content string) *ChangeTtsTimbreToolCall {
	return &ChangeTtsTimbreToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_CHANGE_TTS_TIMBRE,
			Content:  content,
		},
	}
}

func (c *ChangeTtsTimbreToolCall) Run(ctx context.Context) error {
	logger.Info("执行改变TTS音色", zap.String("content", c.Content))
	c.State.LLMUsingTimbreId = c.Content
	return nil
}

type ResetTtsTimbreToolCall struct {
	BaseToolCall
}

func NewResetTtsTimbreToolCall(state *LLMState, content string) *ResetTtsTimbreToolCall {
	return &ResetTtsTimbreToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			ToolType: types.LLMToolStateType_RESET_TTS_TIMBRE,
			Content:  content,
		},
	}
}

func (r *ResetTtsTimbreToolCall) Run(ctx context.Context) error {
	logger.Info("执行重置TTS音色", zap.String("content", r.Content))
	r.State.LLMUsingTimbreId = ""
	return nil
}
