package doll

import (
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"time"

	workerIPC "aigc_server/internal/worker/ipc"

	"go.uber.org/zap"
)

type DollIPCMessageHandler struct {
	dollState  *DollController
	ipcManager *ipc.IPCManager
}

func NewDollIPCMessageHandler(dollState *DollController, ipcManager *ipc.IPCManager) *DollIPCMessageHandler {
	return &DollIPCMessageHandler{
		dollState:  dollState,
		ipcManager: ipcManager,
	}
}

func (h *DollIPCMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("worker进程收到IPC消息", zap.Any("msg", msg))
	switch msg.Type {
	case ipc.MessageTypeDollMessage:
		return h.handleDollMessage(ctx, msg)
	case ipc.MessageTypeDollCrossProcessEvent:
		return h.handleDollCrossProcessEvent(msg)
	}
	return nil
}

func (h *DollIPCMessageHandler) handleDollCrossProcessEvent(msg *ipc.Message) error {
	logger.Info("收到处理娃娃跨进程事件消息", zap.Any("data", msg))

	var event types.DollProcessEvent
	err := json.Unmarshal(msg.Data, &event)
	if err != nil {
		logger.Error("娃娃事件消息解析失败", zap.Error(err))
		return err
	}
	service.GetEventEmitter().SyncEmit(event.EventType, event.Data)
	return nil
}

// handleDollMessage 处理娃娃消息
func (h *DollIPCMessageHandler) handleDollMessage(ctx context.Context, msg *ipc.Message) error {
	// logger.Info("处理娃娃消息", zap.Any("data", msg))

	dollTcpMsg, err := tcp.ParseDollMessage(msg.Data)
	if err != nil {
		logger.Error("娃娃消息解析失败", zap.Any("data", msg.Data))
		return nil
	}

	switch cur := dollTcpMsg.(type) {
	// case tcp.DollEnterRoomRequest:
	// case tcp.DollEnterRoomResponse:
	case tcp.DollHeartbeatRequest:
		h.handleDollHeartbeatRequest(&cur)
	case tcp.DollHeartbeatResponse:
	case tcp.DollSetVolumeRequest:
	case tcp.DollSetVolumeResponse:
		h.handleDollSetVolumeResponse(&cur)
	case tcp.DollAddFriendRequest:
	case tcp.DollAddFriendResponse:
		h.handleDollAddFriendResponse(&cur)
	case tcp.DollVoiceInterruptRequest:
		h.handleDollVoiceInterruptRequest(&cur)
	case tcp.DollVoiceInterruptResponse:
	case tcp.DollDiscoverFriendRequest:
		h.handleDollDiscoverFriendRequest(&cur)
	case tcp.DollDiscoverFriendResponse:
	case tcp.DollWifiListUploadRequest:
	case tcp.DollWifiListUploadResponse:
	case tcp.DollDelSavedWifiRequest:
	case tcp.DollDelSavedWifiResponse:
		h.handleDollDelSavedWifiResponse(&cur)
	case tcp.DollOtaNotifyRequest:
	case tcp.DollOtaNotifyResponse:
	case tcp.DollOtaProcessRequest:
	case tcp.DollOtaProcessResponse:
	case tcp.DollSensorAccRequest:
		h.handleDollSensorAccRequest(&cur)
	case tcp.DollSensorAccResponse:
	default:
		logger.Error("娃娃消息Type失败", zap.Any("data", msg.Data))
		return nil
	}
	return nil
}

func (h *DollIPCMessageHandler) handleDollHeartbeatRequest(cur *tcp.DollHeartbeatRequest) error {
	// dollStatus := model.NewDollStatus(h.dollState.ctx, h.dollState.DollInfo.DollId)
	// dollStatus.Volume = cur.Volume
	// dollStatus.Battery = cur.Battery
	// dollStatus.Charging = cur.Charging
	// dollStatus.VersionFirmware = cur.Version
	// err := dollStatus.Save()
	// logger.Info("娃娃状态同步客户端成功", zap.Any("data", cur))
	// if err != nil {
	// 	logger.Error("娃娃消息Save失败", zap.Any("data", cur), zap.Error(err))
	// 	return err
	// }
	// 在main已经回复
	return nil
}

func (h *DollIPCMessageHandler) handleDollSetVolumeResponse(cur *tcp.DollSetVolumeResponse) error {
	if cur.Code == 0 {
		logger.Info("娃娃音量设置成功", zap.Any("data", cur))
		workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount = 0
		return nil
	}
	logger.Error("娃娃音量设置失败", zap.Any("data", cur))
	if cur.Code == 1 {
		if workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount > 3 {
			logger.Error("娃娃音量设置失败重试次数过多", zap.Any("data", cur))
			return nil
		}
		workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount++
		go func() {
			logger.Info("重试设置音量 延迟2s")
			time.Sleep(2 * time.Second)
			workerIPC.IpcRequestInstance.DoSetVolume(h.dollState.ctx, h.dollState.DollInfo.DollId, cur.Data.Volume)
		}()
	}
	return nil
}

func (h *DollIPCMessageHandler) handleDollAddFriendResponse(cur *tcp.DollAddFriendResponse) error {
	logger.Info("tcp->ipc 娃娃添加好友响应", zap.Any("response", cur))
	return nil
}

func (h *DollIPCMessageHandler) handleDollDiscoverFriendRequest(cur *tcp.DollDiscoverFriendRequest) error {
	logger.Info("tcp->ipc 娃娃发现好友请求", zap.Any("request", cur))
	err := h.dollState.OnTcpDiscoverFriend(cur)
	var msg *tcp.DollDiscoverFriendResponse
	if err != nil {
		msg = &tcp.DollDiscoverFriendResponse{
			DollCommonResponseMessage: tcp.DollCommonResponseMessage{
				Code:    1,
				Message: err.Error(),
			},
		}
	} else {
		msg = &tcp.DollDiscoverFriendResponse{
			DollCommonResponseMessage: tcp.DollCommonResponseMessage{
				Code:    0,
				Message: "success",
			},
		}
	}
	h.ipcManager.SendMessage(h.dollState.ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage, msg)
	return nil
}

func (h *DollIPCMessageHandler) handleDollVoiceInterruptRequest(cur *tcp.DollVoiceInterruptRequest) error {
	logger.Info("tcp->ipc 娃娃语音中断请求", zap.Any("request", cur))

	teamId, err := service.GroupChatServiceInstance.FindTeam(h.dollState.ctx, h.dollState.DollInfo.DollId)
	if err != nil || teamId == "" {
		logger.Info("娃娃语音中断请求,未找到团队,进行语音打断")
		h.dollState.SetPressResetInterruptIndexForceMax()
	} else {
		logger.Info("娃娃语音中断请求,找到团队,挂断通话", zap.String("teamId", teamId))
		service.GetEventEmitter().SyncEmit(types.EventType_Call_Friend_Over, teamId)
	}

	msg := tcp.CreateResponseDollMessage(tcp.MsgTypeVoiceInterruptResponse, 0, "success")
	h.ipcManager.SendMessage(h.dollState.ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage, msg)
	return nil
}

func (h *DollIPCMessageHandler) handleDollDelSavedWifiResponse(cur *tcp.DollDelSavedWifiResponse) error {
	logger.Info("tcp->ipc 娃娃删除保存wifi响应", zap.Any("response", cur))
	if cur.Code == 0 {
		logger.Info("娃娃删除保存wifi成功", zap.Any("response", cur))
		return nil
	} else {
		logger.Error("娃娃删除保存wifi失败", zap.Any("response", cur))
		return fmt.Errorf("娃娃删除保存wifi失败: %s", cur.Message)
	}
}

func (h *DollIPCMessageHandler) handleDollSensorAccRequest(cur *tcp.DollSensorAccRequest) error {
	logger.Info("tcp->ipc 娃娃加速度传感器请求", zap.Any("request", cur))
	jsonStr, err := json.Marshal(cur.Acc)
	if err != nil {
		logger.Error("娃娃加速度传感器请求序列化失败", zap.Error(err))
		h.ipcManager.SendMessage(h.dollState.ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage,
			tcp.CreateResponseDollMessage(tcp.MsgTypeSensorAccResponse, 1, err.Error()))
		return err
	}
	h.dollState.GrpcClient.SendLLMMessage("", true, &map[string]string{
		string(types.LLMToolStateType_REQ_DOLL_SENSOR_ACC): string(jsonStr),
	})
	h.ipcManager.SendMessage(h.dollState.ctx, ipc.GetIPCMainProcessID(), ipc.MessageTypeDollMessage,
		tcp.CreateResponseDollMessage(tcp.MsgTypeSensorAccResponse, 0, "success"))
	return nil
}
