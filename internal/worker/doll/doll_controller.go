package doll

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/service"
	friendsvoicechat "aigc_server/internal/worker/doll/friends_voice_chat"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/llm"
	"aigc_server/internal/worker/model"
	voicesource "aigc_server/internal/worker/model/voice_source"
	"aigc_server/internal/worker/rtc"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/audio"
	"aigc_server/pkg/logger"
	proto "aigc_server/pkg/proto"
	"aigc_server/pkg/utils"
	"sync"
	"time"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

func (s *DollController) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {

}

func (s *DollController) OnUserJoined(userInfo rtcengine.UserInfo) {
	dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
	dollStatus.SetState(model.DollStateTypeIdle)
	s.SendLLMRequestStartupWord()
}
func (s *DollController) OnUserLeave(uid string, reason rtcengine.UserOfflineReason) {
	s.CtxCancel()
}
func (s *DollController) OnRemoteUserAudioFrame(_ rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	for {
		select {
		case s.remoteAudioDataChan <- audioFrame:
			return
		default:
			logger.Info("OnRemoteUserAudioFrame. 音频帧通道已满，丢弃音频帧")
			return
		}
	}
}

type DollController struct {
	Cfg *config.Config

	// RtcService     *rtc.RTCService
	GrpcClient     *service.GRPCClient
	grpcNetHandler *GrpcNetHandler

	remoteAudioDataChan  chan rtcengine.IAudioFrame
	outputAudioFrameChan chan rtcengine.IAudioFrame

	ctx       context.Context
	CtxCancel context.CancelFunc

	LlmState *llm.LLMState

	IpcHandler *DollIPCMessageHandler

	DollInfo *model.DollInfo

	groupChatTransport *service.GroupChatTransport
	groupChatTransChan chan []byte

	// friendsVoiceChatEventHandler *FriendsVoiceChatEventHandler
	friendsVoiceChatFSM *friendsvoicechat.FriendsVoiceChatFSM

	voiceFrameModel *model.VoiceSourceModel

	isSounding bool
	Cache      *service.CacheService
}

func NewDollControllerAndStart(ctx context.Context, ctxCancel context.CancelFunc, cfg *config.Config, params model.DollCmdParams) (*DollController, error) {
	controller := &DollController{
		Cfg:       cfg,
		ctx:       ctx,
		CtxCancel: ctxCancel,
		DollInfo:  model.NewDollInfo(ctx, params),
	}

	// go controller.RoomLooping()
	return controller, nil
}
func (s *DollController) SetIpcHandler(ipcHandler *DollIPCMessageHandler) {
	s.IpcHandler = ipcHandler
	ipc.NewIpcRequest(s.IpcHandler.ipcManager)
}

func (s *DollController) RoomLooping() error {
	defer utils.TraceRecover()
	defer func() {
		dollStatus := model.NewDollStatus(context.Background(), s.DollInfo.DollId)
		dollStatus.SetState(model.DollStateTypeOffline)
		logger.Info("DollController 已停止",
			zap.String("roomID", s.DollInfo.RoomId),
			zap.String("uid", s.DollInfo.DollId),
			zap.String("serverUid", s.DollInfo.ServerUid),
		)
	}()
	s.Cache = service.GetCacheServiceInstance()
	defer s.Cache.Stop()

	s.voiceFrameModel = model.NewVoiceSourceModel(&audio.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		Channels:   constant.AudioChannel,
		BitDepth:   constant.SampleBitDepth,
	})
	defer s.voiceFrameModel.Clear()

	var err error

	// 创建GRPC客户端
	s.GrpcClient, err = service.NewGRPCClient(s.Cfg, s.DollInfo.DollId)
	if err != nil {
		logger.Error("创建GRPC客户端失败", zap.Error(err))
		return err
	}
	s.grpcNetHandler = NewGrpcNetHandler(s)
	s.GrpcClient.AddHandler(s.grpcNetHandler)

	defer func() {
		s.GrpcClient.SendLLMToDisconnect()
		s.GrpcClient.RemoveHandler(s.grpcNetHandler)
		s.GrpcClient.Close()
	}()

	if err := s.DollInfo.StartSyncTimbre(s.ctx); err != nil {
		logger.Error("启动同步音色ID失败", zap.Error(err))
		return err
	}
	s.LlmState = llm.NewLLMState(s.ctx, s.DollInfo.DollId, s.GrpcClient, s.voiceFrameModel)

	s.remoteAudioDataChan = make(chan rtcengine.IAudioFrame, 100)
	defer close(s.remoteAudioDataChan)
	s.outputAudioFrameChan = make(chan rtcengine.IAudioFrame, 100)
	defer close(s.outputAudioFrameChan)
	var rtcService *rtc.RTCService
	rtcService, err = rtc.NewRTCService(s.ctx, s.Cfg, s.DollInfo.DollId, s.DollInfo.ServerUid, s.DollInfo.RoomId, s, s.outputAudioFrameChan)
	if err != nil {
		logger.Error("启动RTC服务失败", zap.Error(err))
		return err
	}
	defer rtcService.Stop()

	timeout, cancelTimeout := context.WithTimeout(s.ctx, 60*time.Second)
	defer cancelTimeout()
	go func() {
		defer utils.TraceRecover()
		<-timeout.Done()
		if rtcService.UserJoinedStatus == rtc.UserJoinedStatusNotJoined {
			logger.Error("RTCService RoomLooping超时，用户未加入房间", zap.String("roomID", rtcService.RoomID), zap.String("uid", rtcService.Uid), zap.String("serverUid", rtcService.ServerUid))
			s.CtxCancel()
		}
	}()

	go s.CheckGroupChatTransport()

	go s.loopReceiveAudioFrame()
	go s.loopDispatchAudioFrame()
	go s.loopReceiveGroupChatAudio()

	// s.friendsVoiceChatEventHandler = NewFriendsVoiceChatEventHandler(s)
	// s.friendsVoiceChatEventHandler.Listen()
	// defer s.friendsVoiceChatEventHandler.Clear()
	s.friendsVoiceChatFSM = friendsvoicechat.NewFriendsVoiceChatFSM(s.ctx, s.DollInfo.DollId, s.GrpcClient)
	defer s.friendsVoiceChatFSM.Stop()

	<-s.ctx.Done()

	return nil
}

func (s *DollController) loopReceiveGroupChatAudio() error {
	defer utils.TraceRecover()
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("DollController ctx已取消,停止接收群组聊天音频帧", zap.String("roomID", s.DollInfo.RoomId), zap.String("uid", s.DollInfo.DollId), zap.String("serverUid", s.DollInfo.ServerUid))
			return nil
		case data := <-s.groupChatTransChan:
			s.voiceFrameModel.AddSourceMain(voicesource.NewBaseSourceFromOneAudio(data, voicesource.SourceType_Base, s.voiceFrameModel.TargetFormat))
		}
	}
}

func (s *DollController) loopReceiveAudioFrame() error {
	defer utils.TraceRecover()
	var saveFileChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		saveFileChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcRecvSaveFilePath(s.DollInfo.DollId))
	}
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("DollController ctx已取消,停止接收音频帧", zap.String("roomID", s.DollInfo.RoomId), zap.String("uid", s.DollInfo.DollId), zap.String("serverUid", s.DollInfo.ServerUid))
			return nil
		case frame, ok := <-s.remoteAudioDataChan:
			if !ok {
				logger.Info("DollController remoteAudioDataChan已关闭")
				return nil
			}
			s.handleReceiveRtcAudioFrame(frame)
			if s.Cfg.Debug.SaveAudio {
				select {
				case saveFileChan <- frame.Data():
				default:
					logger.Warn("DollController ContinueWriteAppendData saveFileChan已满，丢弃音频帧")
				}
			}
		}
	}
}

func (s *DollController) loopDispatchAudioFrame() error {
	defer utils.TraceRecover()
	audioFramePipelineChan := make(chan rtcengine.IAudioFrame)
	defer close(audioFramePipelineChan)

	logger.Info("启动协程推送外部音频帧", zap.Int("frame", constant.RtcFrameRate))

	go s.frameProducer(audioFramePipelineChan)
	go s.frameConsumer(audioFramePipelineChan)

	<-s.ctx.Done()
	return nil
}
func (s *DollController) CheckGroupChatTransport() {
	defer utils.TraceRecover()
	defer func() {
		if s.groupChatTransport != nil {
			s.groupChatTransport.Stop()
			s.groupChatTransport = nil
		}
	}()
	s.groupChatTransChan = make(chan []byte, 200)
	defer close(s.groupChatTransChan)

	mutexGroupChatTransport := sync.Mutex{}
	onStartGroupChatTransport := func(transport *service.GroupChatTransport) {

		logger.Info("创建群组聊天传输成功",
			zap.String("teamId", transport.TeamId),
			zap.String("memberMe", s.DollInfo.DollId),
			zap.String("toMember", transport.ToMember))
		dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
		dollStatus.SetState(model.DollStateTypeVoiceChatWithFriend)

		s.voiceFrameModel.ClearMainAndMix()

		beginSource := voicesource.NewInputSource()
		voiceInputCh := beginSource.InputControlChan
		defer close(voiceInputCh)
		beginSource.AddListener(voicesource.NewIListener(nil, func(source *voicesource.BaseSource) {
			logger.Info("通话开始提示音播放完了,ClearAllVoiceFrameSource")
			s.voiceFrameModel.ClearMainAndMix()
		}))
		s.voiceFrameModel.AddSourceMain(beginSource)

		members, teamType := service.GroupChatServiceInstance.ParseTeamId(transport.TeamId)
		if teamType == service.GroupChatTeamType_ParentVoiceChat && len(members) >= 2 && members[1] == s.DollInfo.DollId {
			logger.Info("DollController 家长来电,发送家长来电TTS文本")
			ttsResList, err := s.GrpcClient.SendTTSStatelessForResponse("家长来电话啦", s.DollInfo.TimbreId)
			if err != nil {
				logger.Error("发送家长来电TTS文本失败", zap.Error(err))
			} else {
				logger.Info("DollController 家长来电,获得家长来电TTS成功", zap.Int("ttsResList", len(ttsResList)))
				for _, ttsRes := range ttsResList {
					voiceInputCh <- ttsRes.Audio
				}
			}
		}
		startAudio, _, err := service.GetMediaServiceInstance().GetCachedCallHintAudio(s.ctx)
		if err != nil {
			logger.Error("获取通话开始提示音失败", zap.Error(err))
		} else {
			voiceInputCh <- startAudio
		}
	}
	onStopGroupChatTransport := func(transport *service.GroupChatTransport) {
		logger.Info("DollController 群组聊天结束,设置状态为Idle")
		dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
		dollStatus.SetState(model.DollStateTypeIdle)

		_, finishAudio, err := service.GetMediaServiceInstance().GetCachedCallHintAudio(s.ctx)
		if err != nil {
			logger.Error("获取通话结束提示音失败", zap.Error(err))
		} else {
			s.voiceFrameModel.AddSourceMain(voicesource.NewBaseSourceFromOneAudio(finishAudio, voicesource.SourceType_Base, s.voiceFrameModel.TargetFormat))
		}
	}
	handler := func() {
		mutexGroupChatTransport.Lock()
		defer mutexGroupChatTransport.Unlock()
		if s.groupChatTransport == nil {
			s.groupChatTransport, _ = service.GroupChatServiceInstance.StartGroupChatTransport(s.ctx, s.DollInfo.DollId, s.groupChatTransChan)
			if s.groupChatTransport != nil {
				onStartGroupChatTransport(s.groupChatTransport)
			}
		} else {
			if !s.groupChatTransport.TeamAvailable() {
				onStopGroupChatTransport(s.groupChatTransport)

				s.groupChatTransport.Stop()
				s.groupChatTransport = nil
			}
		}
	}
	ticker := time.Tick(time.Millisecond * 100)
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker:
			handler()
		}
	}
}
func (s *DollController) CheckIfIsSounding(isSounding bool) error {
	if isSounding == s.isSounding {
		return nil
	}
	s.isSounding = isSounding
	dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
	dollStatus.SetSounding(isSounding)
	return nil
}
func (s *DollController) frameProducer(audioFramePipeline chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()
	frameCount := 0
	// time.Sleep(time.Second * 1)
	for {
		select {
		case <-s.ctx.Done():
			return nil
		default:
			audioFrame := s.TakeAFrame()
			if audioFrame != nil {
				frameCount = 0
				s.CheckIfIsSounding(true)
			} else {
				s.CheckIfIsSounding(false)
				if !utils.IsWebTestRTCUser(s.DollInfo.DollId) {
					if frameCount < constant.RtcFrameRate*3/5 {
						audioFrame = rtcengine.BuildAudioFrame(rtcengine.AudioFrameBuilder{
							SampleRate: s.voiceFrameModel.TargetFormat.SampleRate,
							Channel:    s.voiceFrameModel.TargetFormat.Channels,
							Data:       model.PaddingEmptyFrame(s.voiceFrameModel.FrameSize),
						})
						frameCount++
					}
				}
				if audioFrame == nil {
					time.Sleep(time.Millisecond * 10)
					continue
				}
			}
			select {
			case <-s.ctx.Done():
				return nil
			case audioFramePipeline <- audioFrame:
			}
		}
	}
}

func (s *DollController) frameConsumer(audioFramePipeline chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()

	var debugSaveAudioChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		debugSaveAudioChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcSendSaveFilePath(s.DollInfo.DollId))
	}

	totalFrameCount := 0
	busyFrameIndex := 0
	frameCountToIdle := 20 * constant.RtcFrameRate

	frameRate := constant.RtcFrameRate * 105 / 100 // 消费速度加快一些,避免数据延迟

	ticker := time.Tick(time.Millisecond * time.Duration(1000/frameRate))
	for range ticker {
		totalFrameCount++
		select {
		case <-s.ctx.Done():
			logger.Info("DollController ctx已取消,停止发送音频帧", zap.String("roomID", s.DollInfo.RoomId), zap.String("uid", s.DollInfo.DollId), zap.String("serverUid", s.DollInfo.ServerUid))
			return nil
		case audioFrame := <-audioFramePipeline:
			busyFrameIndex = totalFrameCount
			go func() {
				defer utils.TraceRecover()
				select {
				case <-s.ctx.Done():
					return
				default:
					// ret := s.RtcService.PushExternalAudioFrame(audioFrame)
					// if ret != 0 {
					// 	logger.Error("DollController 推送外部音频帧API调用错误,错误码 %d", zap.Int("error code", ret))
					// }
					s.outputAudioFrameChan <- audioFrame
					if s.Cfg.Debug.SaveAudio {
						select {
						case debugSaveAudioChan <- audioFrame.Data():
						default:
							logger.Warn("DollController ContinueWriteAppendData debugSaveAudioChan已满,丢弃音频帧")
						}
					}
				}
			}()
		default:
			if totalFrameCount-busyFrameIndex > frameCountToIdle {
				busyFrameIndex = totalFrameCount
				dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
				dollStatus.SetState(model.DollStateTypeIdle)
			}
		}
	}
	return nil
}
func (s *DollController) OnTTSResult(audioDatas []byte, index int32, segment_index int32) error {
	return nil
}
func (s *DollController) CheckLLMResetOrResume(response *proto.LLMResponse) {
	switch response.Type {
	case proto.LLMRespType_RESET:
		s.SetLLMResetInterruptIndex(response.MsgIndex)
		s.SetAsrResume()
	case proto.LLMRespType_RESUME:
		s.SetAsrResume()
	case proto.LLMRespType_NORMAL:
	}
}
func (s *DollController) CheckLlmCmd(response *proto.LLMResponse) {
	switch response.Type {
	case proto.LLMRespType_NORMAL:
		s.LlmState.ProcessLLMResponse(response)
	}
}

func (s *DollController) TakeAFrame() rtcengine.IAudioFrame {
	bytes := s.voiceFrameModel.NextMixedFrame()
	if bytes == nil {
		return nil
	}
	return rtcengine.BuildAudioFrame(rtcengine.AudioFrameBuilder{
		SampleRate: s.voiceFrameModel.TargetFormat.SampleRate,
		Channel:    s.voiceFrameModel.TargetFormat.Channels,
		Data:       bytes,
	})
}

func (s *DollController) SetLLMResetInterruptIndex(index int32) {
	s.voiceFrameModel.SetLLMResetInterruptIndex(index)

	// err := s.GrpcClient.SendTTSText("", index, 0, s.DollInfo.TimbreId)
	// if err != nil {
	// 	logger.Error("TTS RESET 请求失败", zap.Error(err))
	// }
	logger.Info("RtcState LLM Reset 打断",
		zap.Int("index", int(index)))
}
func (s *DollController) SetPressResetInterruptIndexForceMax() {
	s.ForceStopToolCall()
	s.SetLLMResetInterruptIndex(s.voiceFrameModel.ResetInterruptIndex + 1)
	logger.Info("RtcState 按键强制 打断",
		zap.Int("index", int(s.voiceFrameModel.ResetInterruptIndex+1)))
}

func (s *DollController) SetAsrInterrupt() error {
	s.voiceFrameModel.SetAsrInterrupt()
	return nil
}

func (s *DollController) SetAsrResume() error {
	s.voiceFrameModel.ResumeAsrInterrupt()
	return nil
}

func (s *DollController) ForceStopToolCall() {
	source := s.voiceFrameModel.MainQueue.GetCurrentSource()
	if source == nil {
		return
	}
	if source.GetFromLLMToolType() == types.LLMToolStateType_PLAY_MUSIC ||
		source.GetFromLLMToolType() == types.LLMToolStateType_TELL_STORY ||
		source.GetFromLLMToolType() == types.LLMToolStateType_PLAY_AUDIO {
		source.SetDone()

		logger.Info("RtcState OnLLMStopToolCall. 当前VoiceFrameSource Discarded", zap.Any("source", source))
	}
}

func (s *DollController) handleReceiveRtcAudioFrame(audioFrame rtcengine.IAudioFrame) {
	if s.groupChatTransport != nil { // TODO: 考虑锁,避免并发问题
		s.groupChatTransport.SendBytes(audioFrame.Data())
	} else {
		if err := s.grpcNetHandler.OnAudioFrame(audioFrame); err != nil {
			logger.Error("DollController handleReceiveRtcAudioFrame 音频帧接收器错误", zap.Error(err))
		}
	}
}

func (s *DollController) OnTcpDiscoverFriend(cur *tcp.DollDiscoverFriendRequest) error {
	sendLLMResult := func(friendId string) {
		list := []string{}
		if friendId != "" {
			list = append(list, friendId)
		}
		s.GrpcClient.SendLLMMessage("", true, &map[string]string{
			string(types.LLMToolStateType_ADD_FRIEND_RESULT): utils.ToJsonIgnoreError(map[string]interface{}{
				"friend_dollId_list": list,
			}),
		})
	}
	if cur.Code != 0 {
		logger.Error("娃娃添加好友失败", zap.Any("response", cur))
		sendLLMResult("")
		return nil
	}
	if cur.Data.FriendId == "" {
		logger.Error("娃娃添加好友失败,friendId为空", zap.Any("response", cur))
		sendLLMResult("")
		return nil
	}
	friendId := cur.Data.FriendId

	response, err := service.GetMediaServiceInstance().RequestAddFriend(s.ctx, s.DollInfo.DollId, friendId)
	if err != nil || response.Code != 0 {
		logger.Error("添加好友失败", zap.Error(err), zap.String("message", response.Message))
		sendLLMResult("")
		return err
	}
	logger.Info("添加好友响应", zap.Any("response", response))

	if response.Data.IsSuccess {
		sendLLMResult(friendId)
		logger.Info("添加好友成功 isSuccess(true)", zap.String("friendId", friendId))
	} else {
		sendLLMResult("")
		logger.Error("添加好友失败 isSuccess(false)", zap.String("friendId", friendId))
	}
	audioBytes, err := service.DownloadFile(s.ctx, response.Data.Audio, map[string]string{}, 10, "")
	if err != nil {
		logger.Error("请求好友音频数据失败", zap.Error(err), zap.String("audioUrl", response.Data.Audio))
		return err
	}
	sampleRate := constant.RtcPushAudioSampleRate
	rtcPCM, err := audio.ProcessAudioToPCM(audioBytes, audio.AudioPCMFormat{
		SampleRate: sampleRate,
		Channels:   constant.AudioChannel,
		BitDepth:   constant.SampleBitDepth,
	})
	if err != nil {
		logger.Error("添加好友,音频数据转换失败", zap.Error(err))
	}
	source := s.voiceFrameModel.AddSourceMain(voicesource.NewBaseSourceFromOneAudio(rtcPCM, voicesource.SourceType_Base, s.voiceFrameModel.TargetFormat))
	source.SetFromLLMToolType(types.LLMToolStateType_ADD_FRIEND_RESULT)
	return nil
}

func (s *DollController) IsCallProcessing() bool {
	callCurrentState := s.friendsVoiceChatFSM.GetCurrentState()
	if callCurrentState != nil && callCurrentState.Name() == friendsvoicechat.FriendsVoiceChatFSMStateType_CallProcessing {
		return true
	}
	return false
}

func (s *DollController) SendLLMRequestStartupWord() error {
	m := &map[string]string{
		string(types.LLMToolStateType_REQ_STARTUP_WORD): string(s.DollInfo.StartupWord),
	}
	logger.Info("发送LLM欢迎请求", zap.Any("startupWord", m))
	err := s.GrpcClient.SendLLMMessage("", true, m)
	if err != nil {
		logger.Error("发送LLM请求 startup_word 失败", zap.Error(err))
		return err
	}
	return nil
}
