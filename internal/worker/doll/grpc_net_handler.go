package doll

import (
	"aigc_server/pkg/logger"
	proto "aigc_server/pkg/proto"
	"aigc_server/pkg/utils"
	"bytedance/bytertc/rtcengine"
	"fmt"
	"time"

	"aigc_server/internal/service"
	"aigc_server/internal/worker/llm"
	voicesource "aigc_server/internal/worker/model/voice_source"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type AudioFrameBuffer struct {
	data      []byte
	timestamp time.Time
}

type statics struct {
	asrInterruptStartAt int64
	asrFirstFrameAt     int64
	llmFirstFrameAt     int64
	ttsFirstFrameAt     int64
	allAverageLatency   int64
	count               int64
	words               string
}

type GrpcNetHandler struct {
	service.IGRPCClientHandler
	uid           string
	ctx           context.Context
	grpcClient    *service.GRPCClient
	frameBuffer   []AudioFrameBuffer
	bufferSize    int
	bufferTimeout time.Duration
	lastSendTime  time.Time

	debugSaveTts<PERSON>han chan []byte

	dollState *DollController

	llmMessageTransmitter *llm.LLMMessageTransmitter

	statics statics
}

func NewGrpcNetHandler(dollState *DollController) *GrpcNetHandler {
	return &GrpcNetHandler{
		uid:                   dollState.DollInfo.DollId,
		ctx:                   dollState.ctx,
		grpcClient:            dollState.GrpcClient,
		frameBuffer:           make([]AudioFrameBuffer, 0),
		bufferSize:            3200,                   // bytes threshold
		bufferTimeout:         500 * time.Millisecond, // timeout
		lastSendTime:          time.Now(),
		dollState:             dollState,
		llmMessageTransmitter: llm.NewLLMMessageTransmitter(dollState.ctx, dollState.Cfg, dollState.DollInfo.DollId),
	}
}

func (h *GrpcNetHandler) OnAudioFrame(audioFrame rtcengine.IAudioFrame) error {
	// return h.dollState.OnAudioFrame(audioFrame)

	// TODO: 处理音频帧
	frame := audioFrame
	data := frame.Data()

	sampleRate := frame.SampleRate()
	channel := frame.Channel()
	logger.Debug("网络处理器：收到音频帧", zap.String("uid", h.uid), zap.Int("sampleRate", sampleRate), zap.Int("channel", channel))

	// Add frame to buffer
	h.frameBuffer = append(h.frameBuffer, AudioFrameBuffer{
		data:      data,
		timestamp: time.Now(),
	})

	// Calculate total buffer size
	totalSize := 0
	timeoutReached := false
	now := time.Now()

	for _, buf := range h.frameBuffer {
		totalSize += len(buf.data)
		// Check if any frame has timed out
		if now.Sub(buf.timestamp) >= h.bufferTimeout {
			timeoutReached = true
			break
		}
	}
	// Send if buffer size threshold reached or timeout occurred
	if totalSize >= h.bufferSize || timeoutReached || now.Sub(h.lastSendTime) >= h.bufferTimeout {
		if len(h.frameBuffer) > 0 {
			// Merge all frames data
			mergedData := make([]byte, 0, totalSize)
			for _, buf := range h.frameBuffer {
				mergedData = append(mergedData, buf.data...)
			}

			// Send merged data
			err := h.grpcClient.SendASRAudio(mergedData)
			if err != nil {
				logger.Error("发送ASR请求失败", zap.Error(err))
				return err
			}
			logger.Debug("发送ASR请求", zap.String("uid", h.uid), zap.Int("frames", len(h.frameBuffer)), zap.Int("length", len(mergedData)))

			// Clear buffer
			h.frameBuffer = h.frameBuffer[:0]
			h.lastSendTime = now
		}
	}

	return nil
}

func (h *GrpcNetHandler) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	// return h.dollState.OnAsrResponse(response)
	logger.Debug("收到ASR响应", zap.String("uid", h.uid), zap.String("text", response.Text), zap.Bool("is_final", response.IsFinal))
	// check params
	if response.Text == "" { // ASR响应为空 说明有错误
		logger.Error("ASR响应为空 说明有错误", zap.String("uid", h.uid))
		return nil
	}
	h.dollState.SetAsrInterrupt()
	if !response.IsFinal {
		h.statics.asrInterruptStartAt = time.Now().UnixMilli()
		return nil
	}
	if h.statics.asrFirstFrameAt <= h.statics.asrInterruptStartAt {
		h.statics.asrFirstFrameAt = time.Now().UnixMilli()
		h.statics.words = response.Text
	}

	// source := h.dollState.voiceFrameModel.MainQueue.GetCurrentSource()
	// reqToolCall := map[string]string{}
	// if source != nil {
	// 	reqToolCall["ClientToolCallStatus"] = string(source.GetFromLLMToolType())
	// }

	if h.dollState.IsCallProcessing() {
		logger.Info("正在通话中,跳过LLM请求", zap.Any("AsrResponse", response))
		return nil
	}

	err := h.grpcClient.SendLLMMessage(response.Text, response.IsFinal, nil)
	if err != nil {
		logger.Error("发送LLM请求失败", zap.Error(err))
		return err
	}
	h.llmMessageTransmitter.OnLlmRequest(response)

	logger.Debug("发送LLM请求", zap.String("uid", h.uid), zap.String("content", response.Text), zap.Bool("is_final", response.IsFinal))
	return nil
}

func (h *GrpcNetHandler) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	// return h.dollState.OnLlmResponse(response)
	logger.Debug("收到LLM响应", zap.String("uid", h.uid), zap.String("toString", response.String()))
	if response.Type == proto.LLMRespType_RESET || response.Type == proto.LLMRespType_RESUME {
		logger.Info("LLM Type Reset/Resume", zap.Any("LLMResponse", response))
	}

	h.dollState.CheckLLMResetOrResume(response)
	h.llmMessageTransmitter.OnLlmResponse(response)
	if response.Type == proto.LLMRespType_NORMAL && response.Content != "" && response.Content != "[NoResponse]" {
		if h.statics.asrFirstFrameAt > 0 && h.statics.llmFirstFrameAt <= h.statics.asrFirstFrameAt {
			h.statics.llmFirstFrameAt = time.Now().UnixMilli()
			h.statics.words += fmt.Sprintf("=>%s", response.Content)
		}

		timbreId := h.dollState.LlmState.LLMUsingTimbreId
		if timbreId == "" {
			timbreId = h.dollState.DollInfo.TimbreId
		}
		err := h.grpcClient.SendTTSText(response.Content, response.MsgIndex, response.MsgSegmentIndex, timbreId)
		if err != nil {
			logger.Error("TTS请求失败", zap.Error(err))
			return err
		}
		source := voicesource.NewTtsSource(response.MsgIndex, response.MsgSegmentIndex)
		h.dollState.voiceFrameModel.AddSourceMain(source)
		logger.Debug("TTS请求成功", zap.String("uid", h.uid), zap.String("content", response.Content))
	}
	h.dollState.CheckLlmCmd(response)

	return nil
}

func (h *GrpcNetHandler) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	// return h.dollState.OnTtsResponse(response)
	if response.ErrorCode != 0 {
		logger.Error("TTS响应错误", zap.Int32("error_code", response.ErrorCode), zap.Any("TTS Response", response))
		return nil
	}
	if response.Audio == nil {
		return nil
	}
	if len(response.Audio) == 0 {
		return nil
	}
	logger.Debug("收到TTS响应", zap.String("uid", h.uid), zap.Int("audio_size", len(response.Audio)), zap.Int32("msg_index", response.MsgIndex))

	if h.dollState.Cfg.Debug.SaveAudio {
		if h.debugSaveTtsChan == nil {
			h.debugSaveTtsChan = utils.ContinueWriteAppendData(h.ctx, utils.GetTtsRecvSaveFilePath(h.uid))
		}
		if h.debugSaveTtsChan != nil {
			select {
			case h.debugSaveTtsChan <- response.Audio:
			default:
				logger.Warn("RTCService ContinueWriteAppendData debugSaveTtsChan已满，丢弃音频帧")
			}
		}
	}

	err := h.dollState.OnTTSResult(response.Audio, response.MsgIndex, response.MsgSegmentIndex)
	if err != nil {
		logger.Error("TTS结果处理失败", zap.Error(err))
		return err
	}
	if h.statics.asrFirstFrameAt > 0 && h.statics.ttsFirstFrameAt <= h.statics.llmFirstFrameAt {
		h.statics.ttsFirstFrameAt = time.Now().UnixMilli()
		asrToLlm := h.statics.llmFirstFrameAt - h.statics.asrFirstFrameAt
		llmToTts := h.statics.ttsFirstFrameAt - h.statics.llmFirstFrameAt
		asrToTts := h.statics.ttsFirstFrameAt - h.statics.asrFirstFrameAt
		h.statics.allAverageLatency = (h.statics.allAverageLatency*h.statics.count + asrToTts) / (h.statics.count + 1)
		h.statics.count++

		logger.Info("响应时间统计", zap.String("uid", h.uid), zap.String("words", h.statics.words), zap.Int64("asrFirstFrameAt", h.statics.asrFirstFrameAt), zap.Int64("asrToLlm", asrToLlm), zap.Int64("llmToTts", llmToTts), zap.Int64("asrToTts", asrToTts), zap.Int64("allAverageLatency", h.statics.allAverageLatency), zap.Int64("count", h.statics.count))
	}
	return nil
}
