package friendsvoicechat

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"encoding/json"

	"go.uber.org/zap"
)

type FriendsVoiceChatFSMState_IdleForChat struct {
	IFriendsVoiceChatFSMState
	fsm *FriendsVoiceChatFSM
}

func NewFriendsVoiceChatFSMState_IdleForChat(fsm *FriendsVoiceChatFSM) *FriendsVoiceChatFSMState_IdleForChat {
	return &FriendsVoiceChatFSMState_IdleForChat{
		fsm: fsm,
	}
}

func (state *FriendsVoiceChatFSMState_IdleForChat) Name() FriendsVoiceChatFSMStateType {
	return FriendsVoiceChatFSMStateType_IdleForChat
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnEnter(oldState IFriendsVoiceChatFSMState) {
	service.GetEventEmitter().On(types.EventType_Calling_Friend_Notify, state.OnCallingFriendNotify, state)
	service.GetEventEmitter().On(types.EventType_Call_By_Friend_Cross_Process_REQ, state.OnCallByFriendRequest, state)
	state.fsm.Data.CallInfo = &FriendsVoiceChatCallingInfo{}
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnExit(newState IFriendsVoiceChatFSMState) {
	service.GetEventEmitter().OffByOwner(state)
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnTimeout() {
	logger.Info("FriendsVoiceChatFSMState_IdleForChat:OnTimeout")
	// state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnCallingFriendNotify(event types.EventType, data interface{}) {
	logger.Info("FriendsVoiceChatFSMState_IdleForChat:OnCallingFriendNotify", zap.Any("event", event), zap.Any("data", data))

	type CallingFriendRequestInfo struct {
		// SrcDollId  string `json:"srcDollId"`
		DestDollId string `json:"destDollId"`
		State      string `json:"state"`
	}

	callInfo, result := func() (*CallingFriendRequestInfo, string) {
		content, ok := data.(string)
		if !ok {
			logger.Error("呼叫朋友请求解析失败,data不是string")
			return nil, "Reject"
		}
		callInfo := CallingFriendRequestInfo{}
		err := json.Unmarshal([]byte(content), &callInfo)
		if err != nil {
			logger.Error("呼叫朋友请求解析失败", zap.Error(err))
			return nil, "Reject"
		}
		if callInfo.DestDollId == "" {
			logger.Error("呼叫朋友请求解析失败,to_doll不能为空")
			return nil, "Reject"
		}
		logger.Info("呼叫朋友请求解析成功", zap.Any("callInfo", callInfo))
		return &callInfo, "Accept"
	}()
	if result == "Accept" {
		state.fsm.Data.CallInfo = &FriendsVoiceChatCallingInfo{
			Caller1Id: state.fsm.DollId,
			Callee2Id: callInfo.DestDollId,
		}

		logger.Info("跨进程呼叫好友, 发送跨进程呼叫好友请求", zap.Any("callInfo", state.fsm.Data.CallInfo))
		ipc.IpcRequestInstance.DoCrossProcessEmitEvent(state.fsm.ctx, callInfo.DestDollId,
			types.EventType_Call_By_Friend_Cross_Process_REQ,
			utils.ToJsonIgnoreError(state.fsm.Data.CallInfo))
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_CallingFriend(state.fsm))
	} else {
		logger.Info("发送LLMToolState,呼叫朋友响应失败", zap.String("state", result))
		state.fsm.GrpcClient.SendLLMMessage("", true, &map[string]string{
			string(types.LLMToolStateType_CALLING_FRIEND_RESULT): utils.ToJsonIgnoreError(&map[string]string{
				"destDollId": callInfo.DestDollId,
				"state":      result,
			}),
		})
	}
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnCallByFriendErrorHappen(callerId string, err error) {
	state.fsm.Data.CallInfo.Error = err.Error()
	ipc.IpcRequestInstance.DoCrossProcessEmitEvent(state.fsm.ctx, callerId,
		types.EventType_Calling_Friend_Cross_Process_Result,
		utils.ToJsonIgnoreError(state.fsm.Data.CallInfo))
	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_IdleForChat) OnCallByFriendRequest(event types.EventType, data interface{}) {
	logger.Info("FriendsVoiceChatFSMState_IdleForChat:OnCallByFriendRequest", zap.Any("event", event), zap.Any("data", data))
	callInfo := &FriendsVoiceChatCallingInfo{}
	err := json.Unmarshal([]byte(data.(string)), callInfo)
	if err != nil {
		logger.Error("呼叫朋友请求解析失败", zap.Error(err))
		return
	}
	state.fsm.Data.CallInfo = callInfo

	err = state.fsm.GrpcClient.SendLLMMessage("", true, &map[string]string{
		string(types.LLMToolStateType_CALL_BY_FRIEND_REQ): utils.ToJsonIgnoreError(map[string]string{
			"srcDollId": callInfo.Caller1Id,
		}),
	})
	if err != nil {
		logger.Error("发送LLM消息失败 Call By Friend Request", zap.Error(err))
		state.OnCallByFriendErrorHappen(callInfo.Caller1Id, err)
		return
	}

	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_CallByFriend(state.fsm))
}
