package friendsvoicechat

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"time"

	"go.uber.org/zap"
)

type FriendsVoiceChatFSMState_CallProcessing struct {
	IFriendsVoiceChatFSMState
	fsm                  *FriendsVoiceChatFSM
	checkGroupChatCtx    context.Context
	checkGroupChatCancel context.CancelFunc
}

func NewFriendsVoiceChatFSMState_CallProcessing(fsm *FriendsVoiceChatFSM) *FriendsVoiceChatFSMState_CallProcessing {
	ctx, cancel := context.WithCancel(fsm.ctx)
	return &FriendsVoiceChatFSMState_CallProcessing{
		fsm:                  fsm,
		checkGroupChatCtx:    ctx,
		checkGroupChatCancel: cancel,
	}
}

func (state *FriendsVoiceChatFSMState_CallProcessing) Name() FriendsVoiceChatFSMStateType {
	return FriendsVoiceChatFSMStateType_CallProcessing
}

func (state *FriendsVoiceChatFSMState_CallProcessing) OnEnter(oldState IFriendsVoiceChatFSMState) {
	service.GetEventEmitter().On(types.EventType_Call_Friend_Over, state.OnCallFriendOver, state)
	service.GetEventEmitter().On(types.EventType_Call_Friend_Other_Over, state.OnCallFriendOver, state)
	state.StartCallProcessing()
	go state.CheckGroupChat(state.checkGroupChatCtx)
}

func (state *FriendsVoiceChatFSMState_CallProcessing) OnExit(newState IFriendsVoiceChatFSMState) {
	service.GetEventEmitter().OffByOwner(state)
	state.checkGroupChatCancel()
	state.StopGroupChat()
}

func (state *FriendsVoiceChatFSMState_CallProcessing) OnTimeout() {
	logger.Info("FriendsVoiceChatFSMState_CallProcessing:OnTimeout")
	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_CallProcessing) StartCallProcessing() {
	if state.fsm.DollId == state.fsm.Data.CallInfo.Caller1Id {
		state.StartGroupChat()
		state.RequestStartCall()
	}
}

func (state *FriendsVoiceChatFSMState_CallProcessing) OnCallFriendOver(event types.EventType, data interface{}) {
	logger.Info("跨进程呼叫好友 挂断", zap.Any("event", event), zap.Any("data", data))
	if event == types.EventType_Call_Friend_Over {
		state.RequestEndCall()
		otherDollId := state.fsm.Data.CallInfo.Caller1Id
		if state.fsm.DollId == otherDollId {
			otherDollId = state.fsm.Data.CallInfo.Callee2Id
		}
		ipc.IpcRequestInstance.DoCrossProcessEmitEvent(state.fsm.ctx, otherDollId,
			types.EventType_Call_Friend_Other_Over,
			data.(string),
		)
	}
	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_CallProcessing) RequestStartCall() {
	now := time.Now()
	state.fsm.Data.CallInfo.StartAt = now.Unix()

	str := now.Format(time.RFC3339)
	service.GetMediaServiceInstance().StartCall(state.fsm.ctx, state.fsm.Data.CallInfo.Caller1Id, state.fsm.Data.CallInfo.Callee2Id, str)
}
func (state *FriendsVoiceChatFSMState_CallProcessing) RequestEndCall() {
	now := time.Now()
	state.fsm.Data.CallInfo.OverAt = now.Unix()
	state.fsm.Data.CallInfo.OverByDollId = state.fsm.DollId

	service.GetMediaServiceInstance().EndCall(state.fsm.ctx, state.fsm.Data.CallInfo.Caller1Id, state.fsm.Data.CallInfo.Callee2Id,
		time.Unix(state.fsm.Data.CallInfo.StartAt, 0).Format(time.RFC3339),
		int(now.Sub(time.Unix(state.fsm.Data.CallInfo.StartAt, 0)).Seconds()))
}

func (state *FriendsVoiceChatFSMState_CallProcessing) StartGroupChat() {
	caller1Id := state.fsm.Data.CallInfo.Caller1Id
	callee2Id := state.fsm.Data.CallInfo.Callee2Id
	teamId, err := service.GroupChatServiceInstance.CreateTeam(state.fsm.ctx, []string{caller1Id, callee2Id}, service.GroupChatTeamType_FriendsVoiceChat)
	if err != nil {
		logger.Error("创建群组失败", zap.Error(err))
		return
	}
	state.fsm.Data.CallInfo.TeamId = teamId

	logger.Info("语音聊天,创建群组成功", zap.String("teamId", teamId), zap.String("caller1Id", caller1Id), zap.String("callee2Id", callee2Id))
}

func (state *FriendsVoiceChatFSMState_CallProcessing) StopGroupChat() {
	teamId, err := service.GroupChatServiceInstance.FindTeam(state.fsm.ctx, state.fsm.DollId)
	if err != nil || teamId == "" {
		logger.Error("跨进程呼叫好友 挂断,查找群组失败", zap.String("dollId", state.fsm.DollId), zap.Error(err))
		return
	}
	service.GroupChatServiceInstance.RemoveTeam(state.fsm.ctx, teamId)
	logger.Info("跨进程呼叫好友 挂断,移除群组成功", zap.String("teamId", teamId), zap.String("dollId", state.fsm.DollId))
}

func (state *FriendsVoiceChatFSMState_CallProcessing) CheckGroupChat(ctx context.Context) {
	defer utils.TraceRecover()
	time.Sleep(time.Second * 4)
	ticker := time.Tick(time.Millisecond * 300)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker:
			{
				teamId, err := service.GroupChatServiceInstance.FindTeam(state.fsm.ctx, state.fsm.DollId)
				if err != nil || teamId == "" {
					logger.Error("跨进程呼叫好友 未找到群组,挂断", zap.String("dollId", state.fsm.DollId), zap.Error(err))
					state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
					return
				}
			}
		}
	}
}
