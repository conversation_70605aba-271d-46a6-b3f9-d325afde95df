package friendsvoicechat

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"encoding/json"
	"errors"
	"time"

	"go.uber.org/zap"
)

type FriendsVoiceChatFSMState_CallingFriend struct {
	IFriendsVoiceChatFSMState
	fsm           *FriendsVoiceChatFSM
	timeoutCancel context.CancelFunc
}

func NewFriendsVoiceChatFSMState_CallingFriend(fsm *FriendsVoiceChatFSM) *FriendsVoiceChatFSMState_CallingFriend {
	return &FriendsVoiceChatFSMState_CallingFriend{
		fsm: fsm,
	}
}

func (state *FriendsVoiceChatFSMState_CallingFriend) Name() FriendsVoiceChatFSMStateType {
	return FriendsVoiceChatFSMStateType_CallingFriend
}

func (state *FriendsVoiceChatFSMState_CallingFriend) OnEnter(oldState IFriendsVoiceChatFSMState) {
	dollStatus := model.NewDollStatus(state.fsm.ctx, state.fsm.DollId)
	dollStatus.SetState(model.DollStateTypeVoiceChatCalling)

	service.GetEventEmitter().On(types.EventType_Calling_Friend_Cross_Process_Result, state.OnCallingFriendResult, state)
	state.timeoutCancel = state.fsm.SetStateTimeout(state, 30*time.Second)
}

func (state *FriendsVoiceChatFSMState_CallingFriend) OnExit(newState IFriendsVoiceChatFSMState) {
	dollStatus := model.NewDollStatus(state.fsm.ctx, state.fsm.DollId)
	dollStatus.SetState(model.DollStateTypeIdle)
	service.GetEventEmitter().OffByOwner(state)
	state.timeoutCancel()
}

func (state *FriendsVoiceChatFSMState_CallingFriend) OnTimeout() {
	logger.Info("FriendsVoiceChatFSMState_CallingFriend:OnTimeout")
	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_CallingFriend) OnCallingFriendResult(event types.EventType, data interface{}) {
	logger.Info("FriendsVoiceChatFSMState_CallingFriend:OnCallingFriendResult", zap.Any("event", event), zap.Any("data", data))
	callInfo := &FriendsVoiceChatCallingInfo{}
	err := json.Unmarshal([]byte(data.(string)), callInfo)
	if err != nil {
		logger.Error("呼叫朋友请求解析失败", zap.Error(err))
		return
	}
	state.fsm.Data.CallInfo = callInfo
	if callInfo.Error != "" {
		logger.Error("接收跨进程呼叫朋友结果,收到错误", zap.Error(errors.New(callInfo.Error)))
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
		return
	}
	result := "Reject"
	if callInfo.IsAccepted {
		result = "Accept"
	}
	state.fsm.GrpcClient.SendLLMMessage("", true, &map[string]string{
		string(types.LLMToolStateType_CALLING_FRIEND_RESULT): utils.ToJsonIgnoreError(map[string]string{
			"destDollId": state.fsm.Data.CallInfo.Callee2Id,
			"state":      result,
		}),
	})
	if callInfo.IsAccepted {
		logger.Info("呼叫朋友对方接受", zap.Any("callInfo", callInfo))
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_CallProcessing(state.fsm))
	} else {
		logger.Info("呼叫朋友对方拒接", zap.Any("callInfo", callInfo))
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
	}
}
