package friendsvoicechat

import (
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"time"

	"go.uber.org/zap"
)

type FriendsVoiceChatFSMStateType string

const (
	FriendsVoiceChatFSMStateType_IdleForChat    FriendsVoiceChatFSMStateType = "IdleForChat"
	FriendsVoiceChatFSMStateType_CallingFriend  FriendsVoiceChatFSMStateType = "CallingFriend"
	FriendsVoiceChatFSMStateType_CallByFriend   FriendsVoiceChatFSMStateType = "CallByFriend"
	FriendsVoiceChatFSMStateType_CallProcessing FriendsVoiceChatFSMStateType = "CallProcessing"
)

type IFriendsVoiceChatFSMState interface {
	Name() FriendsVoiceChatFSMStateType
	OnEnter(oldState IFriendsVoiceChatFSMState)
	OnExit(newState IFriendsVoiceChatFSMState)
	OnTimeout()
}

type FriendsVoiceChatCallingInfo struct {
	Caller1Id    string `json:"caller1_id"`
	Callee2Id    string `json:"callee2_id"`
	TeamId       string `json:"team_id"`
	IsAccepted   bool   `json:"is_accepted"`
	StartAt      int64  `json:"start_at"`
	OverAt       int64  `json:"over_at"`
	OverByDollId string `json:"over_by_doll_id"`
	Error        string `json:"error"`
}
type FriendsVoiceChatData struct {
	CallInfo *FriendsVoiceChatCallingInfo `json:"callInfo"`
}

type FriendsVoiceChatFSM struct {
	ctx        context.Context
	State      IFriendsVoiceChatFSMState
	Data       *FriendsVoiceChatData
	DollId     string
	GrpcClient *service.GRPCClient
}

func NewFriendsVoiceChatFSM(ctx context.Context, dollId string, grpcClient *service.GRPCClient) *FriendsVoiceChatFSM {
	fsm := &FriendsVoiceChatFSM{
		ctx:   ctx,
		State: nil,
		Data: &FriendsVoiceChatData{
			CallInfo: &FriendsVoiceChatCallingInfo{},
		},
		DollId:     dollId,
		GrpcClient: grpcClient,
	}
	fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(fsm))
	return fsm
}

func (fsm *FriendsVoiceChatFSM) Stop() {
	fsm.ChangeState(nil)
	logger.Info("FriendsVoiceChatFSM:Stop")
}

func (fsm *FriendsVoiceChatFSM) ChangeState(newState IFriendsVoiceChatFSMState) {
	if fsm.State != nil {
		logger.Info("FriendsVoiceChatFSM:ChangeState:OnExit", zap.Any("oldState", fsm.State.Name()))
		fsm.State.OnExit(newState)
	}
	oldState := fsm.State
	fsm.State = newState
	if fsm.State != nil {
		logger.Info("FriendsVoiceChatFSM:ChangeState:OnEnter", zap.Any("newState", fsm.State.Name()))
		fsm.State.OnEnter(oldState)
	}
}

func (fsm *FriendsVoiceChatFSM) SetStateTimeout(state IFriendsVoiceChatFSMState, timeout time.Duration) context.CancelFunc {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	isTimeout := true
	cancelWithoutTimeout := func() {
		isTimeout = false
		if cancel != nil {
			cancel()
		}
	}
	go func() {
		defer utils.TraceRecover()
		select {
		case <-fsm.ctx.Done():
			cancelWithoutTimeout()
		case <-ctx.Done():
			if isTimeout {
				state.OnTimeout()
			}
		}
	}()
	return cancelWithoutTimeout
}

func (fsm *FriendsVoiceChatFSM) GetCurrentState() IFriendsVoiceChatFSMState {
	return fsm.State
}

// func (fsm *FriendsVoiceChatFSM) Loop() {
// 	defer utils.TraceRecover()
// 	fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(fsm))
// 	defer func() {
// 		fsm.ChangeState(nil)
// 		logger.Info("FriendsVoiceChatFSM:Loop:Done")
// 	}()
// 	for {
// 		select {
// 		case <-fsm.ctx.Done():
// 			return
// 		default:
// 			if fsm.State != nil {
// 				fsm.State.OnUpdate()
// 			}
// 			time.Sleep(100 * time.Millisecond)
// 		}
// 	}
// }
