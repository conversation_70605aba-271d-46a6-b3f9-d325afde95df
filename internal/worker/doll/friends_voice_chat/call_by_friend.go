package friendsvoicechat

import (
	"aigc_server/internal/service"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"
)

type FriendsVoiceChatFSMState_CallByFriend struct {
	IFriendsVoiceChatFSMState
	fsm           *FriendsVoiceChatFSM
	timeoutCancel context.CancelFunc
}

func NewFriendsVoiceChatFSMState_CallByFriend(fsm *FriendsVoiceChatFSM) *FriendsVoiceChatFSMState_CallByFriend {
	return &FriendsVoiceChatFSMState_CallByFriend{
		fsm: fsm,
	}
}

func (state *FriendsVoiceChatFSMState_CallByFriend) Name() FriendsVoiceChatFSMStateType {
	return FriendsVoiceChatFSMStateType_CallByFriend
}

func (state *FriendsVoiceChatFSMState_CallByFriend) OnEnter(oldState IFriendsVoiceChatFSMState) {
	dollStatus := model.NewDollStatus(state.fsm.ctx, state.fsm.DollId)
	dollStatus.SetState(model.DollStateTypeVoiceChatCallByOther)
	service.GetEventEmitter().On(types.EventType_Call_By_Friend_LLM_RESP, state.OnCallByFriendLLMResp, state)
	state.timeoutCancel = state.fsm.SetStateTimeout(state, 30*time.Second)
}

func (state *FriendsVoiceChatFSMState_CallByFriend) OnExit(newState IFriendsVoiceChatFSMState) {
	dollStatus := model.NewDollStatus(state.fsm.ctx, state.fsm.DollId)
	dollStatus.SetState(model.DollStateTypeIdle)
	service.GetEventEmitter().OffByOwner(state)
	state.timeoutCancel()
}

func (state *FriendsVoiceChatFSMState_CallByFriend) OnTimeout() {
	logger.Info("FriendsVoiceChatFSMState_CallByFriend:OnTimeout")
	state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
}

func (state *FriendsVoiceChatFSMState_CallByFriend) OnCallByFriendLLMResp(event types.EventType, data interface{}) {
	logger.Info("FriendsVoiceChatFSMState_CallByFriend:OnCallByFriendLLMResp", zap.Any("event", event), zap.Any("data", data))
	llmInfo := map[string]string{}
	err := json.Unmarshal([]byte(data.(string)), &llmInfo)
	if err != nil {
		logger.Error("LLM响应被呼叫请求解析失败", zap.Error(err))
		return
	}
	caller1Id := llmInfo["srcDollId"]
	result := llmInfo["state"]
	if caller1Id == "" {
		logger.Error("LLM响应被呼叫请求解析失败,srcDollId为空")
		return
	}
	if result == "" {
		logger.Error("LLM响应被呼叫请求解析失败,state为空")
		return
	}

	if result == "Accept" {
		state.fsm.Data.CallInfo.IsAccepted = true
	} else {
		state.fsm.Data.CallInfo.IsAccepted = false
	}
	ipc.IpcRequestInstance.DoCrossProcessEmitEvent(state.fsm.ctx,
		caller1Id,
		types.EventType_Calling_Friend_Cross_Process_Result,
		utils.ToJsonIgnoreError(state.fsm.Data.CallInfo))
	if result == "Accept" {
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_CallProcessing(state.fsm))
	} else {
		state.fsm.ChangeState(NewFriendsVoiceChatFSMState_IdleForChat(state.fsm))
	}
}
