package types

// 为了兼容性保留的旧类型定义
type LLMToolStateType string

const (
	LLMToolStateType_NO_TOOL               LLMToolStateType = "NoTool"
	LLMToolStateType_TELL_STORY            LLMToolStateType = "TellStory"
	LLMToolStateType_PLAY_MUSIC            LLMToolStateType = "PlayMusic"
	LLMToolStateType_PLAY_AUDIO            LLMToolStateType = "PlayAudio"
	LLMToolStateType_PLAY_MIX_AUDIO        LLMToolStateType = "PlayMixAudio"
	LLMToolStateType_VOLUME                LLMToolStateType = "Volume"
	LLMToolStateType_PLAY_GAME             LLMToolStateType = "PlayGame"
	LLMToolStateType_STOP_GAME             LLMToolStateType = "StopGame"
	LLMToolStateType_ADD_FRIEND            LLMToolStateType = "AddFriend"
	LLMToolStateType_ADD_FRIEND_RESULT     LLMToolStateType = "AddFriendResult"
	LLMToolStateType_CALLING_FRIEND_NOTIFY LLMToolStateType = "CallingFriendNotify"
	LLMToolStateType_CALLING_FRIEND_RESULT LLMToolStateType = "CallingFriendResult"
	LLMToolStateType_CALL_BY_FRIEND_REQ    LLMToolStateType = "CallByFriendRequest"
	LLMToolStateType_CALL_BY_FRIEND_RESP   LLMToolStateType = "CallByFriendResponse"
	LLMToolStateType_PLAY_BACKGROUND_MUSIC LLMToolStateType = "PlayBackgroundMusic"
	LLMToolStateType_STOP_BACKGROUND_MUSIC LLMToolStateType = "StopBackgroundMusic"
	LLMToolStateType_CHANGE_TTS_TIMBRE     LLMToolStateType = "ChangeTtsTimbre"
	LLMToolStateType_RESET_TTS_TIMBRE      LLMToolStateType = "ResetTtsTimbre"

	LLMToolStateType_REQ_DOLL_SENSOR_ACC LLMToolStateType = "DollSensorAcc"
	LLMToolStateType_REQ_STARTUP_WORD    LLMToolStateType = "StartupWord"
)
