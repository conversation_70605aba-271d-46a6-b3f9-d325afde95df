package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/doll"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env         = flag.String("env", "prod", "运行环境: prod")
	uid         = flag.String("uid", "", "用户ID")
	roomID      = flag.String("room_id", "", "房间ID")
	startupWord = flag.String("startup_word", "", "启动词")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"

	// IPC管理器
	ipcManager *ipc.IPCManager
)

func main() {
	flag.Parse()

	if *uid == "" {
		fmt.Println("请提供用户ID: --uid")
		os.Exit(1)
	}

	if *roomID == "" {
		fmt.Println("请提供房间ID: --room_id")
		os.Exit(1)
	}

	if *startupWord == "" {
		*startupWord = string(types.StartupWordType_Start)
	}

	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	if err := logger.Init(&logger.Config{
		Level:     cfg.Log.Level,
		Format:    cfg.Log.Format,
		Output:    cfg.Log.Output,
		FilePath:  cfg.Log.FilePath,
		LoggerID:  fmt.Sprintf("%s_%s", cfg.Server.Env, *roomID), // 使用用户RoomID作为日志文件ID
		IsMainLog: false,                                         // 子进程日志
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	runDollService(cfg, *uid, *roomID, types.StartupWordType(*startupWord))
}

// runDollService 运行doll服务
func runDollService(cfg *config.Config, dollId, roomID string, startupWord types.StartupWordType) error {
	defer utils.TraceRecover()
	logger.Info("doll worker服务启动中", zap.String("dollId", dollId), zap.String("roomID", roomID), zap.String("startupWord", string(startupWord)))
	defer func() {
		logger.Info("doll worker服务已停止")
	}()
	// 启动上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	// 设置信号处理
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("worker进程接收到信号", zap.String("signal", sig.String()))

		// 向主进程发送停止状态
		stopData := map[string]interface{}{
			"state":       "stopped",
			"uid":         dollId,
			"description": "Worker进程正在停止",
		}
		if err := ipcManager.SendMessage(context.Background(), ipc.GetIPCMainProcessID(), "status", stopData); err != nil {
			logger.Error("发送停止状态失败", zap.Error(err))
		}

		cancel()
	}()

	// 初始化Redis和IPC
	if err := myredis.Init(&cfg.Redis); err != nil {
		return fmt.Errorf("初始化Redis失败: %w", err)
	}
	defer myredis.Close()

	service.InitGroupChatService(ctx)

	// 创建worker进程IPC管理器
	workerProcessID := ipc.GetWorkerProcessID(dollId)
	ipcManager = ipc.NewIPCManager(workerProcessID)

	dollController, err := doll.NewDollControllerAndStart(ctx, cancel, cfg, model.DollCmdParams{
		DollId:      dollId,
		RoomId:      roomID,
		StartupWord: startupWord,
	})
	if err != nil {
		return fmt.Errorf("启动doll服务失败: %w", err)
	}
	dollController.SetIpcHandler(doll.NewDollIPCMessageHandler(dollController, ipcManager))
	ipcManager.Router.AddHandler(dollController.IpcHandler)
	defer ipcManager.Router.RemoveHandler(dollController.IpcHandler)

	// 启动IPC消息循环
	ipcManager.Start(ctx)
	defer ipcManager.Stop()

	runIPCStatusLoop(ctx, dollId, roomID)

	err = dollController.RoomLooping()
	if err != nil {
		logger.Error("doll worker服务运行失败", zap.Error(err))
		return fmt.Errorf("doll worker服务运行失败: %w", err)
	}
	return nil
}
func runIPCStatusLoop(ctx context.Context, dollId, roomID string) {

	// 向主进程发送启动状态
	startData := map[string]interface{}{
		"state":       "starting",
		"uid":         dollId,
		"room_id":     roomID,
		"description": "Worker进程正在启动",
	}
	if err := ipcManager.SendMessage(ctx, ipc.GetIPCMainProcessID(), "status", startData); err != nil {
		logger.Error("发送启动状态失败", zap.Error(err))
	}

	logger.Info("worker进程启动",
		zap.String("uid", dollId),
		zap.String("room_id", roomID),
	)

	// 发送运行状态
	runningData := map[string]interface{}{
		"state":       "running",
		"uid":         dollId,
		"room_id":     roomID,
		"description": "Worker进程正在运行",
	}
	if err := ipcManager.SendMessage(ctx, ipc.GetIPCMainProcessID(), "status", runningData); err != nil {
		logger.Error("发送运行状态失败", zap.Error(err))
	}

	// 这里添加具体的worker业务逻辑
	logger.Info("worker进程业务逻辑开始执行")

	// 示例：定期发送心跳
	go func() {
		defer utils.TraceRecover()
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				heartbeatData := map[string]interface{}{
					"event":       "heartbeat",
					"uid":         *uid,
					"timestamp":   time.Now().Unix(),
					"description": "Worker进程心跳",
				}
				if err := ipcManager.SendMessage(ctx, ipc.GetIPCMainProcessID(), "event", heartbeatData); err != nil {
					logger.Error("发送心跳失败", zap.Error(err))
				}
			}
		}
	}()
}
