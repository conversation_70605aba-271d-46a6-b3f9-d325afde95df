package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/http"
	mainIPC "aigc_server/internal/main/ipc"
	"aigc_server/internal/main/process"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env = flag.String("env", "prod", "运行环境: prod")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"
	// IPC管理器
	ipcManager *ipc.IPCManager
)

func main() {
	flag.Parse()

	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	if err := logger.Init(&logger.Config{
		Level:     cfg.Log.Level,
		Format:    cfg.Log.Format,
		Output:    cfg.Log.Output,
		FilePath:  cfg.Log.FilePath,
		LoggerID:  fmt.Sprintf("%s_main", cfg.Server.Env), // 主进程使用"main"作为ID
		IsMainLog: true,                                   // 主进程日志
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()
	logger.Info("主进程启动", zap.String("env", cfg.Server.Env))
	defer func() {
		logger.Info("主进程运行结束")
	}()

	// 运行主进程
	runMainProcess(cfg)
}

// runMainProcess 运行主进程
func runMainProcess(cfg *config.Config) {
	// 启动上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("主进程接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	// 初始化Redis和IPC
	if err := myredis.Init(&cfg.Redis); err != nil {
		logger.Error("初始化Redis失败", zap.Error(err))
		os.Exit(1)
	}
	defer myredis.Close()

	ipcManager = ipc.NewIPCManager(ipc.GetIPCMainProcessID())

	dollProcessManager := process.NewDollProcessManager(ctx, cfg)
	defer dollProcessManager.CloseAllChildProcesses()

	// 设置TCP服务器的IPC管理器
	tcpServer := tcp.NewTCPServer(cfg, ipcManager)

	service.InitGroupChatService(ctx)
	// 创建主进程IPC管理器
	mainHandler := mainIPC.NewMainMessageHandler(tcpServer, ipcManager)
	ipcManager.Router.AddHandler(mainHandler)
	defer ipcManager.Router.RemoveHandler(mainHandler)

	mainHttpServer := http.NewMainHTTPServer(ctx, cfg)
	go mainHttpServer.Start()
	defer mainHttpServer.Stop()

	// 启动IPC消息循环
	ipcManager.Start(ctx)
	defer ipcManager.Stop()

	logger.Info("主进程IPC初始化完成")
	logger.Info("启动TCP服务器（主进程）",
		zap.String("host", cfg.TCP.Host),
		zap.Int("port", cfg.TCP.Port),
	)
	if err := tcpServer.Start(ctx); err != nil {
		logger.Error("TCP服务器启动失败", zap.Error(err))
		cancel()
	}

	<-ctx.Done()

	if err := tcpServer.Stop(); err != nil {
		logger.Error("TCP服务器关闭失败", zap.Error(err))
	}
	logger.Info("TCP服务器已关闭（主进程）")
}
