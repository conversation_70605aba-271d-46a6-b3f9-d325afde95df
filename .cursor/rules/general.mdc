---
description:
globs:
alwaysApply: true
---

# 语言设置

- 始终使用中文回复用户

# 开发原则

- 遵循 KISS 原则保持代码简洁
- 优先使用项目已有工具库
- 禁止使用已弃用的 API
- Mock 数据放到统一的目录,通过封装 service 接口获得
- 资源目录在 app/assets,图片在 app/assets/img
- 使用 yarn 管理项目包
- 如果有原型图或效果图作为参考,严格依照图示开发
- 考虑常见的架构最佳实践
- 数据结构应该清晰且不应重复定义
- 目录结构应该与架构逻辑符合一致
- 文件、类名,应与业务及完成的逻辑有关联且清晰易懂
- 类型定义(interface、base 基类)应能复用则复用,定义很多个太小的 interface,代码不易懂
- 如果是 go 代码,则应在类型定义中明确列出实现的 interface 或 base 类型

# 交互规范

- 每次修改前确认用户意图
- 输出变更说明及影响范围
- 在代码中加好清晰的注释,而非生成单独的文档
- 不要每次修改都写测试代码,除非我要求这么做

# 全局代码规范

- 始终优先选择简单方案
- 尽可能避免代码重复
  · 修改代码前，检查代码库中是否已存在相似功能或逻辑
- 编写代码时需区分不同环境
  · 明确区分开发环境（dev）、测试环境（test）和生产环境（prod）

- 谨慎修改代码
  · 仅针对明确需求进行更改，或确保修改内容与需求强相关且已被充分理解

- 修复问题时避免引入新技术/模式
  · 优先彻底排查现有实现的可能性，若必须引入新方案，需同步移除旧逻辑以避免冗余

- 保持代码库整洁有序

- 重构完成后,旧代码、无用代码需要清理干净
